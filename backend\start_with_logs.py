#!/usr/bin/env python3
"""
带详细日志的后端启动脚本
"""

import logging
import sys
import uvicorn
from pathlib import Path

# 配置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('backend.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def main():
    logger.info("=" * 50)
    logger.info("启动PPT Agent后端服务")
    logger.info("=" * 50)
    
    try:
        # 设置工作目录
        backend_dir = Path(__file__).parent
        logger.info(f"工作目录: {backend_dir}")
        
        # 启动uvicorn服务器
        logger.info("启动uvicorn服务器...")
        uvicorn.run(
            "app.main:app",
            host="127.0.0.1",
            port=9527,
            reload=True,
            log_level="debug",
            access_log=True,
            log_config={
                "version": 1,
                "disable_existing_loggers": False,
                "formatters": {
                    "default": {
                        "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                    },
                },
                "handlers": {
                    "default": {
                        "formatter": "default",
                        "class": "logging.StreamHandler",
                        "stream": "ext://sys.stdout",
                    },
                },
                "root": {
                    "level": "DEBUG",
                    "handlers": ["default"],
                },
            }
        )
        
    except Exception as e:
        logger.error(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
