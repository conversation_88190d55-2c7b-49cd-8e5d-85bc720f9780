"""
基于模板的内容生成器
根据模板结构要求和用户需求生成精确匹配的PPT内容
"""

import logging
import json
import sys
from pathlib import Path
from typing import Dict, List, Any

# 添加LLM客户端路径
llm_path = Path(__file__).parent.parent.parent / "llm"
if str(llm_path) not in sys.path:
    sys.path.insert(0, str(llm_path))

try:
    import llm_client_manager
    LLMClient = llm_client_manager.LLMClient
except ImportError as e:
    logging.error(f"无法导入LLM客户端: {e}")
    LLMClient = None

logger = logging.getLogger(__name__)


class TemplateBasedContentGenerator:
    """基于模板的内容生成器"""
    
    def __init__(self):
        if LLMClient:
            self.llm_client = LLMClient()
        else:
            self.llm_client = None
    
    def generate_content_for_template(self, user_request: str, template_requirements: Dict[str, Any], 
                                    template_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        基于模板要求生成内容
        
        Args:
            user_request: 用户需求描述
            template_requirements: 模板生成要求
            template_analysis: 模板分析结果
            
        Returns:
            生成的内容
        """
        try:
            logger.info(f"开始基于模板生成内容，用户需求: {user_request[:100]}...")
            
            # 构建详细的提示词
            prompt = self._build_detailed_prompt(user_request, template_requirements, template_analysis)
            
            # 调用大模型生成内容
            if not self.llm_client:
                raise RuntimeError("LLM客户端未初始化")

            response = self.llm_client.call_model(prompt, temperature=0.7, max_tokens=8000)
            
            # 解析和验证生成的内容
            generated_content = self._parse_and_validate_response(response, template_requirements)
            
            logger.info(f"内容生成完成，共{len(generated_content.get('slides', []))}页")
            return generated_content
            
        except Exception as e:
            logger.error(f"基于模板生成内容失败: {e}")
            raise
    
    def _build_detailed_prompt(self, user_request: str, template_requirements: Dict[str, Any], 
                              template_analysis: Dict[str, Any]) -> str:
        """构建详细的提示词"""
        
        # 获取模板结构信息
        slides_requirements = template_requirements.get("slides_requirements", [])
        text_structure_summary = template_analysis.get("text_structure_summary", {})
        
        prompt_parts = [
            "你是一个专业的PPT内容生成助手。请严格按照以下模板结构要求生成PPT内容。",
            "",
            f"用户需求：{user_request}",
            "",
            "模板结构要求：",
            f"- 总页数：{len(slides_requirements)}页",
            f"- 总文字区域：{text_structure_summary.get('total_text_elements', 0)}个",
            "",
            "每页详细要求："
        ]
        
        # 为每页生成详细要求
        for slide_req in slides_requirements:
            slide_index = slide_req["slide_index"]
            prompt_parts.append(f"\n第{slide_index + 1}页：")
            
            for element_req in slide_req["required_text_elements"]:
                placeholder_id = element_req["placeholder_id"]
                text_type = element_req["text_type"]
                char_range = element_req["char_range"]
                original_example = element_req["original_example"]
                
                prompt_parts.append(
                    f"  - {placeholder_id} ({text_type})：{char_range['min']}-{char_range['max']}字"
                )
                prompt_parts.append(f"    原文示例：\"{original_example}\"")
        
        prompt_parts.extend([
            "",
            "重要要求：",
            "1. 严格按照每个占位符的字数要求生成内容",
            "2. 保持内容的逻辑性和连贯性",
            "3. 内容要与用户需求高度相关",
            "4. 标题要简洁有力，内容要详实具体",
            "5. 列表项要条理清晰，段落要结构完整",
            "",
            "请以以下JSON格式返回结果：",
            "{",
            '  "title": "PPT总标题",',
            '  "slides": [',
            '    {',
            '      "slide_index": 0,',
            '      "text_elements": {',
            '        "slide_0_text_0": "具体内容文字",',
            '        "slide_0_text_1": "具体内容文字"',
            '      }',
            '    }',
            '  ]',
            "}"
        ])
        
        return "\n".join(prompt_parts)
    
    def _parse_and_validate_response(self, response: str, template_requirements: Dict[str, Any]) -> Dict[str, Any]:
        """解析和验证大模型响应"""
        try:
            # 提取JSON内容
            json_content = self._extract_json_from_response(response)
            
            if not json_content:
                raise ValueError("无法从响应中提取有效的JSON内容")
            
            # 解析JSON
            generated_content = json.loads(json_content)
            
            # 验证结构
            self._validate_generated_content(generated_content, template_requirements)
            
            # 优化内容长度
            optimized_content = self._optimize_content_length(generated_content, template_requirements)
            
            return optimized_content
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            raise ValueError(f"生成的内容不是有效的JSON格式: {e}")
        except Exception as e:
            logger.error(f"解析响应失败: {e}")
            raise
    
    def _extract_json_from_response(self, response: str) -> str:
        """从响应中提取JSON内容"""
        # 查找JSON代码块
        if "```json" in response:
            start = response.find("```json") + 7
            end = response.find("```", start)
            if end != -1:
                return response[start:end].strip()
        
        # 查找大括号包围的内容
        start = response.find("{")
        if start != -1:
            brace_count = 0
            for i, char in enumerate(response[start:], start):
                if char == "{":
                    brace_count += 1
                elif char == "}":
                    brace_count -= 1
                    if brace_count == 0:
                        return response[start:i+1]
        
        return ""
    
    def _validate_generated_content(self, generated_content: Dict[str, Any], 
                                   template_requirements: Dict[str, Any]):
        """验证生成的内容结构"""
        slides_requirements = template_requirements.get("slides_requirements", [])
        generated_slides = generated_content.get("slides", [])
        
        if len(generated_slides) != len(slides_requirements):
            raise ValueError(f"页数不匹配：要求{len(slides_requirements)}页，生成{len(generated_slides)}页")
        
        # 验证每页的占位符
        for i, (slide_req, generated_slide) in enumerate(zip(slides_requirements, generated_slides)):
            required_placeholders = {elem["placeholder_id"] for elem in slide_req["required_text_elements"]}
            generated_placeholders = set(generated_slide.get("text_elements", {}).keys())
            
            missing_placeholders = required_placeholders - generated_placeholders
            if missing_placeholders:
                raise ValueError(f"第{i+1}页缺少占位符：{missing_placeholders}")
    
    def _optimize_content_length(self, generated_content: Dict[str, Any], 
                                template_requirements: Dict[str, Any]) -> Dict[str, Any]:
        """优化内容长度以匹配模板要求"""
        slides_requirements = template_requirements.get("slides_requirements", [])
        
        for slide_req, generated_slide in zip(slides_requirements, generated_content["slides"]):
            text_elements = generated_slide.get("text_elements", {})
            
            for element_req in slide_req["required_text_elements"]:
                placeholder_id = element_req["placeholder_id"]
                char_range = element_req["char_range"]
                
                if placeholder_id in text_elements:
                    content = text_elements[placeholder_id]
                    optimized_content = self._adjust_text_length(
                        content, char_range["min"], char_range["max"]
                    )
                    text_elements[placeholder_id] = optimized_content
        
        return generated_content
    
    def _adjust_text_length(self, text: str, min_chars: int, max_chars: int) -> str:
        """调整文字长度"""
        current_length = len(text)
        
        if current_length < min_chars:
            # 内容太短，需要扩展
            if "•" in text or "-" in text:
                # 列表内容，添加更多项目
                lines = text.split('\n')
                while len('\n'.join(lines)) < min_chars and len(lines) < 10:
                    lines.append(f"• 相关要点{len(lines)}")
                return '\n'.join(lines)[:max_chars]
            else:
                # 普通文本，添加补充说明
                return text + "，这是一个重要的方面，需要深入理解和应用。"[:max_chars]
        
        elif current_length > max_chars:
            # 内容太长，需要截断
            if "•" in text or "-" in text:
                # 列表内容，保留前几项
                lines = text.split('\n')
                result_lines = []
                current_len = 0
                for line in lines:
                    if current_len + len(line) + 1 <= max_chars:
                        result_lines.append(line)
                        current_len += len(line) + 1
                    else:
                        break
                return '\n'.join(result_lines)
            else:
                # 普通文本，直接截断
                return text[:max_chars-3] + "..."
        
        return text


# 创建全局实例
template_based_content_generator = TemplateBasedContentGenerator()
