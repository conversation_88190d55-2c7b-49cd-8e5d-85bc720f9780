"""
PPT生成相关API路由 - 基于专业PPT生成器
"""
from fastapi import APIRouter, HTTPException, Response
from fastapi.responses import FileResponse
from pydantic import BaseModel
from typing import Dict, List, Optional
import logging
import os

from ..services.moffee_service import MoffeeService

logger = logging.getLogger(__name__)
router = APIRouter()

# 全局服务实例
moffee_service = MoffeeService()

class PreviewRequest(BaseModel):
    """预览请求"""
    outline: Dict
    theme: Optional[str] = "default"

class ExportRequest(BaseModel):
    """导出请求"""
    outline: Dict
    theme: Optional[str] = "default"
    format: Optional[str] = "pdf"  # pdf, html

class ProfessionalGenerateRequest(BaseModel):
    """专业PPT生成请求"""
    content: str
    template_id: Optional[str] = None
    user_input: Optional[str] = ""
    apply_patches: Optional[bool] = True

class GenerateResponse(BaseModel):
    """生成响应"""
    success: bool
    data: Optional[Dict] = None
    message: str = ""
    error: Optional[str] = None

@router.get("/themes", response_model=GenerateResponse)
async def get_available_themes():
    """获取可用主题列表"""
    try:
        themes = moffee_service.get_available_themes()
        
        return GenerateResponse(
            success=True,
            data={"themes": themes},
            message="获取主题列表成功"
        )
        
    except Exception as e:
        logger.error(f"获取主题列表失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取主题列表失败: {str(e)}"
        )

@router.post("/preview", response_model=GenerateResponse)
async def generate_preview(request: PreviewRequest):
    """生成PPT预览"""
    try:
        logger.info(f"收到预览请求，主题: {request.theme}")
        
        # 验证大纲格式
        is_valid, error_msg = moffee_service.validate_outline(request.outline)
        if not is_valid:
            raise ValueError(f"大纲格式错误: {error_msg}")
        
        # 生成预览
        html_file = moffee_service.generate_preview(
            outline=request.outline,
            theme=request.theme
        )
        
        return GenerateResponse(
            success=True,
            data={
                "preview_url": f"/files/preview/{os.path.basename(html_file)}",
                "theme": request.theme
            },
            message="预览生成成功"
        )
        
    except Exception as e:
        logger.error(f"生成预览失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"生成预览失败: {str(e)}"
        )

@router.post("/markdown", response_model=GenerateResponse)
async def generate_markdown(request: PreviewRequest):
    """生成Markdown内容"""
    try:
        logger.info(f"收到Markdown生成请求，主题: {request.theme}")
        
        # 验证大纲格式
        is_valid, error_msg = moffee_service.validate_outline(request.outline)
        if not is_valid:
            raise ValueError(f"大纲格式错误: {error_msg}")
        
        # 生成Markdown
        markdown_content = moffee_service.generate_markdown_from_outline(
            outline=request.outline,
            theme=request.theme
        )
        
        return GenerateResponse(
            success=True,
            data={
                "markdown": markdown_content,
                "theme": request.theme
            },
            message="Markdown生成成功"
        )
        
    except Exception as e:
        logger.error(f"生成Markdown失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"生成Markdown失败: {str(e)}"
        )

@router.post("/export")
async def export_presentation(request: ExportRequest):
    """导出演示文稿"""
    try:
        logger.info(f"收到导出请求，格式: {request.format}, 主题: {request.theme}")
        
        # 验证大纲格式
        is_valid, error_msg = moffee_service.validate_outline(request.outline)
        if not is_valid:
            raise ValueError(f"大纲格式错误: {error_msg}")
        
        if request.format.lower() == "pdf":
            # 导出PDF
            file_path = moffee_service.export_to_pdf(
                outline=request.outline,
                theme=request.theme
            )
            
            # 返回文件下载
            return FileResponse(
                path=file_path,
                filename=f"presentation_{request.theme}.pdf",
                media_type="application/pdf"
            )
            
        elif request.format.lower() == "html":
            # 导出HTML
            html_file = moffee_service.generate_preview(
                outline=request.outline,
                theme=request.theme
            )
            
            return FileResponse(
                path=html_file,
                filename=f"presentation_{request.theme}.html",
                media_type="text/html"
            )
            
        else:
            raise ValueError(f"不支持的导出格式: {request.format}")
        
    except Exception as e:
        logger.error(f"导出失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"导出失败: {str(e)}"
        )

@router.post("/professional", response_model=GenerateResponse)
async def generate_with_professional(request: ProfessionalGenerateRequest):
    """使用专业PPT生成器生成PPT"""
    try:
        logger.info(f"收到专业PPT生成请求，模板ID: {request.template_id}")

        # 验证内容
        if not request.content.strip():
            raise ValueError("生成内容不能为空")

        # 使用专业PPT生成器
        from ..services.professional_ppt_generator import ProfessionalPPTGenerator
        import time

        generator = ProfessionalPPTGenerator()

        # 生成输出文件名
        timestamp = int(time.time())
        output_filename = f"professional_generated_{timestamp}.pptx"

        # 生成PPT
        result = generator.generate_ppt_from_template(
            template_id=request.template_id,
            user_request=f"{request.user_input}\n\n{request.content}",
            output_filename=output_filename
        )

        if result.get("success"):
            return GenerateResponse(
                success=True,
                data=result,
                message="专业PPT生成成功"
            )
        else:
            raise Exception(result.get("error", "PPT生成失败"))

    except Exception as e:
        logger.error(f"专业PPT生成失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"专业PPT生成失败: {str(e)}"
        )

@router.post("/validate-outline", response_model=GenerateResponse)
async def validate_outline(outline: Dict):
    """验证大纲格式"""
    try:
        is_valid, message = moffee_service.validate_outline(outline)

        return GenerateResponse(
            success=is_valid,
            data={"valid": is_valid},
            message=message if is_valid else "",
            error=message if not is_valid else None
        )

    except Exception as e:
        logger.error(f"验证大纲失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"验证大纲失败: {str(e)}"
        )
