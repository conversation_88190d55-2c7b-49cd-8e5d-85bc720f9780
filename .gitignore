# Python缓存文件
__pycache__/
*.py[cod]
*$py.class
*.so

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试/覆盖率报告
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# 翻译
*.mo
*.pot

# Django
*.log
local_settings.py
db.sqlite3

# Flask
instance/
.webassets-cache

# Scrapy
.scrapy

# Sphinx文档
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat调度文件
celerybeat-schedule

# SageMath解析文件
*.sage.py

# 环境变量
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder项目设置
.spyderproject
.spyproject

# Rope项目设置
.ropeproject

# mkdocs文档
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# IDE设置
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 项目特定的临时文件和缓存
backend/temp/
backend/temp_processing/
backend/processed_templates/
backend/uploads/generated/
backend/uploads/static/
backend/static/
backend/generated/

# 模板分析缓存
backend/template_analysis/

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp
*.bak

# PPT生成的临时文件
*.pptx.tmp
*_placeholder.pptx
*_generated_*.pptx

# Node.js (如果有前端)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 配置文件中的敏感信息
config/local.py
config/production.py
.env.local
.env.production

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 备份文件
*.backup
*.bak
