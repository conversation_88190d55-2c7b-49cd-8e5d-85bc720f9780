import sys
sys.path.insert(0, './backend')

from app.services.outline_converter import outline_converter

# 测试JSON到兼容模板转换
json_outline = {
    'title': '基于人工智能的结构化数据文本编辑方法及系统',
    'subtitle': '专利技术介绍',
    'slides': [
        {
            'title': '封面',
            'content': ['基于人工智能的结构化数据文本编辑方法及系统', '专利技术介绍', '2025年']
        },
        {
            'title': '背景与痛点',
            'content': [
                '结构化数据只能使用Excel等表格工具编辑',
                '编辑操作复杂，效率低下'
            ]
        }
    ]
}

print('🔄 测试兼容模板转换...')
markdown_result = outline_converter.json_to_markdown(json_outline, 'template_20250708_221451_adc64662.pptx')

print('✅ 转换成功！')
print('=' * 50)
print(markdown_result)
print('=' * 50)
