# -*- coding: utf-8 -*-
"""
PPT生成系统 - 提示词处理模块
负责提示词的标准化、占位符替换、格式化等
"""

import logging
import re
import os
import datetime

logger = logging.getLogger(__name__)

class PromptProcessor:
    """提示词处理器 - 专为PPT生成优化"""

    def __init__(self):
        """初始化提示词处理器"""
        logger.debug("初始化PPT提示词处理器")

    def process_prompt(self, prompt, placeholder=None):
        """
        处理提示词，替换占位符

        Args:
            prompt: 提示词
            placeholder: 占位符替换字典，用于替换prompt中的占位符

        Returns:
            str: 处理后的提示词
        """
        if not prompt:
            raise ValueError("提示词不能为空")

        processed_prompt = prompt

        if placeholder and isinstance(placeholder, dict):
            for key, value in placeholder.items():
                if not isinstance(key, str):
                    logger.warning(f"占位符键必须是字符串，跳过: {key}")
                    continue

                str_value = str(value) if value is not None else ""
                processed_prompt = processed_prompt.replace(key, str_value)

        return processed_prompt

    def standardize_prompt(self, prompt):
        """
        标准化提示词格式，保留原始排版格式

        Args:
            prompt: 提示词

        Returns:
            str: 标准化后的提示词
        """
        if not prompt:
            return ""

        prompt = prompt.rstrip()
        if prompt and not prompt.endswith('\n'):
            prompt += '\n'

        return prompt

    def build_ppt_prompt(self, content, template_info=None, user_input=""):
        """
        构建PPT生成提示词

        Args:
            content: PPT内容
            template_info: 模板信息
            user_input: 用户输入

        Returns:
            str: 完整的PPT生成提示词
        """
        if not content:
            raise ValueError("PPT内容不能为空")

        # 构建基础提示词
        prompt = f"""请根据以下内容生成专业的PPT大纲：

用户需求：{user_input}

内容：
{content}

要求：
1. 生成结构清晰的PPT大纲
2. 每页内容要简洁明了
3. 逻辑层次分明
4. 适合演示展示

请以markdown格式输出PPT大纲。"""

        # 如果有模板信息，添加模板相关的指导
        if template_info:
            template_guidance = f"""

模板信息：
- 模板名称：{template_info.get('name', '未知')}
- 推荐页数：{template_info.get('recommended_slides', 5)}页
- 风格：{template_info.get('style', '商务')}

请根据模板特点调整内容结构。"""
            prompt += template_guidance

        return self.standardize_prompt(prompt)

    def validate_prompt_template(self, template, required_placeholders=None):
        """验证提示词模板"""
        if not template:
            return False, "提示词模板不能为空"
        if not isinstance(template, str):
            return False, "提示词模板必须是字符串"
        if len(template.strip()) < 10:
            return False, "提示词模板过短"

        if required_placeholders:
            for placeholder in required_placeholders:
                if placeholder not in template:
                    return False, f"提示词模板缺少必需的占位符: {placeholder}"

        # 检查占位符格式
        placeholders = re.findall(r'\{[^}]+\}', template)
        for placeholder in placeholders:
            placeholder_name = placeholder[1:-1]
            if not placeholder_name.isidentifier() and not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', placeholder_name):
                logger.warning(f"占位符名称可能无效: {placeholder}")

        return True, ""

    def extract_placeholders(self, template):
        """提取模板中的占位符"""
        if not template:
            return []
        placeholders = re.findall(r'\{([^}]+)\}', template)
        return list(set(placeholders))

    def format_response_for_logging(self, response, max_length=200):
        """格式化响应用于日志记录"""
        if not response:
            return "空响应"
        formatted = re.sub(r'\s+', ' ', str(response).strip())
        if len(formatted) > max_length:
            formatted = formatted[:max_length] + "..."
        return formatted

    def save_prompt_to_file(self, prompt, filename_prefix="ppt_prompt"):
        """保存提示词到文件"""
        try:
            logs_dir = "logs"
            if not os.path.exists(logs_dir):
                os.makedirs(logs_dir)

            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{logs_dir}/{filename_prefix}_{timestamp}.txt"

            with open(filename, "w", encoding="utf-8", newline='') as f:
                f.write(prompt)

            logger.info(f"提示词已保存到文件: {filename}")
            return filename
        except Exception as e:
            logger.error(f"保存提示词到文件失败: {str(e)}", exc_info=True)
            return None

    def save_response_to_file(self, response, filename_prefix="ppt_response"):
        """保存响应到文件"""
        try:
            logs_dir = "logs"
            if not os.path.exists(logs_dir):
                os.makedirs(logs_dir)

            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{logs_dir}/{filename_prefix}_{timestamp}.txt"

            with open(filename, "w", encoding="utf-8", newline='') as f:
                f.write(str(response))

            logger.info(f"响应已保存到文件: {filename}")
            return filename
        except Exception as e:
            logger.error(f"保存响应到文件失败: {str(e)}", exc_info=True)
            return None
