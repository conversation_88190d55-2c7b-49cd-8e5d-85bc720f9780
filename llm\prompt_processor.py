# -*- coding: utf-8 -*-
"""
提示词处理模块（修复版 - 支持工单ID）
负责提示词的标准化、占位符替换、格式化等
保留原始排版格式，文件名包含工单ID
"""

import logging
import re
import json 
import datetime
import os # Added missing import

logger = logging.getLogger(__name__)

class PromptProcessor:
    """提示词处理器"""
    
    def __init__(self):
        """初始化提示词处理器"""
        logger.debug("初始化提示词处理器")
    
    def process_prompt(self, prompt, placeholder=None):
        """
        处理提示词，替换占位符
        
        Args:
            prompt: 提示词
            placeholder: 占位符替换字典，用于替换prompt中的占位符
            
        Returns:
            str: 处理后的提示词
        """
        if not prompt:
            raise ValueError("提示词不能为空")
        
        processed_prompt = prompt
        
        if placeholder and isinstance(placeholder, dict):
            for key, value in placeholder.items():
                if not isinstance(key, str):
                    logger.warning(f"占位符键必须是字符串，跳过: {key}")
                    continue
                
                str_value = str(value) if value is not None else ""
                processed_prompt = processed_prompt.replace(key, str_value)
        
        return processed_prompt
    
    def standardize_prompt(self, prompt):
        """
        标准化提示词格式，保留原始排版格式
        
        Args:
            prompt: 提示词
            
        Returns:
            str: 标准化后的提示词
        """
        if not prompt:
            return ""
        
        prompt = prompt.rstrip()
        if prompt and not prompt.endswith('\n'):
            prompt += '\n'
        
        return prompt
    
    def build_work_events_text(self, work_events, use_aggregation=True):
        """
        构建工单列表文本
        
        Args:
            work_events: 工单数据列表
            use_aggregation: 是否使用聚合流程 (True: ID+内容列表; False: 仅第一个工单内容)
            
        Returns:
            str: 工单列表文本
        """
        if not work_events:
            return ""
        
        if use_aggregation:
            work_events_list = ""
            for i, event in enumerate(work_events):
                event_id = event.get('id', '')
                case_remarks = event.get('case_remarks', '')
                work_events_list += f"{i+1}.ID:{event_id};内容:{case_remarks}\n"
            return work_events_list.rstrip('\n')
        else:
            if work_events and len(work_events) > 0:
                return work_events[0].get('case_remarks', '')
            return ""
    
    def build_categories_text(self, flat_paths_input, use_aggregation=True):
        """
        构建分类文本 (此方法可能需要根据 flat_paths_input 的实际类型调整或不再被 build_classification_prompt 直接使用)
        
        Args:
            flat_paths_input: 扁平化的分类路径列表 (期望是结构化数据，如列表套字典)
                              或者已经是格式化好的字符串 (如果调用方已处理)
            use_aggregation: 是否使用聚合流程 (影响输出格式)
            
        Returns:
            str: 分类文本
        """
        if not flat_paths_input:
            return ""
        
        if isinstance(flat_paths_input, str):
            logger.debug("build_categories_text: 输入已是字符串，直接返回。")
            return flat_paths_input

        if use_aggregation:
            def json_serializer_local(obj):
                if isinstance(obj, (datetime.datetime, datetime.date)):
                    return obj.strftime('%Y-%m-%d %H:%M:%S')
                raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
            try:
                return json.dumps(flat_paths_input, ensure_ascii=False, separators=(',', ':'), default=json_serializer_local)
            except TypeError: 
                 logger.warning("build_categories_text: 输入数据在JSON序列化时出错 (聚合模式)，将尝试简单字符串转换。")
                 return str(flat_paths_input)
        else:
            categories_list = []
            if isinstance(flat_paths_input, list):
                for item in flat_paths_input:
                    if isinstance(item, dict) and "path" in item and isinstance(item["path"], list):
                        path = item["path"]
                        path_str = ",".join(str(p) for p in path)
                        categories_list.append(path_str)
                    else:
                        logger.warning(f"build_categories_text: 非聚合模式下，项目格式不符合预期: {item}")
            else:
                logger.warning(f"build_categories_text: 非聚合模式下，输入不是列表: {type(flat_paths_input)}")
            return "\n".join(categories_list)
    
    def build_classification_prompt(self, work_events, flat_paths_str, template):
        """
        构建分类提示词
        
        Args:
            work_events: 工单数据列表
            flat_paths_str: 格式化好的分类路径字符串 (由batch_processor准备)
            template: 提示词模板
            
        Returns:
            str: 完整的提示词
        """
        if not template:
            raise ValueError("提示词模板不能为空")
        
        # 简化配置，使用默认值
        use_aggregation_for_work_events_text = False
        
        work_events_text = self.build_work_events_text(work_events, use_aggregation_for_work_events_text)
        categories_text_for_prompt = flat_paths_str if flat_paths_str is not None else ""

        prompt = template.replace('{existing_categories}', categories_text_for_prompt)
        prompt = prompt.replace('{work_events}', work_events_text)
        
        prompt = self.standardize_prompt(prompt)
        logger.debug(f"构建的分类提示词 (前300字符): {prompt[:300]}...")
        return prompt
    
    def build_aggregation_prompt(self, new_categories, template):
        if not template:
            raise ValueError("提示词模板不能为空")
        simplified_new = self._simplify_new_categories(new_categories)
        def json_serializer_local(obj):
            if isinstance(obj, (datetime.datetime, datetime.date)):
                return obj.strftime('%Y-%m-%d %H:%M:%S')
            raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
        new_categories_json = json.dumps(simplified_new, ensure_ascii=False, separators=(',', ':'), default=json_serializer_local)
        prompt = template.replace('{new_categories}', new_categories_json)
        prompt = self.standardize_prompt(prompt)
        return prompt

    def _simplify_new_categories(self, new_categories):
        if isinstance(new_categories, dict) and 'new_classifications' in new_categories:
            return new_categories.get('new_classifications', [])
        elif isinstance(new_categories, dict) and 'matches' in new_categories:
            return new_categories.get('matches', [])
        elif isinstance(new_categories, list):
            return new_categories
        else:
            logger.warning(f"未知的新分类数据格式: {type(new_categories)}")
            return []

    def validate_prompt_template(self, template, required_placeholders=None):
        if not template: return False, "提示词模板不能为空"
        if not isinstance(template, str): return False, "提示词模板必须是字符串"
        if len(template.strip()) < 10: return False, "提示词模板过短"
        if required_placeholders:
            for placeholder in required_placeholders:
                if placeholder not in template:
                    return False, f"提示词模板缺少必需的占位符: {placeholder}"
        placeholders = re.findall(r'\{[^}]+\}', template)
        for placeholder in placeholders:
            placeholder_name = placeholder[1:-1]
            if not placeholder_name.isidentifier() and not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', placeholder_name):
                logger.warning(f"占位符名称可能无效: {placeholder}")
        return True, ""

    def extract_placeholders(self, template):
        if not template: return []
        placeholders = re.findall(r'\{([^}]+)\}', template)
        return list(set(placeholders))

    def format_response_for_logging(self, response, max_length=200):
        if not response: return "空响应"
        formatted = re.sub(r'\s+', ' ', str(response).strip())
        if len(formatted) > max_length: formatted = formatted[:max_length] + "..."
        return formatted

    def save_prompt_to_file(self, prompt, filename_prefix="prompt", work_event_ids=None):
        try:
            logs_dir = "logs"
            if not os.path.exists(logs_dir): os.makedirs(logs_dir)
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            id_str_part = ""
            if work_event_ids and len(work_event_ids) > 0:
                if len(work_event_ids) > 3: id_str_part = "_" + "_".join(str(id) for id in work_event_ids[:3]) + f"_etc{len(work_event_ids)}"
                else: id_str_part = "_" + "_".join(str(id) for id in work_event_ids)
            filename = f"{logs_dir}/{filename_prefix}_{timestamp}{id_str_part}.txt"
            with open(filename, "w", encoding="utf-8", newline='') as f: f.write(prompt)
            logger.info(f"提示词已保存到文件: {filename}")
            return filename
        except Exception as e:
            logger.error(f"保存提示词到文件失败: {str(e)}", exc_info=True) # Added exc_info
            return None
    
    def save_response_to_file(self, response, filename_prefix="response", work_event_ids=None):
        try:
            logs_dir = "logs"
            if not os.path.exists(logs_dir): os.makedirs(logs_dir)
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            id_str_part = ""
            if work_event_ids and len(work_event_ids) > 0:
                if len(work_event_ids) > 3: id_str_part = "_" + "_".join(str(id) for id in work_event_ids[:3]) + f"_etc{len(work_event_ids)}"
                else: id_str_part = "_" + "_".join(str(id) for id in work_event_ids)
            filename = f"{logs_dir}/{filename_prefix}_{timestamp}{id_str_part}.txt"
            with open(filename, "w", encoding="utf-8", newline='') as f: f.write(str(response))
            logger.info(f"响应已保存到文件: {filename}")
            return filename
        except Exception as e:
            logger.error(f"保存响应到文件失败: {str(e)}", exc_info=True) # Added exc_info
            return None
