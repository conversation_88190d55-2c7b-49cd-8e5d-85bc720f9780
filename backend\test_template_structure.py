#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试模板结构化JSON生成
"""

import sys
import json
import requests
from pathlib import Path

def test_template_structure_api():
    """通过API测试模板结构化JSON生成"""
    try:
        print('🔍 测试模板结构化JSON生成')
        print('=' * 50)
        
        # API端点
        url = 'http://localhost:9527/api/template-ppt/parse-template'
        
        # 请求数据
        data = {
            "template_id": "template_20250708_221451_adc64662"
        }
        
        print(f'📡 调用API: {url}')
        print(f'📋 请求数据: {data}')
        print()
        
        # 发送请求
        response = requests.post(url, json=data, timeout=30)
        
        print(f'📊 响应状态: {response.status_code}')
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get("success"):
                print('✅ 模板解析成功!')
                
                # 获取结构化数据
                template_structure = result["data"]["template_structure"]
                total_pages = result["data"]["total_pages"]
                total_placeholders = result["data"]["total_placeholders"]
                
                print(f'📄 总页数: {total_pages}')
                print(f'🏷️  总占位符: {total_placeholders}')
                print()
                
                print('📋 结构化JSON描述:')
                print('=' * 50)
                
                # 格式化输出JSON
                formatted_json = json.dumps(template_structure, ensure_ascii=False, indent=2)
                print(formatted_json)
                
                print()
                print('=' * 50)
                
                # 显示前2页的详细信息
                print('📖 前2页详细信息:')
                for i, (page_key, page_data) in enumerate(list(template_structure.items())[:2]):
                    print(f'\n{page_key}:')
                    for element_key, element_data in page_data.items():
                        print(f'  {element_key}:')
                        print(f'    类型: {element_data["type"]}')
                        print(f'    内容: {element_data["content"][:50]}...')
                        print(f'    字数限制: {element_data["word_limit"]}')
                        print(f'    占位符ID: {element_data["PlaceholderId"]}')
                
                return True
                
            else:
                print(f'❌ 模板解析失败: {result.get("message")}')
                print(f'错误: {result.get("error")}')
                return False
        else:
            print(f'❌ API调用失败: {response.status_code}')
            print(f'响应: {response.text}')
            return False
            
    except Exception as e:
        print(f'❌ 测试异常: {e}')
        import traceback
        traceback.print_exc()
        return False

def test_direct_parsing():
    """直接测试模板解析（不通过API）"""
    try:
        print('\n🔧 直接测试模板解析')
        print('=' * 50)
        
        sys.path.insert(0, str(Path(__file__).parent))
        from app.services.template_based_ppt_generator import template_ppt_generator
        
        template_id = 'template_20250708_221451_adc64662'
        
        # 获取模板路径
        template_path = template_ppt_generator._get_template_path(template_id)
        if not template_path:
            print(f'❌ 模板文件不存在: {template_id}')
            return False
        
        print(f'✅ 找到模板: {template_path}')
        
        # 解析模板
        result = template_ppt_generator.step1_parse_template_ppt(template_path)
        
        if result["success"]:
            print('✅ 直接解析成功!')
            
            template_structure = result["template_structure"]
            
            print('📋 生成的结构化JSON:')
            print('=' * 50)
            
            # 格式化输出
            formatted_json = json.dumps(template_structure, ensure_ascii=False, indent=2)
            print(formatted_json)
            
            return True
        else:
            print(f'❌ 直接解析失败: {result["error"]}')
            return False
            
    except Exception as e:
        print(f'❌ 直接测试异常: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print('🚀 开始测试模板结构化JSON生成')
    print()
    
    # 先测试API方式
    api_success = test_template_structure_api()
    
    # 如果API失败，尝试直接测试
    if not api_success:
        print('\n🔄 API测试失败，尝试直接测试...')
        direct_success = test_direct_parsing()
    else:
        direct_success = True
    
    print(f'\n🎯 最终结果:')
    print(f'  API测试: {"✅ 成功" if api_success else "❌ 失败"}')
    print(f'  直接测试: {"✅ 成功" if direct_success else "❌ 失败"}')
    
    if api_success or direct_success:
        print('\n✅ 结构化JSON生成功能正常！')
        print('您可以通过以下方式获取结构化JSON:')
        print('1. API调用: POST /api/template-ppt/parse-template')
        print('2. 前端界面: 模板管理 -> 重新解析模板')
    else:
        print('\n❌ 结构化JSON生成功能异常，需要检查！')
