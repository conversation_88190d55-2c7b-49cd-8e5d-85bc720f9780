# 新会话快速上手指南

## 🎯 立即开始

如果您是在新的会话窗口中接手这个项目，请按以下步骤快速了解和启动：

### 1️⃣ 快速状态检查 (30秒)
```bash
# 运行状态检查脚本
python quick_status_check.py
```

### 2️⃣ 阅读项目文档 (5分钟)
- 📖 **必读**: `项目详细说明文档.md` - 完整项目状态和技术细节
- 📋 **参考**: `核心开发方案.md` - 开发计划和任务列表

### 3️⃣ 启动服务 (2分钟)
```bash
# 启动后端 (终端1)
cd backend
python run_server.py

# 启动前端 (终端2)  
cd frontend
npm run dev
```

### 4️⃣ 验证功能 (3分钟)
- 🌐 访问前端: http://127.0.0.1:9528
- 🔧 检查后端: http://localhost:9527/health
- 📚 API文档: http://localhost:9527/docs

## 📊 项目当前状态

### ✅ 已完成功能
- **模板选择**: 支持模板上传、预览、选择
- **智能对话**: 基于DeepSeek V3的真实AI对话
- **大纲生成**: 自动生成PPT大纲，支持编辑
- **端口管理**: 严格的9527/9528端口配置
- **错误处理**: 完善的前后端错误处理机制

### 🔧 技术架构
- **前端**: React 18 + Vite + Tailwind CSS (端口9528)
- **后端**: FastAPI + Python (端口9527)
- **AI**: DeepSeek V3 API集成
- **配置**: 统一的config.json管理

### 🐛 已修复问题
1. ✅ 模板选择"请选择就绪的模板"问题
2. ✅ 对话阶段只有前端模拟响应问题  
3. ✅ 大纲生成失败但后端成功问题
4. ✅ OutlineStep组件`outline.trim is not a function`错误
5. ✅ 端口管理混乱问题

## 🚨 重要注意事项

### 端口配置 (关键!)
- **严格使用**: 后端9527，前端9528
- **禁止使用**: 9529端口 (已移除)
- **严格模式**: 前端端口被占用时启动失败，不自动切换

### API响应格式 (已标准化)
```json
// 大纲生成API响应
{
  "success": true,
  "data": {
    "outline": "大纲内容(字符串或对象)"
  },
  "message": "大纲生成成功"
}

// 模板列表API响应  
[
  {
    "id": "template_id",
    "name": "模板名称",
    "status": "ready",  // 必须字段
    ...
  }
]
```

### 数据类型处理 (已完善)
- **大纲数据**: 支持字符串和对象两种格式
- **类型转换**: 前端自动处理类型转换
- **验证逻辑**: 安全的类型检查，避免trim错误

## 🔄 开发工作流

### 当前开发重点
1. **PPT生成功能**: 完善主题选择和文件生成
2. **预览功能**: 实时预览和在线编辑
3. **导出功能**: 多格式导出支持
4. **用户体验**: 界面优化和错误提示

### 开发环境检查
```bash
# 检查Python环境
python --version  # 需要3.9+

# 检查Node.js环境  
node --version    # 需要16+
npm --version

# 检查依赖
pip list | grep fastapi
npm list react
```

## 🛠️ 常用调试命令

### 端口管理
```bash
# 检查端口占用
netstat -ano | findstr ":952"

# 快速端口检查
python -c "
import socket
def check_port(port):
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(1)
            return s.connect_ex(('127.0.0.1', port)) == 0
    except: return False
print('Backend (9527):', 'IN USE' if check_port(9527) else 'AVAILABLE')
print('Frontend (9528):', 'IN USE' if check_port(9528) else 'AVAILABLE')
"
```

### API测试
```bash
# 健康检查
curl http://localhost:9527/health

# 模板列表
curl http://localhost:9527/api/templates/

# 大纲生成测试
curl -X POST http://localhost:9527/api/chat/outline \
  -H "Content-Type: application/json" \
  -d '{"user_input":"创建AI PPT","context":"测试"}'
```

### 日志查看
- **后端日志**: 在运行`python run_server.py`的终端查看
- **前端日志**: 浏览器开发者工具Console
- **网络请求**: 浏览器开发者工具Network

## 📁 关键文件位置

### 前端核心文件
```
frontend/src/
├── pages/HomePage.jsx           # 主页面，状态管理
├── components/Steps/
│   ├── TemplateStep.jsx        # 模板选择
│   ├── ChatStep.jsx            # 对话阶段  
│   ├── OutlineStep.jsx         # 大纲确认
│   └── ThemeStep.jsx           # 主题选择
├── services/api.js             # API调用
└── config/config.js            # 前端配置
```

### 后端核心文件
```
backend/
├── app/main.py                 # FastAPI主应用
├── app/api/
│   ├── chat.py                 # 对话API
│   ├── generate.py             # 生成API
│   └── templates.py            # 模板API
├── app/services/
│   ├── chat_service.py         # 对话服务
│   └── template_manager.py     # 模板管理
└── run_server.py               # 启动脚本
```

## 🎯 下一步开发建议

1. **立即可做**: 测试现有功能，确保基础流程正常
2. **短期目标**: 完善PPT生成和预览功能
3. **中期目标**: 优化用户界面和体验
4. **长期目标**: 添加高级功能和性能优化

## 📞 获取帮助

如果遇到问题：
1. 🔍 运行 `python quick_status_check.py` 检查状态
2. 📖 查看 `项目详细说明文档.md` 了解详情
3. 🐛 检查已修复问题列表，避免重复工作
4. 🔧 使用调试命令定位问题

---

**记住**: 这个项目已经有了坚实的基础，主要功能都已实现并测试通过。您的任务是在现有基础上继续完善和优化！
