#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent))

def test_fixed_version():
    """测试修复后的版本"""
    try:
        from app.services.professional_ppt_generator import professional_ppt_generator
        
        print('🔧 测试修复后的版本...')
        
        result = professional_ppt_generator.generate_ppt_from_template(
            'template_20250708_221451_adc64662', 
            '人工智能医疗应用测试', 
            'fixed_test.pptx', 
            test_mode=True
        )
        
        if result.get('success'):
            print('✅ 生成成功!')
            print(f'文件: {result["file_path"]}')
            print(f'大小: {result["file_size"]/1024/1024:.1f} MB')
            
            # 验证文件
            from pptx import Presentation
            prs = Presentation(result["file_path"])
            print(f'页数: {len(prs.slides)}')
            
            # 尝试访问第一页
            try:
                slide = prs.slides[0]
                print('✅ 文件结构正常')
                return True
            except Exception as e:
                print(f'❌ 文件结构异常: {e}')
                return False
        else:
            print('❌ 生成失败!')
            print(f'错误: {result.get("error")}')
            return False
            
    except Exception as e:
        print(f'❌ 测试异常: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_fixed_version()
