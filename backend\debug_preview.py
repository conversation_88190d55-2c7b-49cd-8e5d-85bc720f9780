#!/usr/bin/env python3
"""
直接测试预览生成，绕过所有其他逻辑
"""

import sys
import os
from pathlib import Path
import io

# 添加当前目录到路径
sys.path.insert(0, '.')

def generate_preview_directly():
    """直接生成预览，不通过模板管理器"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        from pptx import Presentation
        
        # 找到模板文件
        template_files = list(Path("templates").glob("*.pptx"))
        if not template_files:
            print("❌ 没有找到PPT模板文件")
            return
        
        template_file = template_files[0]
        print(f"使用模板文件: {template_file}")
        
        # 加载PPT
        prs = Presentation(str(template_file))
        print(f"✅ PPT加载成功，{len(prs.slides)} 张幻灯片")
        
        if len(prs.slides) > 0:
            slide = prs.slides[0]
            
            # 提取内容
            text_content = []
            images = []
            
            print(f"第一张幻灯片有 {len(slide.shapes)} 个形状")
            
            for i, shape in enumerate(slide.shapes):
                try:
                    # 提取文本
                    if hasattr(shape, 'text_frame') and shape.text_frame.text.strip():
                        text = shape.text_frame.text.strip()
                        text_content.append(text)
                        print(f"  文本{i}: {text[:30]}...")
                    
                    # 提取图片
                    if hasattr(shape, 'image'):
                        try:
                            image = shape.image
                            images.append({
                                'format': image.ext,
                                'size': len(image.blob),
                                'data': image.blob
                            })
                            print(f"  图片{i}: {image.ext}, {len(image.blob)} bytes")
                        except Exception as e:
                            print(f"  图片提取失败: {e}")
                            
                except Exception as e:
                    print(f"  处理形状{i}失败: {e}")
            
            print(f"\n提取结果: {len(text_content)}个文本, {len(images)}张图片")
            
            # 生成预览图片
            img = Image.new('RGB', (400, 300), color='white')
            
            # 如果有图片，使用第一张图片作为背景
            background_applied = False
            if images:
                try:
                    print("尝试使用PPT中的图片作为背景...")
                    # 使用最大的图片作为背景
                    largest_image = max(images, key=lambda x: x['size'])
                    print(f"选择图片: {largest_image['format']}, {largest_image['size']} bytes")
                    
                    bg_image = Image.open(io.BytesIO(largest_image['data']))
                    print(f"原始图片尺寸: {bg_image.size}")
                    
                    # 调整背景图片大小
                    bg_image = bg_image.resize((400, 300), Image.Resampling.LANCZOS)
                    
                    # 创建半透明遮罩
                    overlay = Image.new('RGBA', (400, 300), (255, 255, 255, 128))
                    bg_image = bg_image.convert('RGBA')
                    img = Image.alpha_composite(bg_image, overlay).convert('RGB')
                    
                    background_applied = True
                    print("✅ 成功应用PPT背景图片")
                    
                except Exception as e:
                    print(f"❌ 应用背景图片失败: {e}")
                    import traceback
                    traceback.print_exc()
            
            if not background_applied:
                print("使用渐变背景...")
                for y in range(300):
                    color_value = int(255 - (y * 50 / 300))
                    for x in range(400):
                        img.putpixel((x, y), (color_value, color_value, 255))
            
            draw = ImageDraw.Draw(img)
            
            # 绘制边框
            draw.rectangle([0, 0, 399, 299], outline='#1976d2', width=2)
            
            # 绘制文本
            try:
                font = ImageFont.truetype("C:/Windows/Fonts/msyh.ttc", 16)
                title_font = ImageFont.truetype("C:/Windows/Fonts/msyh.ttc", 20)
            except:
                font = ImageFont.load_default()
                title_font = ImageFont.load_default()
            
            y_offset = 20
            if text_content:
                title = text_content[0][:30] + "..." if len(text_content[0]) > 30 else text_content[0]
                draw.text((20, y_offset), title, fill='#333333', font=title_font)
                y_offset += 40
                
                for i, text in enumerate(text_content[1:3]):
                    if y_offset > 250:
                        break
                    content = text[:40] + "..." if len(text) > 40 else text
                    draw.text((20, y_offset), content, fill='#666666', font=font)
                    y_offset += 25
            else:
                draw.text((20, 20), "PPT模板预览", fill='#333333', font=title_font)
            
            # 保存预览图片
            preview_path = "debug_direct_preview.png"
            img.save(preview_path, 'PNG', quality=85)
            print(f"✅ 直接预览图片已保存: {preview_path}")
            
            # 检查文件大小
            if os.path.exists(preview_path):
                size = os.path.getsize(preview_path)
                print(f"文件大小: {size} bytes")
                
                if background_applied and size > 50000:  # 50KB
                    print("✅ 预览图片包含背景，大小正常")
                elif background_applied:
                    print("⚠️ 预览图片包含背景，但大小偏小")
                else:
                    print("ℹ️ 预览图片使用默认背景")
        
    except Exception as e:
        print(f"❌ 直接预览生成失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    print("=== 直接预览生成测试 ===")
    generate_preview_directly()
    print("\n测试完成!")
