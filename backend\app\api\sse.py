"""
SSE流式API路由
提供Server-Sent Events接口用于实时PPT生成
"""

from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import StreamingResponse, HTMLResponse
from pydantic import BaseModel
from typing import Optional
import logging
import json
import asyncio
import time

from ..services.sse_generator import sse_generator

logger = logging.getLogger(__name__)
router = APIRouter()

class StreamGenerationRequest(BaseModel):
    """流式生成请求"""
    content: str
    template_id: str
    user_input: Optional[str] = ""
    session_id: Optional[str] = ""

@router.get("/stream/{template_id}")
async def stream_generation(
    template_id: str,
    content: str = Query(..., description="要生成的内容"),
    user_input: str = Query("", description="用户输入上下文")
):
    """
    SSE流式生成PPT内容
    
    Args:
        template_id: 模板ID
        content: 要生成的内容
        user_input: 用户输入上下文
        
    Returns:
        StreamingResponse: SSE数据流
    """
    try:
        logger.info(f"开始SSE流式生成: template_id={template_id}, content_length={len(content)}")
        
        # 验证模板ID
        if not template_id:
            raise HTTPException(status_code=400, detail="模板ID不能为空")
        
        # 验证内容
        if not content.strip():
            raise HTTPException(status_code=400, detail="生成内容不能为空")
        
        # 暂时使用简单的模板信息，避免导入问题
        template_schema = {
            'template_id': template_id,
            'template_name': f'模板_{template_id}',
            'style': 'business',
            'recommended_slides': 5
        }

        logger.info(f"使用简化模板信息: {template_id}")

        # 创建SSE流
        async def event_stream():
            # 使用专业PPT生成器流式生成方法
            async for data in sse_generator.generate_professional_stream(template_id, content, user_input, apply_patches=True):
                yield data
        
        return StreamingResponse(
            event_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control"
            }
        )
        
    except Exception as e:
        logger.error(f"SSE流式生成失败: {e}")
        raise HTTPException(status_code=500, detail=f"流式生成失败: {str(e)}")

@router.post("/stream")
async def stream_generation_post(request: StreamGenerationRequest):
    """
    POST方式的SSE流式生成
    
    Args:
        request: 流式生成请求
        
    Returns:
        StreamingResponse: SSE数据流
    """
    try:
        logger.info(f"开始POST SSE流式生成: template_id={request.template_id}")
        
        # 验证请求
        if not request.template_id:
            raise HTTPException(status_code=400, detail="模板ID不能为空")
        
        if not request.content.strip():
            raise HTTPException(status_code=400, detail="生成内容不能为空")
        
        # 暂时使用简化的模板信息，避免导入问题
        template_schema = {
            'template_id': request.template_id,
            'template_name': f'模板_{request.template_id}',
            'style': 'business',
            'recommended_slides': 5
        }

        logger.info(f"使用简化模板信息: {request.template_id}")

        # 创建SSE流
        async def event_stream():
            async for data in sse_generator.generate_enhanced_stream(
                request.user_input,
                request.template_id,
                template_schema
            ):
                yield data
        
        return StreamingResponse(
            event_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control"
            }
        )
        
    except Exception as e:
        logger.error(f"POST SSE流式生成失败: {e}")
        raise HTTPException(status_code=500, detail=f"流式生成失败: {str(e)}")

@router.post("/stop")
async def stop_generation():
    """
    停止当前的流式生成过程
    
    Returns:
        dict: 停止结果
    """
    try:
        logger.info("收到停止生成请求")
        sse_generator.stop_generation()
        
        return {
            "success": True,
            "message": "生成过程已停止"
        }
        
    except Exception as e:
        logger.error(f"停止生成失败: {e}")
        raise HTTPException(status_code=500, detail=f"停止生成失败: {str(e)}")

@router.get("/status")
async def get_generation_status():
    """
    获取当前生成状态
    
    Returns:
        dict: 生成状态信息
    """
    try:
        return {
            "is_generating": sse_generator.is_generating,
            "current_session": sse_generator.current_session,
            "status": "generating" if sse_generator.is_generating else "idle"
        }
        
    except Exception as e:
        logger.error(f"获取生成状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")

@router.get("/test")
async def test_sse():
    """
    测试SSE连接
    
    Returns:
        StreamingResponse: 测试SSE数据流
    """
    try:
        logger.info("开始SSE连接测试")
        
        async def test_stream():
            import time
            import json
            
            for i in range(5):
                data = {
                    "type": "test",
                    "message": f"测试消息 {i + 1}",
                    "progress": (i + 1) * 20,
                    "timestamp": time.time()
                }
                yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
                
                # 等待1秒
                import asyncio
                await asyncio.sleep(1)
            
            # 发送完成消息
            final_data = {
                "type": "complete",
                "message": "SSE测试完成",
                "progress": 100,
                "timestamp": time.time()
            }
            yield f"data: {json.dumps(final_data, ensure_ascii=False)}\n\n"
        
        return StreamingResponse(
            test_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control"
            }
        )
        
    except Exception as e:
        logger.error(f"SSE测试失败: {e}")
        raise HTTPException(status_code=500, detail=f"SSE测试失败: {str(e)}")


@router.get("/preview/{session_id}")
async def get_preview(session_id: str):
    """获取生成的预览内容"""
    try:
        # 从生成器获取预览内容
        if hasattr(sse_generator, 'last_preview_content'):
            content = sse_generator.last_preview_content
            if content:
                return HTMLResponse(content=content)

        # 如果没有预览内容，返回默认页面
        default_content = """
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>预览不可用</title>
            <style>
                body {
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                    margin: 0;
                    padding: 40px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                .container {
                    background: white;
                    border-radius: 15px;
                    padding: 40px;
                    text-align: center;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                }
                h1 { color: #2c3e50; margin-bottom: 20px; }
                p { color: #6c757d; font-size: 1.1em; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🔄 预览生成中</h1>
                <p>预览内容正在生成中，请稍后刷新页面...</p>
                <p>会话ID: {session_id}</p>
            </div>
        </body>
        </html>
        """.format(session_id=session_id)

        return HTMLResponse(content=default_content)

    except Exception as e:
        logger.error(f"获取预览失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取预览失败: {str(e)}")


# MD2PPTX GET API已删除，统一使用专业PPT生成器


# MD2PPTX POST API已删除，统一使用专业PPT生成器


class ChatStreamRequest(BaseModel):
    """流式对话请求"""
    user_input: str
    conversation_history: Optional[list] = []
    template_id: Optional[str] = None


@router.post("/chat/stream")
async def stream_chat(request: ChatStreamRequest):
    """
    流式对话API

    Args:
        request: 对话请求

    Returns:
        StreamingResponse: SSE数据流
    """
    try:
        logger.info(f"开始流式对话: {request.user_input[:100]}...")

        # 导入chat_service
        from ..services.chat_service import chat_service

        # 创建流式生成器
        async def chat_stream():
            try:
                # 发送开始事件
                yield f"data: {json.dumps({'type': 'start', 'message': '正在思考中...'})}\n\n"

                # 调用LLM进行流式对话
                response = chat_service.template_aware_chat(
                    user_input=request.user_input,
                    conversation_history=request.conversation_history
                )

                # 模拟流式输出（将完整响应分块发送）
                if response and response.get('message'):
                    message = response['message']
                    chunk_size = 20  # 每次发送20个字符

                    for i in range(0, len(message), chunk_size):
                        chunk = message[i:i + chunk_size]
                        yield f"data: {json.dumps({'type': 'chunk', 'content': chunk})}\n\n"
                        await asyncio.sleep(0.05)  # 模拟打字效果

                # 发送完成事件
                yield f"data: {json.dumps({'type': 'complete', 'message': '对话完成'})}\n\n"

            except Exception as e:
                logger.error(f"流式对话过程中出错: {e}")
                error_data = {
                    "type": "error",
                    "error": str(e),
                    "message": "对话过程中出现错误"
                }
                yield f"data: {json.dumps(error_data)}\n\n"

        return StreamingResponse(
            chat_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*"
            }
        )

    except Exception as e:
        logger.error(f"流式对话失败: {e}")
        raise HTTPException(status_code=500, detail=f"流式对话失败: {str(e)}")
