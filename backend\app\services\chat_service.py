"""
对话服务
处理用户对话，生成PPT大纲和内容
"""
import json
import logging
import sys
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple

# 添加LLM客户端路径
current_file = Path(__file__).resolve()
# 从 backend/app/services/chat_service.py 到 llm/ 需要向上4级
llm_path = current_file.parent.parent.parent.parent / "llm"
llm_path = llm_path.resolve()

if str(llm_path) not in sys.path:
    sys.path.insert(0, str(llm_path))

# 添加backend路径以导入模板管理器
backend_path = current_file.parent.parent.parent
if str(backend_path) not in sys.path:
    sys.path.insert(0, str(backend_path))

try:
    # 直接导入LLM客户端管理器
    import llm_client_manager
    LLMClient = llm_client_manager.LLMClient
    logging.info("LLM客户端导入成功")
except ImportError as e:
    logging.error(f"LLM客户端导入失败: {e}")
    LLMClient = None

try:
    from services.template_manager import template_manager
except ImportError as e:
    logging.error(f"无法导入模板管理器: {e}")
    template_manager = None

logger = logging.getLogger(__name__)

class ChatService:
    """对话服务类"""

    def __init__(self):
        """初始化对话服务"""
        self.llm_client = None
        self._init_llm_client()
        self.conversation_history = []
        self.current_template = None  # 当前选择的模板
        self.template_context = None  # 模板上下文信息
        logger.info("ChatService初始化完成")
    
    def _init_llm_client(self):
        """初始化LLM客户端"""
        try:
            # 导入LLM客户端 - 使用已经设置好的路径
            import llm_client_manager
            LLMClient = llm_client_manager.LLMClient
        except ImportError as e:
            logger.error(f"LLM客户端导入失败: {e}")
            self.llm_client = None
            return

        try:
            # 简化LLM客户端初始化，使用默认配置
            logger.info("初始化LLM客户端...")

            # 尝试多种配置导入方式
            config = None

            # 从统一配置加载器获取LLM配置
            try:
                import sys
                root_dir = Path(__file__).parent.parent.parent.parent
                if str(root_dir) not in sys.path:
                    sys.path.insert(0, str(root_dir))

                from config_loader import config as global_config
                config = global_config.get_llm_config()
                logger.info("成功从统一配置导入LLM配置")
            except ImportError as e:
                logger.warning(f"无法从统一配置导入LLM配置: {e}")
                config = None

            # 使用默认配置作为备用
            if config is None or not config:
                config = {
                    'api_key': 'sk-2h1RwdRjqYc6FosI3aus',
                    'model': 'deepseek-v3-0324',
                    'api_base': 'http://192.168.78.35/gateway/ai-service/v1',
                    'timeout': 180
                }
                logger.info("使用默认LLM配置")

            # 初始化LLM客户端
            self.llm_client = LLMClient(config)
            logger.info("LLM客户端初始化成功")

        except Exception as e:
            logger.error(f"初始化LLM客户端失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            self.llm_client = None

    def set_template(self, template_id: str, user_input: str = "") -> Dict:
        """
        设置当前使用的模板

        Args:
            template_id: 模板ID
            user_input: 用户输入（用于生成动态提示词）

        Returns:
            Dict: 模板信息和AI提示词
        """
        if not template_manager:
            raise RuntimeError("模板管理器未初始化")

        try:
            # 获取模板详情
            template_details = template_manager.get_template_details(template_id)
            if not template_details:
                raise ValueError(f"模板 {template_id} 不存在")

            # 获取AI提示词（检测是否应该使用新系统）
            use_new_system = self._should_use_new_system()
            # 如果有用户输入，使用包含当前输入的上下文构建方法
            if user_input:
                user_context = self._build_user_context_for_ppt(user_input)
            else:
                user_context = self._build_user_context_for_prompt()
            ai_prompt = template_manager.generate_ai_prompt(template_id, use_new_system=use_new_system, user_context=user_context)

            # 设置当前模板
            self.current_template = template_id
            self.template_context = {
                "template_details": template_details,
                "ai_prompt": ai_prompt,
                "md2pptx_mapping": template_details.get("md2pptx_mapping", {}),
                "recommended_metadata": template_details.get("recommended_metadata", {})
            }

            logger.info(f"模板设置成功: {template_id}")
            return {
                "template_id": template_id,
                "template_name": template_details.get("name", ""),
                "ai_prompt": ai_prompt,
                "layout_count": len(template_details.get("slide_layouts", [])),
                "md2pptx_mapping": template_details.get("md2pptx_mapping", {})
            }

        except Exception as e:
            logger.error(f"设置模板失败: {e}")
            raise

    def _should_use_new_system(self) -> bool:
        """检测是否应该使用新的基于成品PPT模板的系统"""
        # 这里可以添加更复杂的检测逻辑
        # 目前简单返回True，表示优先使用新系统
        return True

    def _build_user_context_for_prompt(self) -> str:
        """为提示词构建用户上下文（最新3000字内容）"""
        context_parts = []

        # 添加最近的对话历史
        recent_history = self.conversation_history[-5:] if self.conversation_history else []

        for i, item in enumerate(recent_history):
            if item.get("user_input"):
                context_parts.append(f"用户: {item['user_input']}")
            if item.get("response"):
                response_text = str(item['response'])
                # 限制单条回复长度
                if len(response_text) > 500:
                    response_text = response_text[:500] + "..."
                context_parts.append(f"助手: {response_text}")

        # 合并所有上下文
        full_context = "\n".join(context_parts)

        # 限制总长度为3000字
        if len(full_context) > 3000:
            full_context = full_context[-3000:]

        return full_context if full_context else "用户需要生成一个专业的PPT演示文稿。"

    def get_available_templates(self) -> List[Dict]:
        """
        获取可用的模板列表

        Returns:
            List[Dict]: 模板列表
        """
        if not template_manager:
            return []

        try:
            templates = template_manager.get_template_list()
            return templates
        except Exception as e:
            logger.error(f"获取模板列表失败: {e}")
            return []

    def template_aware_chat(self, user_input: str, conversation_history: List[Dict] = None) -> Dict:
        """
        模板感知的对话功能

        Args:
            user_input: 用户输入
            conversation_history: 对话历史

        Returns:
            Dict: AI回复和相关信息
        """
        if not self.llm_client:
            raise RuntimeError("LLM客户端未初始化")

        try:
            # 检查是否需要生成PPT（优先检测，避免无效的AI调用）
            if self._should_generate_ppt(user_input, ""):
                logger.info("检测到PPT生成需求，直接调用新的基于成品PPT模板的生成器")

                try:
                    # 先添加当前用户输入到对话历史，以便构建正确的上下文
                    self.conversation_history.append({
                        "type": "ppt_request",
                        "user_input": user_input,
                        "template_id": self.current_template,
                        "timestamp": time.time()
                    })

                    # 直接调用新的模板PPT生成器，使用动态提示词
                    ppt_result = self._generate_ppt_with_new_system(user_input)

                    if ppt_result.get("success"):
                        # 生成成功的回复消息
                        success_message = f"✅ PPT生成成功！\n\n📄 文件信息：\n- 文件名：{ppt_result['filename']}\n- 页数：{ppt_result['total_pages']}页\n- 占位符：{ppt_result['total_placeholders']}个\n- 文件大小：{ppt_result['file_size']/1024/1024:.1f} MB\n\n🔗 下载链接：{ppt_result['download_url']}"

                        # 记录对话历史
                        self.conversation_history.append({
                            "type": "ppt_generation",
                            "user_input": user_input,
                            "template_id": self.current_template,
                            "response": success_message,
                            "ppt_result": ppt_result
                        })

                        return {
                            "message": success_message,
                            "template_id": self.current_template,
                            "template_context": self.template_context is not None,
                            "ppt_generated": True,
                            "ppt_result": ppt_result
                        }
                    else:
                        error_message = f"❌ PPT生成失败：{ppt_result.get('error', '未知错误')}"
                        logger.warning(f"新PPT生成器失败: {ppt_result.get('error')}")

                        return {
                            "message": error_message,
                            "template_id": self.current_template,
                            "template_context": self.template_context is not None,
                            "ppt_generated": False,
                            "error": ppt_result.get('error')
                        }

                except Exception as e:
                    error_message = f"❌ PPT生成异常：{str(e)}"
                    logger.error(f"新PPT生成器调用失败: {e}")

                    return {
                        "message": error_message,
                        "template_id": self.current_template,
                        "template_context": self.template_context is not None,
                        "ppt_generated": False,
                        "error": str(e)
                    }

            # 如果不是PPT生成需求，进行正常的对话
            # 构建模板感知的提示词
            prompt = self._build_template_aware_prompt(user_input, conversation_history)

            # 调用LLM
            response = self.llm_client.call_model(
                prompt=prompt,
                temperature=0.7,
                max_tokens=4000
            )

            # 记录对话历史
            self.conversation_history.append({
                "type": "template_aware_chat",
                "user_input": user_input,
                "template_id": self.current_template,
                "response": response
            })

            logger.info(f"模板感知对话成功，响应长度: {len(response) if response else 0}")
            logger.debug(f"AI响应内容: {response[:300] if response else 'None'}...")

            return {
                "message": response,
                "template_id": self.current_template,
                "template_context": self.template_context is not None
            }

        except Exception as e:
            logger.error(f"模板感知对话失败: {e}")
            raise

    def _should_generate_ppt(self, user_input: str, ai_response: str) -> bool:
        """判断是否需要生成PPT"""
        # 检查用户输入中的关键词
        ppt_keywords = [
            "生成ppt", "生成PPT", "制作ppt", "制作PPT",
            "生成演示", "生成幻灯片", "基于上面的说明生成一份ppt",
            "生成一份ppt", "做个ppt", "做个PPT"
        ]

        user_lower = user_input.lower()
        for keyword in ppt_keywords:
            if keyword.lower() in user_lower:
                return True

        return False

    def _generate_ppt_with_new_system(self, user_input: str) -> Dict:
        """使用新的基于成品PPT模板的系统生成PPT"""
        try:
            # 导入新的模板PPT生成器
            from app.services.template_based_ppt_generator import template_ppt_generator

            if not self.current_template:
                return {
                    "success": False,
                    "error": "未选择模板，无法生成PPT"
                }

            # 构建用户上下文（包含对话历史）
            user_context = self._build_user_context_for_ppt(user_input)

            # 生成输出文件名
            import time
            timestamp = int(time.time())
            output_filename = f"chat_generated_{timestamp}.pptx"

            logger.info(f"开始使用新系统生成PPT: 模板={self.current_template}, 文件={output_filename}")

            # 调用新的5步流程
            result = template_ppt_generator.generate_ppt_from_template(
                self.current_template, user_context, output_filename
            )

            if result.get("success"):
                logger.info(f"新系统PPT生成成功: {result['file_path']}")
                return {
                    "success": True,
                    "file_path": result["file_path"],
                    "file_size": result["file_size"],
                    "filename": result["filename"],
                    "total_pages": result.get("total_pages", 0),
                    "total_placeholders": result.get("total_placeholders", 0),
                    "download_url": f"/files/generated/{result['filename']}"
                }
            else:
                logger.error(f"新系统PPT生成失败: {result.get('error')}")
                return {
                    "success": False,
                    "error": result.get("error", "未知错误")
                }

        except Exception as e:
            logger.error(f"新系统PPT生成异常: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _build_user_context_for_ppt(self, user_input: str) -> str:
        """为PPT生成构建用户上下文"""
        context_parts = []

        # 添加当前用户输入
        context_parts.append(f"用户当前需求: {user_input}")

        # 添加最近的对话历史（最多3轮）
        recent_history = self.conversation_history[-3:] if self.conversation_history else []

        if recent_history:
            context_parts.append("\n对话历史:")
            for i, item in enumerate(recent_history):
                if item.get("user_input"):
                    context_parts.append(f"用户{i+1}: {item['user_input']}")
                if item.get("response"):
                    response_text = str(item['response'])  # 确保是字符串
                    context_parts.append(f"助手{i+1}: {response_text[:200]}...")

        # 限制总长度
        full_context = "\n".join(context_parts)
        if len(full_context) > 3000:
            full_context = full_context[-3000:]

        return full_context
    
    def generate_outline(self, user_input: str, context: str = "") -> Dict:
        """
        生成PPT大纲
        
        Args:
            user_input: 用户输入
            context: 上下文信息
            
        Returns:
            Dict: 生成的大纲
        """
        if not self.llm_client:
            raise RuntimeError("LLM客户端未初始化")
        
        try:
            prompt = self._build_outline_prompt(user_input, context)
            response = self.llm_client.call_model(
                prompt=prompt,
                temperature=0.7,
                max_tokens=4000
            )
            
            outline = self._parse_outline_response(response)
            
            # 记录对话历史
            self.conversation_history.append({
                "type": "outline_generation",
                "user_input": user_input,
                "response": outline
            })
            
            logger.info(f"大纲生成成功，包含{len(outline.get('slides', []))}页")
            return outline
            
        except Exception as e:
            logger.error(f"生成大纲失败: {e}")
            raise
    
    def refine_outline(self, current_outline: Dict, user_feedback: str) -> Dict:
        """
        根据用户反馈优化大纲
        
        Args:
            current_outline: 当前大纲
            user_feedback: 用户反馈
            
        Returns:
            Dict: 优化后的大纲
        """
        if not self.llm_client:
            raise RuntimeError("LLM客户端未初始化")
        
        try:
            prompt = self._build_refine_prompt(current_outline, user_feedback)
            response = self.llm_client.call_model(
                prompt=prompt,
                temperature=0.5,
                max_tokens=4000
            )
            
            refined_outline = self._parse_outline_response(response)
            
            # 记录对话历史
            self.conversation_history.append({
                "type": "outline_refinement",
                "user_feedback": user_feedback,
                "original_outline": current_outline,
                "refined_outline": refined_outline
            })
            
            logger.info("大纲优化成功")
            return refined_outline
            
        except Exception as e:
            logger.error(f"优化大纲失败: {e}")
            raise
    
    def generate_content(self, outline: Dict) -> Dict:
        """
        根据大纲生成详细内容
        
        Args:
            outline: PPT大纲
            
        Returns:
            Dict: 包含详细内容的大纲
        """
        if not self.llm_client:
            raise RuntimeError("LLM客户端未初始化")
        
        try:
            prompt = self._build_content_prompt(outline)
            response = self.llm_client.call_model(
                prompt=prompt,
                temperature=0.6,
                max_tokens=6000
            )
            
            detailed_outline = self._parse_content_response(response, outline)
            
            logger.info("内容生成成功")
            return detailed_outline
            
        except Exception as e:
            logger.error(f"生成内容失败: {e}")
            raise
    
    def _build_outline_prompt(self, user_input: str, context: str = "") -> str:
        """构建大纲生成提示词"""
        prompt = f"""你是一个专业的PPT制作助手。请根据用户需求生成一个结构清晰的PPT大纲。

用户需求：{user_input}

{f"上下文信息：{context}" if context else ""}

请生成一个JSON格式的PPT大纲，包含以下结构：
{{
    "title": "PPT标题",
    "subtitle": "副标题（可选）",
    "slides": [
        {{
            "title": "幻灯片标题",
            "content": ["要点1", "要点2", "要点3"]
        }}
    ]
}}

要求：
1. 大纲应该逻辑清晰，层次分明
2. 每页幻灯片应该有明确的主题
3. 内容要点应该简洁明了
4. 总页数控制在5-15页之间
5. 必须返回有效的JSON格式

请直接返回JSON，不要包含其他文字说明。"""
        
        return prompt
    
    def _build_refine_prompt(self, current_outline: Dict, user_feedback: str) -> str:
        """构建大纲优化提示词"""
        outline_str = json.dumps(current_outline, ensure_ascii=False, indent=2)
        
        prompt = f"""请根据用户反馈优化以下PPT大纲。

当前大纲：
{outline_str}

用户反馈：{user_feedback}

请根据反馈调整大纲，保持JSON格式不变。要求：
1. 充分考虑用户的反馈意见
2. 保持大纲的逻辑性和完整性
3. 适当调整内容结构和要点
4. 确保返回有效的JSON格式

请直接返回优化后的JSON大纲，不要包含其他文字说明。"""
        
        return prompt
    
    def _build_content_prompt(self, outline: Dict) -> str:
        """构建内容生成提示词"""
        outline_str = json.dumps(outline, ensure_ascii=False, indent=2)
        
        prompt = f"""请为以下PPT大纲生成详细内容。

大纲：
{outline_str}

请为每个要点生成更详细的内容，保持JSON格式，扩展content字段：
{{
    "title": "PPT标题",
    "subtitle": "副标题",
    "slides": [
        {{
            "title": "幻灯片标题",
            "content": [
                {{
                    "type": "text",
                    "content": "详细的文字内容"
                }},
                {{
                    "type": "bullet",
                    "content": "要点内容"
                }}
            ]
        }}
    ]
}}

要求：
1. 为每个要点生成具体、有价值的内容
2. 内容应该专业、准确、有说服力
3. 适当使用不同的内容类型（text, bullet等）
4. 保持JSON格式的正确性

请直接返回包含详细内容的JSON，不要包含其他文字说明。"""
        
        return prompt
    
    def _parse_outline_response(self, response: str) -> Dict:
        """解析大纲响应"""
        try:
            # 尝试提取JSON部分
            response = response.strip()
            
            # 查找JSON开始和结束位置
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1
            
            if start_idx == -1 or end_idx == 0:
                raise ValueError("响应中未找到有效的JSON")
            
            json_str = response[start_idx:end_idx]
            outline = json.loads(json_str)
            
            # 验证必要字段
            if "title" not in outline:
                outline["title"] = "PPT演示"
            if "slides" not in outline:
                outline["slides"] = []
            
            return outline
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            # 返回默认大纲
            return {
                "title": "PPT演示",
                "subtitle": "",
                "slides": [
                    {
                        "title": "概述",
                        "content": ["请重新生成大纲"]
                    }
                ]
            }
        except Exception as e:
            logger.error(f"解析大纲响应失败: {e}")
            raise
    
    def _parse_content_response(self, response: str, original_outline: Dict) -> Dict:
        """解析内容响应"""
        try:
            detailed_outline = self._parse_outline_response(response)
            
            # 如果解析失败，返回原始大纲
            if not detailed_outline.get("slides"):
                return original_outline
            
            return detailed_outline
            
        except Exception as e:
            logger.error(f"解析内容响应失败: {e}")
            return original_outline
    
    def get_conversation_history(self) -> List[Dict]:
        """获取对话历史"""
        return self.conversation_history.copy()
    
    def clear_conversation_history(self):
        """清空对话历史"""
        self.conversation_history.clear()
        logger.info("对话历史已清空")
    
    def test_connection(self) -> Tuple[bool, str]:
        """测试LLM连接"""
        if not self.llm_client:
            return False, "LLM客户端未初始化"

        try:
            success, message = self.llm_client.test_connection()
            return success, message
        except Exception as e:
            return False, f"连接测试失败: {e}"

    def _build_template_aware_prompt(self, user_input: str, conversation_history: List[Dict] = None) -> str:
        """构建模板感知的提示词"""

        # 基础提示词
        base_prompt = """你是一个专业的PPT生成助手，专门帮助用户基于特定的PPT模板生成高质量的演示文稿内容。"""

        # 调试信息
        logger.info(f"构建提示词 - template_context存在: {self.template_context is not None}")
        if self.template_context:
            logger.info(f"模板上下文: {self.template_context.get('template_details', {}).get('name', '未知')}")

        # 模板上下文
        template_context = ""
        if self.template_context:
            template_details = self.template_context["template_details"]
            ai_prompt = self.template_context["ai_prompt"]
            md2pptx_mapping = self.template_context["md2pptx_mapping"]

            template_context = f"""
## 当前使用的PPT模板信息：
- 模板名称：{template_details.get('name', '未知')}
- 模板描述：{template_details.get('description', '无描述')}
- 可用布局数量：{len(template_details.get('slide_layouts', []))}
- md2pptx布局映射：{md2pptx_mapping}

## 模板专用指导：
{ai_prompt}

## 重要要求：
1. 严格按照md2pptx语法生成内容
2. 使用模板支持的布局类型
3. 确保生成的内容与模板风格匹配
4. 优先使用模板推荐的元数据配置
"""
        else:
            template_context = """
## 注意：
当前未选择PPT模板。建议用户先选择一个合适的模板，以获得更好的生成效果。
"""

        # 对话历史
        history_context = ""
        if conversation_history:
            history_context = "\n## 对话历史：\n"
            for msg in conversation_history[-5:]:  # 只保留最近5条
                role = msg.get('role', 'user')
                content = msg.get('content', '')
                history_context += f"{role}: {content}\n"

        # 组合完整提示词
        full_prompt = f"""{base_prompt}

{template_context}

{history_context}

## 用户当前请求：
{user_input}

请基于以上模板信息和对话历史，为用户提供专业、准确的回答。如果用户要求生成PPT内容，请严格按照md2pptx语法和模板要求生成。"""

        return full_prompt
