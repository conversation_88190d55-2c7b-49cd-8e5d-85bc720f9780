#!/usr/bin/env python3
"""
LibreOffice PPT预览生成器
"""

import os
import subprocess
import tempfile
import logging
from pathlib import Path
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)

class LibreOfficeConverter:
    """LibreOffice转换器，用于生成PPT预览图片"""
    
    def __init__(self):
        self.libreoffice_paths = [
            "C:/Program Files/LibreOffice/program/soffice.exe",
            "C:/Program Files (x86)/LibreOffice/program/soffice.exe",
            "/usr/bin/libreoffice",  # Linux
            "/usr/bin/soffice",      # Linux alternative
            "/Applications/LibreOffice.app/Contents/MacOS/soffice",  # macOS
        ]
        self.soffice_path = self._find_libreoffice()
    
    def _find_libreoffice(self) -> Optional[str]:
        """查找LibreOffice安装路径"""
        for path in self.libreoffice_paths:
            if os.path.exists(path):
                logger.info(f"找到LibreOffice: {path}")
                return path
        
        logger.warning("未找到LibreOffice安装")
        return None
    
    def is_available(self) -> bool:
        """检查LibreOffice是否可用"""
        return self.soffice_path is not None
    
    def convert_ppt_to_image(self, ppt_path: str, output_dir: str, 
                           format: str = "png", page: int = 1) -> Optional[str]:
        """
        将PPT转换为图片
        
        Args:
            ppt_path: PPT文件路径
            output_dir: 输出目录
            format: 输出格式 (png, jpg, pdf)
            page: 要转换的页面（从1开始）
            
        Returns:
            生成的图片文件路径，失败返回None
        """
        if not self.is_available():
            logger.error("LibreOffice不可用")
            return None
        
        if not os.path.exists(ppt_path):
            logger.error(f"PPT文件不存在: {ppt_path}")
            return None
        
        try:
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)
            
            # 构建LibreOffice命令
            cmd = [
                self.soffice_path,
                "--headless",  # 无头模式
                "--invisible",  # 不显示界面
                "--nodefault",  # 不加载默认文档
                "--nolockcheck",  # 不检查文件锁
                "--nologo",  # 不显示启动画面
                "--norestore",  # 不恢复上次会话
                "--convert-to", f"{format}",
                "--outdir", output_dir,
                ppt_path
            ]
            
            logger.info(f"执行LibreOffice转换: {' '.join(cmd)}")
            
            # 执行转换
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=60,  # 60秒超时
                cwd=output_dir
            )
            
            if result.returncode == 0:
                # 查找生成的文件
                ppt_name = Path(ppt_path).stem
                output_file = os.path.join(output_dir, f"{ppt_name}.{format}")
                
                if os.path.exists(output_file):
                    logger.info(f"转换成功: {output_file}")
                    return output_file
                else:
                    logger.error(f"转换完成但找不到输出文件: {output_file}")
                    return None
            else:
                logger.error(f"LibreOffice转换失败: {result.stderr}")
                return None
                
        except subprocess.TimeoutExpired:
            logger.error("LibreOffice转换超时")
            return None
        except Exception as e:
            logger.error(f"LibreOffice转换异常: {e}")
            return None
    
    def generate_preview(self, ppt_path: str, preview_dir: str, 
                        template_id: str) -> Optional[str]:
        """
        生成PPT预览图片
        
        Args:
            ppt_path: PPT文件路径
            preview_dir: 预览图片目录
            template_id: 模板ID
            
        Returns:
            预览图片的相对路径，失败返回None
        """
        try:
            # 创建临时目录用于转换
            with tempfile.TemporaryDirectory() as temp_dir:
                # 转换PPT第一页为PNG
                converted_file = self.convert_ppt_to_image(
                    ppt_path=ppt_path,
                    output_dir=temp_dir,
                    format="png",
                    page=1
                )
                
                if not converted_file:
                    return None
                
                # 移动到预览目录并重命名
                preview_filename = f"{template_id}_preview.png"
                preview_path = os.path.join(preview_dir, preview_filename)
                
                # 确保预览目录存在
                os.makedirs(preview_dir, exist_ok=True)
                
                # 复制文件
                import shutil
                shutil.copy2(converted_file, preview_path)
                
                logger.info(f"预览图片生成成功: {preview_path}")
                
                # 返回相对路径
                return f"previews/{preview_filename}"
                
        except Exception as e:
            logger.error(f"生成预览图片失败: {e}")
            return None
    
    def test_conversion(self) -> Dict[str, Any]:
        """测试LibreOffice转换功能"""
        result = {
            "available": self.is_available(),
            "path": self.soffice_path,
            "test_passed": False,
            "error": None
        }
        
        if not self.is_available():
            result["error"] = "LibreOffice未安装或未找到"
            return result
        
        try:
            # 测试LibreOffice版本
            cmd = [self.soffice_path, "--version"]
            version_result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if version_result.returncode == 0:
                result["version"] = version_result.stdout.strip()
                result["test_passed"] = True
                logger.info(f"LibreOffice测试通过: {result['version']}")
            else:
                result["error"] = f"版本检查失败: {version_result.stderr}"
                
        except Exception as e:
            result["error"] = f"测试失败: {str(e)}"
        
        return result

# 创建全局实例
libreoffice_converter = LibreOfficeConverter()
