#!/usr/bin/env python3
"""
完整的PPT生成助手服务器
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 添加LLM客户端路径（指向根目录的llm文件夹）
llm_path = current_dir.parent / "llm"
sys.path.insert(0, str(llm_path))

# 添加moffee路径
moffee_path = current_dir.parent / "moffee-base"
sys.path.insert(0, str(moffee_path))

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
import uvicorn
import logging
import os

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入模板路由
try:
    from app.template_routes import router as template_router
    template_router_available = True
    logger.info("模板路由导入成功")

    # 测试模板管理器
    from services.template_manager import template_manager
    templates = template_manager.get_template_list()
    logger.info(f"启动时模板管理器状态: {len(templates)} 个模板")
    if templates:
        first_template = templates[0]
        logger.info(f"第一个模板字段: {list(first_template.keys())}")
        logger.info(f"是否有status字段: {'status' in first_template}")

except ImportError as e:
    logger.warning(f"模板路由导入失败: {e}")
    template_router_available = False

# 导入chat路由
try:
    from app.api.chat import router as chat_router
    chat_router_available = True
    logger.info("Chat路由导入成功")
except ImportError as e:
    logger.warning(f"Chat路由导入失败: {e}")
    chat_router_available = False

# 导入SSE路由
try:
    from app.api.sse import router as sse_router
    sse_router_available = True
    logger.info("SSE路由导入成功")
except ImportError as e:
    logger.warning(f"SSE路由导入失败: {e}")
    sse_router_available = False

# 导入编辑器路由
try:
    from app.api.editor import router as editor_router
    editor_router_available = True
    logger.info("编辑器路由导入成功")
except ImportError as e:
    logger.warning(f"编辑器路由导入失败: {e}")
    editor_router_available = False

# 导入生成路由
try:
    from app.api.generate import router as generate_router
    generate_router_available = True
    logger.info("生成路由导入成功")
except ImportError as e:
    logger.warning(f"生成路由导入失败: {e}")
    generate_router_available = False

app = FastAPI(title="PPT生成助手", version="1.0.0")

# CORS配置
try:
    import sys
    import os
    # 添加根目录到Python路径
    root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    if root_dir not in sys.path:
        sys.path.insert(0, root_dir)

    from config_loader import settings
    cors_origins = settings.ALLOWED_ORIGINS
    logger.info(f"使用配置的CORS源: {cors_origins}")
except ImportError as e:
    cors_origins = ["*"]  # 默认允许所有来源
    logger.warning(f"配置导入失败: {e}，使用默认CORS配置")
except Exception as e:
    cors_origins = ["*"]  # 默认允许所有来源
    logger.warning(f"配置导入异常: {e}，使用默认CORS配置")

app.add_middleware(
    CORSMiddleware,
    allow_origins=cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 创建静态文件目录
static_dir = os.path.join(os.path.dirname(__file__), "static")
os.makedirs(static_dir, exist_ok=True)

# 创建uploads目录
uploads_dir = os.path.join(os.path.dirname(__file__), "uploads")
os.makedirs(uploads_dir, exist_ok=True)

# 挂载静态文件服务
app.mount("/static", StaticFiles(directory=static_dir), name="static")
app.mount("/files", StaticFiles(directory=uploads_dir), name="files")

# 注册模板路由
if template_router_available:
    app.include_router(template_router, prefix="/api/templates", tags=["模板管理"])
    logger.info("模板路由注册成功")
else:
    logger.warning("模板路由未注册")

# 注册chat路由
if chat_router_available:
    app.include_router(chat_router, prefix="/api/chat", tags=["智能对话"])
    logger.info("Chat路由注册成功")
else:
    logger.warning("Chat路由未注册")

# 注册SSE路由
if sse_router_available:
    app.include_router(sse_router, prefix="/api/sse", tags=["流式生成"])
    logger.info("SSE路由注册成功")
else:
    logger.warning("SSE路由未注册")

# 注册编辑器路由
if editor_router_available:
    app.include_router(editor_router, prefix="/api/editor", tags=["在线编辑"])
    logger.info("编辑器路由注册成功")
else:
    logger.warning("编辑器路由未注册")

# 注册生成路由
if generate_router_available:
    app.include_router(generate_router, prefix="/api/generate", tags=["PPT生成"])
    logger.info("生成路由注册成功")
else:
    logger.warning("生成路由未注册")

# 文件下载服务
@app.get("/files/{filename}")
async def download_file(filename: str):
    """下载生成的文件"""
    try:
        import os
        import tempfile
        from fastapi.responses import FileResponse

        # 在临时目录中查找文件
        temp_dir = tempfile.gettempdir()

        # 搜索所有临时目录中的文件
        for root, dirs, files in os.walk(temp_dir):
            if filename in files:
                file_path = os.path.join(root, filename)

                # 根据文件扩展名确定媒体类型
                if filename.endswith('.pptx'):
                    media_type = "application/vnd.openxmlformats-officedocument.presentationml.presentation"
                elif filename.endswith('.pdf'):
                    media_type = "application/pdf"
                elif filename.endswith('.html'):
                    media_type = "text/html"
                else:
                    media_type = "application/octet-stream"

                return FileResponse(
                    path=file_path,
                    filename=filename,
                    media_type=media_type
                )

        # 如果找不到文件，返回404
        raise HTTPException(status_code=404, detail=f"文件 {filename} 未找到")

    except Exception as e:
        logger.error(f"下载文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"下载文件失败: {str(e)}")

# 数据模型
class ChatRequest(BaseModel):
    message: str
    conversation_history: list = []

class OutlineRequest(BaseModel):
    user_input: str
    context: str = ""

class RefineRequest(BaseModel):
    current_outline: str
    user_feedback: str

class GenerateRequest(BaseModel):
    outline: str
    theme: str = "default"
    format: str = "pdf"  # pdf, html, 或 pptx
    formats: list = []  # 支持多格式导出
    return_info: bool = False  # 是否返回文件信息而非直接下载

# 基础路由
@app.get("/")
async def root():
    return {"message": "PPT生成助手API服务正在运行"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "ppt-generator"}

# 获取可用主题
@app.get("/api/generate/themes")
async def get_themes():
    try:
        themes = [
            {"name": "default", "display_name": "默认主题", "description": "简洁的默认样式", "color": "#6B7280"},
            {"name": "beam", "display_name": "Beam主题", "description": "现代商务风格", "color": "#3B82F6"},
            {"name": "robo", "display_name": "Robo主题", "description": "科技感设计", "color": "#8B5CF6"},
            {"name": "blue", "display_name": "蓝色主题", "description": "专业蓝色调", "color": "#1E40AF"},
            {"name": "gaia", "display_name": "Gaia主题", "description": "自然绿色调", "color": "#059669"}
        ]

        logger.info(f"返回主题列表: {len(themes)}个主题")
        return {
            "success": True,
            "data": {
                "themes": themes
            },
            "message": "获取主题列表成功"
        }
    except Exception as e:
        logger.error(f"获取主题列表失败: {str(e)}")
        return {
            "success": False,
            "message": f"获取主题列表失败: {str(e)}"
        }

# 通用聊天API
@app.post("/api/chat")
async def chat(request: ChatRequest):
    try:
        logger.info(f"聊天请求: {request.message}")

        # 调用真实的LLM进行对话
        try:
            from llm import llm_client_manager
            from config_loader import config
            MODEL_CONFIG = config.get_llm_config()

            # 创建LLM客户端
            llm_client = llm_client_manager.LLMClient(MODEL_CONFIG)

            # 构建对话上下文
            conversation_context = ""
            if request.conversation_history:
                for msg in request.conversation_history[-5:]:  # 只保留最近5条消息
                    role = msg.get('role', 'user')
                    content = msg.get('content', '')
                    conversation_context += f"{role}: {content}\n"

            # 构建提示词
            prompt = f"""你是一个专业的PPT生成助手。请根据用户的需求，帮助他们生成PPT大纲或回答相关问题。

对话历史：
{conversation_context}

用户当前问题：{request.message}

请提供专业、有用的回答。如果用户想要生成PPT，请询问具体的主题和要求。"""

            # 调用大模型
            response = llm_client.call_model(prompt, temperature=0.7, max_tokens=2000)

            if response and response.strip():
                ai_response = response.strip()
                logger.info(f"LLM对话成功，响应长度: {len(ai_response)}")
            else:
                ai_response = "我是PPT生成助手，请告诉我您想要制作什么主题的PPT，我会帮您生成专业的大纲。"
                logger.warning("LLM返回空结果，使用默认回复")

        except Exception as llm_error:
            logger.error(f"LLM调用失败: {llm_error}")
            ai_response = "我是PPT生成助手，请告诉我您想要制作什么主题的PPT，我会帮您生成专业的大纲。"

        return {
            "success": True,
            "data": {
                "message": ai_response,
                "role": "assistant"
            },
            "message": "对话成功"
        }
    except Exception as e:
        logger.error(f"聊天失败: {e}")
        return {
            "success": False,
            "message": f"聊天失败: {str(e)}"
        }

# 生成大纲
@app.post("/api/chat/generate-outline")
async def generate_outline(request: OutlineRequest):
    try:
        logger.info(f"生成大纲请求: {request.user_input}")

        # 调用真实的LLM生成大纲
        try:
            from llm import llm_client_manager
            from config_loader import config
            MODEL_CONFIG = config.get_llm_config()

            # 创建LLM客户端
            llm_client = llm_client_manager.LLMClient(MODEL_CONFIG)

            # 构建提示词
            prompt = f"""请为以下主题生成一个详细的PPT大纲，使用markdown格式：

主题：{request.user_input}
上下文：{request.context}

要求：
1. 使用markdown格式，以#开头作为标题
2. 包含4-6个主要章节
3. 每个章节下包含3-5个要点
4. 内容要专业、逻辑清晰
5. 适合制作成PPT演示

请直接返回markdown格式的大纲："""

            # 调用大模型
            response = llm_client.call_model(prompt, temperature=0.1, max_tokens=4000)

            if response and response.strip():
                outline = response.strip()
                logger.info(f"LLM生成大纲成功，长度: {len(outline)}")
            else:
                # 如果LLM返回空，使用备用大纲
                outline = f"""# {request.user_input}

## 1. 引言与背景
- 主题背景介绍
- 现状分析
- 研究意义

## 2. 核心概念
- 基本定义
- 关键特征
- 理论基础

## 3. 主要内容
- 核心要点分析
- 实际应用案例
- 技术实现方法

## 4. 发展趋势
- 当前发展状况
- 未来发展方向
- 机遇与挑战

## 5. 总结与展望
- 主要结论
- 实践建议
- 未来规划"""
                logger.warning("LLM返回空结果，使用备用大纲")

        except Exception as llm_error:
            logger.error(f"LLM调用失败: {llm_error}")
            # LLM调用失败时的备用大纲
            outline = f"""# {request.user_input}

## 1. 概述
- 主题介绍
- 背景分析
- 目标设定

## 2. 核心内容
- 关键概念
- 重要特点
- 应用场景

## 3. 深入分析
- 详细解析
- 案例研究
- 技术要点

## 4. 总结
- 主要收获
- 实践意义
- 发展前景"""

        return {
            "success": True,
            "data": {
                "outline": outline
            },
            "message": "大纲生成成功"
        }
    except Exception as e:
        logger.error(f"生成大纲失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 优化大纲
@app.post("/api/chat/refine-outline")
async def refine_outline(request: RefineRequest):
    try:
        logger.info(f"优化大纲请求: {request.user_feedback}")

        # 调用真实的LLM优化大纲
        try:
            from llm import llm_client_manager
            from config_loader import config
            MODEL_CONFIG = config.get_llm_config()

            # 创建LLM客户端
            llm_client = llm_client_manager.LLMClient(MODEL_CONFIG)

            # 构建优化提示词
            prompt = f"""请根据用户反馈优化以下PPT大纲：

当前大纲：
{request.current_outline}

用户反馈：
{request.user_feedback}

要求：
1. 保持markdown格式
2. 根据用户反馈进行针对性调整
3. 保持大纲的逻辑性和完整性
4. 如果用户要求增加内容，请具体补充
5. 如果用户要求修改结构，请合理调整

请返回优化后的完整大纲："""

            # 调用大模型
            response = llm_client.call_model(prompt, temperature=0.1, max_tokens=4000)

            if response and response.strip():
                refined_outline = response.strip()
                logger.info(f"LLM优化大纲成功，长度: {len(refined_outline)}")
            else:
                # 如果LLM返回空，使用简单的文本拼接
                refined_outline = request.current_outline + f"\n\n## 根据反馈调整\n- {request.user_feedback}\n- 已根据您的建议进行优化"
                logger.warning("LLM返回空结果，使用简单优化")

        except Exception as llm_error:
            logger.error(f"LLM调用失败: {llm_error}")
            # LLM调用失败时的备用处理
            refined_outline = request.current_outline + f"\n\n## 根据反馈调整\n- {request.user_feedback}\n- 已根据您的建议进行优化"

        return {
            "success": True,
            "data": {
                "outline": refined_outline
            },
            "message": "大纲优化成功"
        }
    except Exception as e:
        logger.error(f"优化大纲失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 生成预览
@app.post("/api/generate/preview")
async def generate_preview(request: GenerateRequest):
    try:
        logger.info(f"生成预览请求: 主题={request.theme}")

        if not request.outline:
            return {
                "success": False,
                "message": "缺少大纲内容"
            }

        # 使用moffee生成幻灯片预览
        html_content = await generate_moffee_slideshow(request.outline, request.theme)

        # 保存预览文件到静态目录
        import os
        static_dir = os.path.join(os.path.dirname(__file__), "static")
        os.makedirs(static_dir, exist_ok=True)

        preview_file = os.path.join(static_dir, "slideshow_preview.html")
        with open(preview_file, 'w', encoding='utf-8') as f:
            f.write(html_content)

        preview_url = "/static/slideshow_preview.html"

        return {
            "success": True,
            "data": {
                "preview_url": preview_url
            },
            "message": "预览生成成功"
        }
    except Exception as e:
        logger.error(f"生成预览失败: {e}")
        return {
            "success": False,
            "message": f"生成预览失败: {str(e)}"
        }

# 导出PPT
@app.post("/api/generate/export")
async def export_ppt(request: GenerateRequest):
    try:
        # 从请求中获取格式参数
        formats = getattr(request, 'formats', [])
        if not formats:
            # 如果没有formats，使用format字段
            output_format = getattr(request, 'format', 'pdf')
            formats = [output_format]

        return_info = getattr(request, 'return_info', False)
        logger.info(f"导出PPT请求: 主题={request.theme}, 格式={formats}")

        if not request.outline:
            raise HTTPException(status_code=400, detail="缺少大纲内容")

        # 生成多格式文件
        generated_files = {}

        for output_format in formats:
            if output_format.lower() == "pptx":
                # 生成PPTX文件
                import tempfile
                import os
                temp_dir = tempfile.mkdtemp()
                file_path = await convert_to_pptx(request.outline, request.theme, temp_dir)
                generated_files['pptx'] = file_path
                logger.info(f"PPTX文件生成成功: {file_path}")
            else:
                # 生成HTML或PDF文件
                file_path = await generate_ppt_file(request.outline, request.theme, output_format)
                generated_files[output_format] = file_path
                logger.info(f"{output_format.upper()}文件生成成功: {file_path}")

        # 如果只有一个格式且不需要返回信息，直接返回文件
        if len(formats) == 1 and not return_info:
            output_format = formats[0]
            file_path = generated_files[output_format]

            if output_format.lower() == "pptx":
                from fastapi.responses import FileResponse
                return FileResponse(
                    path=file_path,
                    filename=f"presentation_{request.theme}.pptx",
                    media_type="application/vnd.openxmlformats-officedocument.presentationml.presentation"
                )
            else:
                from fastapi.responses import FileResponse
                media_type = "application/pdf" if output_format == "pdf" else "text/html"
                return FileResponse(
                    path=file_path,
                    filename=f"presentation_{request.theme}.{output_format}",
                    media_type=media_type
                )

        # 返回文件信息
        import os
        result = {
            "success": True,
            "files": {},
            "theme": request.theme
        }

        for fmt, file_path in generated_files.items():
            file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
            result["files"][fmt] = {
                "path": file_path,
                "size": f"{file_size / 1024:.1f} KB",
                "download_url": f"/files/{os.path.basename(file_path)}"
            }

        return result

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        logger.error(f"导出PPT失败: {e}")
        logger.error(f"错误详情: {error_details}")
        return {
            "success": False,
            "message": f"导出PPT失败: {str(e)}",
            "error_details": error_details
        }

async def generate_ppt_file(outline: str, theme: str = "beam", output_format: str = "html"):
    """使用moffee生成PPT文件"""
    import tempfile
    import os
    from moffee.builder import build

    try:
        # 创建临时目录
        temp_dir = tempfile.mkdtemp()

        # 准备markdown内容
        markdown_content = f"""---
theme: {theme}
---

{outline}
"""

        # 保存markdown文件
        md_file = os.path.join(temp_dir, "presentation.md")
        with open(md_file, "w", encoding="utf-8") as f:
            f.write(markdown_content)

        # 生成输出目录
        output_dir = os.path.join(temp_dir, "output")
        os.makedirs(output_dir, exist_ok=True)

        # 使用moffee生成HTML
        template_dir = os.path.join(os.path.dirname(__file__), "..", "moffee-base", "moffee", "templates", "base")
        theme_dir = os.path.join(os.path.dirname(__file__), "..", "moffee-base", "moffee", "templates", theme)

        build(
            document_path=md_file,
            output_dir=output_dir,
            template_dir=template_dir,
            theme_dir=theme_dir
        )

        # 返回生成的HTML文件路径
        html_file = os.path.join(output_dir, "index.html")
        if os.path.exists(html_file):
            logger.info(f"HTML生成成功: {html_file}")

            # 如果需要PDF格式，转换HTML为PDF
            if output_format.lower() == "pdf":
                pdf_file = await convert_html_to_pdf(html_file, temp_dir, theme)
                return pdf_file
            else:
                return html_file
        else:
            raise Exception("HTML文件生成失败")

    except Exception as e:
        logger.error(f"moffee生成失败: {e}")
        # 如果moffee失败，生成简单的HTML
        return await generate_simple_html(outline, theme, temp_dir, output_format)

async def convert_html_to_pdf(html_file: str, temp_dir: str, theme: str = "default"):
    """将HTML文件转换为PDF"""
    try:
        from reportlab.lib.pagesizes import A4, landscape
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        import re
        from html import unescape

        # 生成PDF文件路径
        pdf_file = os.path.join(temp_dir, f"presentation_{theme}.pdf")

        # 创建PDF文档
        doc = SimpleDocTemplate(
            pdf_file,
            pagesize=landscape(A4),
            rightMargin=inch,
            leftMargin=inch,
            topMargin=inch,
            bottomMargin=inch
        )

        # 读取HTML内容
        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()

        # 获取样式
        styles = getSampleStyleSheet()

        # 创建自定义样式
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=1  # 居中
        )

        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading2'],
            fontSize=18,
            spaceAfter=12,
            spaceBefore=12
        )

        normal_style = ParagraphStyle(
            'CustomNormal',
            parent=styles['Normal'],
            fontSize=12,
            spaceAfter=6,
            leading=18
        )

        # 解析HTML内容并转换为PDF元素
        story = []

        # 简单的HTML解析 - 提取文本内容
        # 移除HTML标签但保留内容
        text_content = re.sub(r'<script[^>]*>.*?</script>', '', html_content, flags=re.DOTALL)
        text_content = re.sub(r'<style[^>]*>.*?</style>', '', text_content, flags=re.DOTALL)

        # 提取标题和内容
        lines = text_content.split('\n')
        current_slide_content = []

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 移除HTML标签
            clean_line = re.sub(r'<[^>]+>', '', line)
            clean_line = unescape(clean_line).strip()

            if not clean_line:
                continue

            # 检查是否是标题
            if any(keyword in clean_line.lower() for keyword in ['演示文稿', '大纲', '目录', '第', '章', '节']):
                if current_slide_content:
                    # 添加前一页内容
                    for content in current_slide_content:
                        story.append(Paragraph(content, normal_style))
                    story.append(PageBreak())
                    current_slide_content = []

                story.append(Paragraph(clean_line, title_style))
                story.append(Spacer(1, 20))
            elif clean_line.startswith(('•', '-', '*', '1.', '2.', '3.', '4.', '5.')):
                # 列表项
                current_slide_content.append(clean_line)
            elif len(clean_line) > 10:  # 正文内容
                current_slide_content.append(clean_line)

        # 添加最后一页内容
        if current_slide_content:
            for content in current_slide_content:
                story.append(Paragraph(content, normal_style))

        # 如果没有内容，添加默认内容
        if not story:
            story.append(Paragraph("演示文稿", title_style))
            story.append(Spacer(1, 20))
            story.append(Paragraph("此演示文稿已成功生成为PDF格式", normal_style))

        # 生成PDF
        doc.build(story)

        if os.path.exists(pdf_file):
            logger.info(f"PDF生成成功: {pdf_file}")
            return pdf_file
        else:
            raise Exception("PDF文件生成失败")

    except Exception as e:
        logger.error(f"HTML转PDF失败: {e}")
        # 如果PDF转换失败，返回HTML文件
        logger.warning("PDF转换失败，返回HTML文件")
        return html_file

async def generate_simple_html(outline: str, theme: str, temp_dir: str, output_format: str = "html"):
    """生成简单的HTML演示文稿作为备用方案"""

    # 简单的HTML模板
    html_template = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PPT演示文稿</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }}
        .slide {{
            background: white;
            margin: 20px auto;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 800px;
            min-height: 500px;
        }}
        h1 {{ color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }}
        h2 {{ color: #34495e; margin-top: 30px; }}
        h3 {{ color: #7f8c8d; }}
        ul {{ line-height: 1.8; }}
        li {{ margin: 8px 0; }}
        .theme-{theme} {{
            background: {'linear-gradient(135deg, #3498db 0%, #2980b9 100%)' if theme == 'blue' else
                        'linear-gradient(135deg, #27ae60 0%, #229954 100%)' if theme == 'gaia' else
                        'linear-gradient(135deg, #8e44ad 0%, #7d3c98 100%)' if theme == 'robo' else
                        'linear-gradient(135deg, #e74c3c 0%, #c0392b 100%)' if theme == 'beam' else
                        'linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%)'};
        }}
    </style>
</head>
<body class="theme-{theme}">
    <div class="slide">
        <div style="white-space: pre-wrap;">{outline}</div>
    </div>
</body>
</html>"""

    # 保存HTML文件
    html_file = os.path.join(temp_dir, f"presentation_{theme}.html")
    with open(html_file, "w", encoding="utf-8") as f:
        f.write(html_template)

    logger.info(f"简单HTML生成成功: {html_file}")

    # 如果需要PDF格式，转换HTML为PDF
    if output_format.lower() == "pdf":
        try:
            pdf_file = await convert_html_to_pdf(html_file, temp_dir, theme)
            return pdf_file
        except Exception as e:
            logger.warning(f"简单HTML转PDF失败: {e}，返回HTML文件")
            return html_file
    else:
        return html_file

async def generate_moffee_slideshow(outline: str, theme: str):
    """生成专业的HTML幻灯片预览"""
    try:
        # 直接使用我们的高质量幻灯片生成器
        logger.info(f"生成{theme}主题的幻灯片预览")
        return generate_professional_slideshow_html(outline, theme)
    except Exception as e:
        logger.error(f"幻灯片生成失败: {e}")
        # 回退到简单HTML幻灯片
        return generate_simple_slideshow_html(outline, theme)

def generate_professional_slideshow_html(outline: str, theme: str):
    """生成专业的HTML幻灯片"""

    # 主题配色方案
    theme_colors = {
        'business': {
            'primary': '#1e40af',
            'secondary': '#3b82f6',
            'accent': '#60a5fa',
            'background': 'linear-gradient(135deg, #1e40af 0%, #3b82f6 100%)',
            'text': '#1f2937'
        },
        'creative': {
            'primary': '#7c3aed',
            'secondary': '#a855f7',
            'accent': '#c084fc',
            'background': 'linear-gradient(135deg, #7c3aed 0%, #ec4899 100%)',
            'text': '#1f2937'
        },
        'academic': {
            'primary': '#059669',
            'secondary': '#10b981',
            'accent': '#34d399',
            'background': 'linear-gradient(135deg, #059669 0%, #0891b2 100%)',
            'text': '#1f2937'
        },
        'modern': {
            'primary': '#374151',
            'secondary': '#6b7280',
            'accent': '#9ca3af',
            'background': 'linear-gradient(135deg, #374151 0%, #111827 100%)',
            'text': '#1f2937'
        }
    }

    colors = theme_colors.get(theme, theme_colors['modern'])

    # 解析大纲内容
    lines = outline.strip().split('\n')
    slides = []
    current_slide = {'title': '', 'content': []}

    for line in lines:
        line = line.strip()
        if not line:
            continue

        if line.startswith('# '):
            # 主标题 - 新幻灯片
            if current_slide['title'] or current_slide['content']:
                slides.append(current_slide)
            current_slide = {'title': line[2:], 'content': [], 'type': 'title'}
        elif line.startswith('## '):
            # 二级标题 - 新幻灯片
            if current_slide['title'] or current_slide['content']:
                slides.append(current_slide)
            current_slide = {'title': line[3:], 'content': [], 'type': 'section'}
        elif line.startswith('### '):
            # 三级标题 - 内容标题
            current_slide['content'].append({'type': 'subtitle', 'text': line[4:]})
        elif line.startswith('- ') or line.startswith('* '):
            # 列表项
            current_slide['content'].append({'type': 'bullet', 'text': line[2:]})
        elif line.startswith('1. ') or line.startswith('2. ') or line.startswith('3. '):
            # 数字列表
            current_slide['content'].append({'type': 'number', 'text': line[3:]})
        else:
            # 普通文本
            if line:
                current_slide['content'].append({'type': 'text', 'text': line})

    # 添加最后一张幻灯片
    if current_slide['title'] or current_slide['content']:
        slides.append(current_slide)

    # 如果没有幻灯片，创建一个默认的
    if not slides:
        slides = [{'title': 'PPT演示', 'content': [{'type': 'text', 'text': outline}], 'type': 'title'}]

    # 生成HTML内容
    html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专业PPT预览</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: {colors['background']};
            overflow: hidden;
            color: {colors['text']};
        }}

        .slideshow-container {{
            position: relative;
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }}

        .slide {{
            display: none;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            padding: 80px 60px;
            width: 90%;
            max-width: 1000px;
            min-height: 600px;
            position: relative;
            animation: slideIn 0.5s ease-in-out;
        }}

        .slide.active {{
            display: flex;
            flex-direction: column;
            justify-content: center;
        }}

        .slide.title {{
            text-align: center;
            background: linear-gradient(135deg, {colors['primary']}, {colors['secondary']});
            color: white;
        }}

        .slide h1 {{
            font-size: 3.5em;
            font-weight: 700;
            margin-bottom: 40px;
            line-height: 1.2;
        }}

        .slide h2 {{
            font-size: 2.8em;
            font-weight: 600;
            margin-bottom: 30px;
            color: {colors['primary']};
            border-bottom: 3px solid {colors['accent']};
            padding-bottom: 15px;
        }}

        .slide.title h1, .slide.title h2 {{
            color: white;
            border-bottom-color: rgba(255,255,255,0.3);
        }}

        .content-item {{
            margin-bottom: 20px;
            font-size: 1.4em;
            line-height: 1.6;
        }}

        .content-item.subtitle {{
            font-size: 1.8em;
            font-weight: 600;
            color: {colors['secondary']};
            margin: 30px 0 20px 0;
        }}

        .content-item.bullet {{
            position: relative;
            padding-left: 40px;
        }}

        .content-item.bullet::before {{
            content: '●';
            position: absolute;
            left: 0;
            color: {colors['primary']};
            font-size: 1.2em;
        }}

        .content-item.number {{
            position: relative;
            padding-left: 40px;
            counter-increment: slide-counter;
        }}

        .content-item.number::before {{
            content: counter(slide-counter) '.';
            position: absolute;
            left: 0;
            color: {colors['primary']};
            font-weight: bold;
        }}

        .controls {{
            position: fixed;
            bottom: 40px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 20px;
            z-index: 1000;
        }}

        .btn {{
            background: rgba(255,255,255,0.95);
            border: 2px solid {colors['primary']};
            padding: 15px 30px;
            border-radius: 30px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            color: {colors['primary']};
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }}

        .btn:hover {{
            background: {colors['primary']};
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }}

        .slide-counter {{
            position: fixed;
            top: 40px;
            right: 40px;
            background: rgba(255,255,255,0.95);
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: bold;
            color: {colors['primary']};
            backdrop-filter: blur(10px);
            border: 2px solid {colors['accent']};
        }}

        .progress-bar {{
            position: fixed;
            bottom: 0;
            left: 0;
            height: 4px;
            background: {colors['primary']};
            transition: width 0.3s ease;
            z-index: 1000;
        }}

        @keyframes slideIn {{
            from {{
                opacity: 0;
                transform: translateX(50px);
            }}
            to {{
                opacity: 1;
                transform: translateX(0);
            }}
        }}

        @media (max-width: 768px) {{
            .slide {{
                padding: 40px 30px;
                width: 95%;
            }}
            .slide h1 {{ font-size: 2.5em; }}
            .slide h2 {{ font-size: 2em; }}
            .content-item {{ font-size: 1.2em; }}
        }}
    </style>
</head>
<body>
    <div class="slideshow-container">
"""

    # 生成幻灯片
    for i, slide in enumerate(slides):
        active_class = "active" if i == 0 else ""
        slide_type = slide.get('type', 'section')

        html_content += f"""
        <div class="slide {slide_type} {active_class}">
"""

        if slide['title']:
            if slide_type == 'title':
                html_content += f"            <h1>{slide['title']}</h1>\n"
            else:
                html_content += f"            <h2>{slide['title']}</h2>\n"

        if slide['content']:
            for item in slide['content']:
                item_type = item['type']
                text = item['text']
                html_content += f'            <div class="content-item {item_type}">{text}</div>\n'

        html_content += "        </div>\n"

    # 添加控制和脚本
    html_content += f"""
    </div>

    <div class="slide-counter">
        <span id="current-slide">1</span> / <span id="total-slides">{len(slides)}</span>
    </div>

    <div class="progress-bar" id="progress-bar"></div>

    <div class="controls">
        <button class="btn" onclick="previousSlide()">◀ 上一页</button>
        <button class="btn" onclick="toggleFullscreen()">⛶ 全屏</button>
        <button class="btn" onclick="nextSlide()">下一页 ▶</button>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;

        function updateProgress() {{
            const progress = ((currentSlide + 1) / totalSlides) * 100;
            document.getElementById('progress-bar').style.width = progress + '%';
        }}

        function showSlide(n) {{
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');
            document.getElementById('current-slide').textContent = currentSlide + 1;
            updateProgress();
        }}

        function nextSlide() {{
            showSlide(currentSlide + 1);
        }}

        function previousSlide() {{
            showSlide(currentSlide - 1);
        }}

        function toggleFullscreen() {{
            if (!document.fullscreenElement) {{
                document.documentElement.requestFullscreen();
            }} else {{
                document.exitFullscreen();
            }}
        }}

        // 键盘控制
        document.addEventListener('keydown', function(e) {{
            if (e.key === 'ArrowRight' || e.key === ' ') {{
                e.preventDefault();
                nextSlide();
            }} else if (e.key === 'ArrowLeft') {{
                e.preventDefault();
                previousSlide();
            }} else if (e.key === 'F11') {{
                e.preventDefault();
                toggleFullscreen();
            }} else if (e.key === 'Home') {{
                e.preventDefault();
                showSlide(0);
            }} else if (e.key === 'End') {{
                e.preventDefault();
                showSlide(totalSlides - 1);
            }}
        }});

        // 触摸控制
        let touchStartX = 0;
        let touchEndX = 0;

        document.addEventListener('touchstart', function(e) {{
            touchStartX = e.changedTouches[0].screenX;
        }});

        document.addEventListener('touchend', function(e) {{
            touchEndX = e.changedTouches[0].screenX;
            handleSwipe();
        }});

        function handleSwipe() {{
            if (touchEndX < touchStartX - 50) {{
                nextSlide();
            }}
            if (touchEndX > touchStartX + 50) {{
                previousSlide();
            }}
        }}

        // 初始化
        document.getElementById('total-slides').textContent = totalSlides;
        updateProgress();
    </script>
</body>
</html>"""

    return html_content

def generate_simple_slideshow_html(outline: str, theme: str):
    """生成简单的HTML幻灯片"""
    slides = outline.split('\n\n')

    html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PPT预览</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow: hidden;
        }}
        .slideshow-container {{
            position: relative;
            width: 100%;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }}
        .slide {{
            display: none;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            padding: 60px;
            width: 80%;
            max-width: 900px;
            min-height: 500px;
            text-align: center;
        }}
        .slide.active {{
            display: block;
        }}
        .slide h1 {{
            color: #333;
            font-size: 2.5em;
            margin-bottom: 30px;
        }}
        .slide h2 {{
            color: #555;
            font-size: 2em;
            margin-bottom: 20px;
        }}
        .slide p, .slide li {{
            color: #666;
            font-size: 1.2em;
            line-height: 1.6;
            margin-bottom: 15px;
        }}
        .controls {{
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 20px;
        }}
        .btn {{
            background: rgba(255,255,255,0.9);
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
        }}
        .btn:hover {{
            background: white;
            transform: translateY(-2px);
        }}
        .slide-counter {{
            position: fixed;
            top: 30px;
            right: 30px;
            background: rgba(255,255,255,0.9);
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: bold;
        }}
    </style>
</head>
<body>
    <div class="slideshow-container">
"""

    for i, slide_content in enumerate(slides):
        if slide_content.strip():
            active_class = "active" if i == 0 else ""
            html_content += f"""
        <div class="slide {active_class}">
            <div>{slide_content.strip()}</div>
        </div>
"""

    html_content += f"""
    </div>

    <div class="slide-counter">
        <span id="current-slide">1</span> / <span id="total-slides">{len([s for s in slides if s.strip()])}</span>
    </div>

    <div class="controls">
        <button class="btn" onclick="previousSlide()">上一页</button>
        <button class="btn" onclick="nextSlide()">下一页</button>
        <button class="btn" onclick="toggleFullscreen()">全屏</button>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;

        function showSlide(n) {{
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');
            document.getElementById('current-slide').textContent = currentSlide + 1;
        }}

        function nextSlide() {{
            showSlide(currentSlide + 1);
        }}

        function previousSlide() {{
            showSlide(currentSlide - 1);
        }}

        function toggleFullscreen() {{
            if (!document.fullscreenElement) {{
                document.documentElement.requestFullscreen();
            }} else {{
                document.exitFullscreen();
            }}
        }}

        // 键盘控制
        document.addEventListener('keydown', function(e) {{
            if (e.key === 'ArrowRight' || e.key === ' ') {{
                nextSlide();
            }} else if (e.key === 'ArrowLeft') {{
                previousSlide();
            }} else if (e.key === 'F11') {{
                e.preventDefault();
                toggleFullscreen();
            }}
        }});

        document.getElementById('total-slides').textContent = totalSlides;
    </script>
</body>
</html>"""

    return html_content

async def convert_to_pptx(outline: str, theme: str, temp_dir: str):
    """将大纲内容转换为PPTX文件 - 使用新的高质量生成系统"""
    try:
        from app.services.pptx_generator.pptx_builder import PPTXBuilder
        import os

        # 创建PPTX构建器
        builder = PPTXBuilder()

        # 设置输出路径
        pptx_file = os.path.join(temp_dir, f"presentation_{theme}.pptx")

        # 验证内容格式
        is_valid, message = builder.validate_markdown(outline)
        if not is_valid:
            logger.warning(f"内容验证警告: {message}")

        # 生成PPTX文件
        output_path = builder.generate_from_markdown(
            markdown_content=outline,
            theme_name=theme,
            output_path=pptx_file
        )

        # 获取生成统计信息
        stats = builder.get_generation_stats(outline)
        logger.info(f"PPTX生成成功: {output_path}")
        logger.info(f"生成统计: {stats}")

        return output_path

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        logger.error(f"PPTX转换失败: {e}")
        logger.error(f"PPTX转换错误详情: {error_details}")
        raise

if __name__ == "__main__":
    # 使用已经导入的配置
    try:
        port = settings.BACKEND_PORT
        logger.info(f"使用配置端口: {port}")
    except Exception as e:
        port = 9527  # 默认端口
        logger.warning(f"配置导入失败，使用默认端口: {port}")

    uvicorn.run(app, host="0.0.0.0", port=port, reload=False)
