#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import json
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent))

def simple_test():
    """简单测试专业PPT生成器"""
    try:
        print('🔄 简单测试开始...')
        
        from app.services.professional_ppt_generator import professional_ppt_generator
        
        print('✅ 专业PPT生成器导入成功')
        
        # 测试参数
        template_id = 'template_20250708_221451_adc64662'
        user_request = '人工智能技术发展'
        output_filename = 'simple_test.pptx'
        
        print(f'模板: {template_id}')
        print(f'需求: {user_request}')
        print()
        
        # 执行生成（使用测试模式）
        print('🚀 开始生成...')
        result = professional_ppt_generator.generate_ppt_from_template(
            template_id, user_request, output_filename, test_mode=True
        )
        
        print('📊 生成结果:')
        print(f'成功: {result.get("success")}')
        if result.get('success'):
            print(f'文件: {result["file_path"]}')
            print(f'大小: {result["file_size"]/1024/1024:.1f} MB')
            print('✅ 生成成功！')
        else:
            print(f'错误: {result.get("error")}')
            print('❌ 生成失败')
        
        return result.get('success', False)
        
    except Exception as e:
        print(f'❌ 测试异常: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = simple_test()
    print(f'\n🎯 测试结果: {"✅ 成功" if success else "❌ 失败"}')
