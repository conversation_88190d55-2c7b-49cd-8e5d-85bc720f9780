#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试新的基于成品PPT模板的生成系统
"""

import sys
import json
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent))

def test_new_template_system():
    """测试新的模板系统"""
    try:
        print('🚀 测试新的基于成品PPT模板的生成系统')
        print('=' * 60)
        
        from app.services.template_based_ppt_generator import template_ppt_generator
        
        # 测试参数
        template_id = 'template_20250708_221451_adc64662'
        user_context = '''
        用户需求：生成一个关于人工智能在医疗诊断中应用的PPT
        
        用户对话：
        用户：我需要制作一个关于AI医疗诊断的演示文稿
        助手：好的，我来为您生成一个专业的AI医疗诊断PPT
        用户：重点介绍技术原理、应用场景和发展前景
        助手：明白了，我会重点突出这三个方面的内容
        '''
        
        print(f'📋 测试参数:')
        print(f'  模板ID: {template_id}')
        print(f'  用户上下文长度: {len(user_context)}字符')
        print()
        
        # 步骤1: 测试模板解析
        print('📖 步骤1: 测试模板解析')
        print('-' * 40)
        
        template_path = template_ppt_generator._get_template_path(template_id)
        if not template_path:
            print(f'❌ 模板文件不存在: {template_id}')
            return False
        
        print(f'✅ 找到模板文件: {template_path}')
        
        parse_result = template_ppt_generator.step1_parse_template_ppt(template_path)
        
        if parse_result["success"]:
            print(f'✅ 模板解析成功')
            print(f'  总页数: {parse_result["total_pages"]}')
            print(f'  占位符数量: {parse_result["total_placeholders"]}')
            
            # 显示前2页的结构
            template_structure = parse_result["template_structure"]
            for i, (page_key, page_data) in enumerate(list(template_structure.items())[:2]):
                print(f'  {page_key}:')
                for element_key, element_data in page_data.items():
                    print(f'    {element_key}: {element_data["type"]}, {element_data["word_limit"]}字, {element_data["PlaceholderId"]}')
        else:
            print(f'❌ 模板解析失败: {parse_result["error"]}')
            return False
        
        print()
        
        # 步骤2: 测试模板文件创建
        print('🔧 步骤2: 测试模板文件创建')
        print('-' * 40)
        
        try:
            template_file_path = template_ppt_generator.step2_create_template_file(
                template_path, template_structure
            )
            print(f'✅ 模板文件创建成功: {template_file_path}')
        except Exception as e:
            print(f'❌ 模板文件创建失败: {e}')
            return False
        
        print()
        
        # 步骤3: 测试提示词生成
        print('📝 步骤3: 测试提示词生成')
        print('-' * 40)
        
        prompt = template_ppt_generator.step3_generate_prompt(user_context, template_structure)
        print(f'✅ 提示词生成成功')
        print(f'  提示词长度: {len(prompt)}字符')
        print(f'  提示词预览: {prompt[:200]}...')
        
        print()
        
        # 步骤4: 测试大模型调用（模拟）
        print('🤖 步骤4: 测试大模型调用')
        print('-' * 40)
        
        # 创建模拟的生成内容
        mock_generated_content = {}
        for page_key, page_data in template_structure.items():
            mock_page_content = {}
            for element_key, element_data in page_data.items():
                mock_page_content[element_key] = {
                    "type": element_data["type"],
                    "content": f"AI医疗诊断{element_data['type']}内容示例",
                    "word_limit": element_data["word_limit"],
                    "PlaceholderId": element_data["PlaceholderId"]
                }
            mock_generated_content[page_key] = mock_page_content
        
        print(f'✅ 模拟内容生成成功')
        print(f'  生成页数: {len(mock_generated_content)}')
        
        print()
        
        # 步骤5: 测试内容填充
        print('📄 步骤5: 测试内容填充')
        print('-' * 40)
        
        output_filename = 'test_new_template_system.pptx'
        
        try:
            fill_result = template_ppt_generator.step5_fill_content_to_template(
                template_file_path, mock_generated_content, output_filename
            )
            
            if fill_result["success"]:
                print(f'✅ 内容填充成功')
                print(f'  输出文件: {fill_result["file_path"]}')
                print(f'  文件大小: {fill_result["file_size"]/1024/1024:.1f} MB')
                
                # 验证生成的PPT
                from pptx import Presentation
                prs = Presentation(fill_result["file_path"])
                print(f'  PPT验证: {len(prs.slides)}页，结构完整')
                
            else:
                print(f'❌ 内容填充失败: {fill_result["error"]}')
                return False
                
        except Exception as e:
            print(f'❌ 内容填充异常: {e}')
            import traceback
            traceback.print_exc()
            return False
        
        print()
        
        # 完整流程测试
        print('🎯 完整流程测试')
        print('-' * 40)
        
        try:
            complete_result = template_ppt_generator.generate_ppt_from_template(
                template_id, user_context, 'test_complete_flow.pptx'
            )
            
            if complete_result["success"]:
                print(f'✅ 完整流程测试成功')
                print(f'  输出文件: {complete_result["file_path"]}')
                print(f'  文件大小: {complete_result["file_size"]/1024/1024:.1f} MB')
                print(f'  总页数: {complete_result["total_pages"]}')
                print(f'  占位符数量: {complete_result["total_placeholders"]}')
            else:
                print(f'❌ 完整流程测试失败: {complete_result["error"]}')
                return False
                
        except Exception as e:
            print(f'❌ 完整流程测试异常: {e}')
            import traceback
            traceback.print_exc()
            return False
        
        print()
        print('🎉 新模板系统测试全部通过！')
        print('=' * 60)
        
        return True
        
    except Exception as e:
        print(f'❌ 测试异常: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_new_template_system()
    print(f'\n🎯 最终结果: {"✅ 全部通过" if success else "❌ 测试失败"}')
