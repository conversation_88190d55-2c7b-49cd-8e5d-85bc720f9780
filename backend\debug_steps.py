"""
分步调试专业PPT生成器
"""

import sys
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')

# 添加路径
sys.path.insert(0, str(Path(__file__).parent))

def test_step1():
    """测试步骤1：解析PPT内容"""
    print("🔍 测试步骤1：解析PPT内容")
    
    try:
        from pptx import Presentation
        
        template_path = "templates/template_20250708_221451_adc64662.pptx"
        print(f"模板路径: {template_path}")
        print(f"文件存在: {Path(template_path).exists()}")
        
        if Path(template_path).exists():
            prs = Presentation(template_path)
            print(f"✅ PPT加载成功，共{len(prs.slides)}页")
            
            # 检查第一页
            if len(prs.slides) > 0:
                slide = prs.slides[0]
                text_shapes = [shape for shape in slide.shapes if hasattr(shape, 'text') and shape.text.strip()]
                print(f"✅ 第1页有{len(text_shapes)}个文字形状")
                
                if text_shapes:
                    print(f"✅ 第1个文字: {text_shapes[0].text[:50]}...")
                    return True
        
        return False
        
    except Exception as e:
        print(f"❌ 步骤1失败: {e}")
        return False

def test_step2():
    """测试步骤2：创建占位符"""
    print("\n🔍 测试步骤2：创建占位符")
    
    try:
        from app.services.professional_ppt_generator import professional_ppt_generator
        
        template_path = "templates/template_20250708_221451_adc64662.pptx"
        structure_data = professional_ppt_generator._parse_ppt_content(template_path)
        
        print(f"✅ 解析完成: {structure_data['template_info']['total_slides']}页")
        print(f"✅ 文字元素: {sum(len(s['text_elements']) for s in structure_data['slides'])}个")
        
        # 创建占位符模板
        placeholder_path = professional_ppt_generator._create_placeholder_template(
            template_path, structure_data, "test"
        )
        
        print(f"✅ 占位符模板: {placeholder_path}")
        return True
        
    except Exception as e:
        print(f"❌ 步骤2失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始分步调试...")
    
    # 测试步骤1
    if test_step1():
        # 测试步骤2
        test_step2()
    
    print("\n✅ 调试完成")
