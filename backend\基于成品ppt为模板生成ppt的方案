PPT生成系统需求描述
目标
基于现有成品PPT作为模板，解析其内容并生成结构化描述，结合用户需求生成新内容并填充回模板，快速生成新的PPT。
开发流程：
1. 成品ppt数据解析
用户上传的成品PPT文件或者重新解析现有PPT内容时解析提取ppt的每页文字内容数据，并进行记录。
功能：
使用python-pptx提取每页的所有文字内容。
获取总得ppt页数，识别每页文字类型（标题、副标题、正文、列表项等）、文字内容，位置（页码、层级）和字数,以及占位符id（随机生成唯一id）。
输出：结构化描述每页的文字（JSON格式），示例：
[
  "page_1": {
    "title": {"type": "title", "content":"AAAA","word_limit": 8,"PlaceholderId"："XXXXXXXX"},
    "body": {"type": "text", "content":"BBBB","word_limit": 45,"PlaceholderId"："XXXXXXXX"}
  },
 "page_2": {
    "title": {"type": "title","content":"CCCC","word_limit": 8,"PlaceholderId"："XXXXXXXX"},
    "body": {"type": "text", "content":"DDDD","word_limit": 45,"PlaceholderId"："XXXXXXXX"}
  }
]



2. 生成成品ppt对应的模板文件
将成品ppt的每页原文字内容替换为对应文字的占位符id,也就是基于成品ppt数据解析功能输出的结构化描述每页的文字来进行处理
功能：
根据文字内容（content）和类型（type）将原ppt文字替换为唯一占位符，内容就为占位符id（PlaceholderId）。
记录占位符与原始文字的映射关系。
输出：带占位符的模板PPT文件。

3. 动态生成提示词
根据用户对话过程中的生成ppt要求和ppt模板的结构化描述每页的文字生成大模型ppt内容生成提示词。
功能：
主要是以用户与大模型对话的上下文[取最新的不超过3000字内容]和ppt模板的结构化描述每页的文字的（JSON格式）为生成数据格式要求编写提示词，这两部分就是动态的，ppt模板的结构化描述每页的文字的（JSON格式）就是根据选择模板变更。以下为提示词的示例模板，你可以酌情优化：
“
# 任务
你是一个ppt编写专家，下面就请结合用户问答过程中对于ppt的生成需求和最终需要生成的数据格式要求生成ppt内容。

#用户的问答信息是
[用户的问答信息数据]

#输出的数据结构是
[ppt模板的结构化描述每页的文字的（JSON格式）]

#生成要求
请严格按照输出的数据结构的每页的文字结构生成内容，生成的页数内容要保持一直，每页需要生成的文字类型和文字数量一致，生成内容字数与模板相差不超过±5个字，type和PlaceholderId需要保持与原本内容一致，content就是新生成的内容。
页数调整：暂不考虑支持用户指定页数，完全根据ppt模板的页数生成。
”
输出：融合后的ppt内容生成提示词。

4. 大模型生成内容
根据动态生成提示词生成ppt的内
功能：
调用大模型通过动态生成提示词告知大模型每页结构和字数限制以及用户生成ppt的内容要求。
生成符合要求的内容。
输出：ppt模板的结构化描述每页的文字的（JSON格式）的ppt内容。

5. 填充内容到模板
基于之前流程选择模板PPT和大模型生成的内容，将大模型生成的每页内容文字逐一根据占位符替换到原模板中，保持原有的文字大小和样式。
功能：
将内容替换对应占位符(根据PlaceholderId进行映射)。
保持原样式（字体、大小、颜色等）不变。
输出：生成新的PPT文件。

6。原有逻辑调整
原有流程中对话生成环节需要生成大纲，这个流程不要了，就是只要大模型返回了生成的ppt内容就可以进入下一步。
将确认大纲改为确认内容，不要直接展示json数据，而是按照每页内容