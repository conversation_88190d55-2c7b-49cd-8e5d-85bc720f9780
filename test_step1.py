import sys
sys.path.insert(0, './backend')

try:
    from app.services.professional_ppt_generator import professional_ppt_generator
    
    print('🔄 测试步骤1：解析PPT内容...')
    
    template_id = 'template_20250708_221451_adc64662'
    template_path = f'backend/templates/{template_id}.pptx'
    
    # 测试步骤1
    structure_data = professional_ppt_generator._parse_ppt_content(template_path)
    
    print('✅ 步骤1完成!')
    print(f'   - 总页数: {structure_data["template_info"]["total_slides"]}')
    print(f'   - 总文字元素: {sum(len(s["text_elements"]) for s in structure_data["slides"])}')
    
    # 显示前3页的解析结果
    for i, slide_data in enumerate(structure_data["slides"][:3]):
        print(f'   - 第{i+1}页: {len(slide_data["text_elements"])}个文字元素')
        
        for j, text_element in enumerate(slide_data["text_elements"][:2]):
            print(f'     {text_element["placeholder_id"]}: {text_element["text_type"]}, {text_element["word_count"]}字')
            print(f'       原文: {text_element["original_text"][:30]}...')

except Exception as e:
    print(f'❌ 步骤1测试失败: {e}')
    import traceback
    traceback.print_exc()
