#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试动态提示词格式是否正确
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

from services.template_manager import template_manager

def test_prompt_format():
    """测试提示词格式"""
    try:
        print('🔧 测试动态提示词格式')
        print('=' * 60)
        
        template_id = 'template_20250708_221451_adc64662'
        user_context = '''用户: 我需要生成一个关于人工智能的PPT
助手: 好的，我来为您生成
用户: 重点介绍技术原理和应用场景
助手: 明白了，我会重点突出这些内容'''
        
        print(f'📋 模板ID: {template_id}')
        print(f'📝 用户上下文: {user_context[:50]}...')
        print()
        
        # 测试新系统提示词
        print('🚀 测试新系统提示词格式:')
        print('-' * 40)
        
        new_prompt = template_manager.generate_ai_prompt(
            template_id, 
            use_new_system=True, 
            user_context=user_context
        )
        
        if new_prompt.get("success"):
            prompt_text = new_prompt["prompt"]
            print('✅ 新提示词生成成功')
            print(f'提示词长度: {len(prompt_text)}字符')
            
            # 检查关键格式
            checks = {
                "包含'# 任务'": "# 任务" in prompt_text,
                "包含'# 用户的问答信息是'": "# 用户的问答信息是" in prompt_text,
                "包含'# 输出的数据结构是'": "# 输出的数据结构是" in prompt_text,
                "包含'# 生成要求'": "# 生成要求" in prompt_text,
                "包含用户上下文": user_context in prompt_text,
                "包含JSON结构": "{" in prompt_text and "}" in prompt_text,
            }
            
            print('\n📋 格式检查:')
            for check_name, result in checks.items():
                status = "✅" if result else "❌"
                print(f'  {status} {check_name}: {result}')
            
            # 显示提示词的关键部分
            print('\n📖 提示词关键部分:')
            lines = prompt_text.split('\n')
            for i, line in enumerate(lines):
                if line.startswith('# '):
                    print(f'第{i+1}行: {line}')
                    # 显示该部分的前几行内容
                    for j in range(1, min(4, len(lines) - i)):
                        if i + j < len(lines) and not lines[i + j].startswith('# '):
                            content = lines[i + j][:100]
                            if content.strip():
                                print(f'  内容: {content}...')
                        else:
                            break
                    print()
            
            # 检查是否有用户上下文
            if "# 用户的问答信息是" in prompt_text:
                start_idx = prompt_text.find("# 用户的问答信息是")
                end_idx = prompt_text.find("# 输出的数据结构是", start_idx)
                if end_idx > start_idx:
                    user_info_section = prompt_text[start_idx:end_idx].strip()
                    print(f'📝 用户问答信息部分:')
                    print(f'长度: {len(user_info_section)}字符')
                    print(f'内容: {user_info_section[:200]}...')
                    print()
            
            return all(checks.values())
        else:
            print(f'❌ 新提示词生成失败: {new_prompt.get("error")}')
            return False
            
    except Exception as e:
        print(f'❌ 测试异常: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print('🚀 开始测试动态提示词格式')
    
    success = test_prompt_format()
    
    print(f'\n🎯 测试结果: {"✅ 成功" if success else "❌ 失败"}')
    
    if success:
        print('\n🎉 动态提示词格式完全正确!')
        print('包含所有必需的部分：')
        print('1. # 任务')
        print('2. # 用户的问答信息是')
        print('3. # 输出的数据结构是')
        print('4. # 生成要求')
    else:
        print('\n❌ 动态提示词格式需要修复')
