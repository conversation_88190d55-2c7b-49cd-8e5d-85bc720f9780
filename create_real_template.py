from pptx import Presentation
from pptx.util import Inches
from pptx.enum.shapes import MSO_SHAPE_TYPE
import os

def create_real_template_from_content():
    """
    从我们的内容幻灯片创建真正的PowerPoint模板
    """
    print('🔄 开始创建真正的模板文件...')
    
    # 1. 加载我们的原始文件（包含18张内容幻灯片）
    source_prs = Presentation('backend/templates/template_20250708_221451_adc64662.pptx')
    print(f'源文件: {len(source_prs.slides)}张幻灯片')
    
    # 2. 创建新的演示文稿作为模板基础
    template_prs = Presentation()
    
    # 3. 获取默认的母版
    slide_master = template_prs.slide_masters[0]
    
    # 4. 清除默认布局，准备添加我们的布局
    # 注意：不能直接删除布局，需要基于现有布局修改
    
    print('🔄 分析源幻灯片的设计元素...')
    
    # 5. 分析第一张幻灯片作为标题布局的基础
    if len(source_prs.slides) > 0:
        first_slide = source_prs.slides[0]
        print(f'第一张幻灯片有 {len(first_slide.shapes)} 个形状')
        
        # 分析背景和设计元素
        for i, shape in enumerate(first_slide.shapes):
            if shape.shape_type == MSO_SHAPE_TYPE.PICTURE:
                print(f'  发现图片形状 {i}: 位置({shape.left}, {shape.top}) 大小({shape.width}, {shape.height})')
            elif shape.shape_type == MSO_SHAPE_TYPE.AUTO_SHAPE:
                print(f'  发现自动形状 {i}: {shape.auto_shape_type}')
            elif hasattr(shape, 'text'):
                text_preview = shape.text[:30].replace('\n', ' ') if shape.text else '(空文本)'
                print(f'  发现文本形状 {i}: "{text_preview}"')
    
    # 6. 保存分析结果
    print('✅ 模板分析完成')
    print('\n建议的解决方案:')
    print('1. 您的文件是一个包含18张设计好的幻灯片的演示文稿')
    print('2. 需要将其中的设计元素提取出来创建布局模板')
    print('3. 或者直接使用这些幻灯片作为内容，不依赖md2pptx的布局系统')
    
    return source_prs

if __name__ == '__main__':
    create_real_template_from_content()
