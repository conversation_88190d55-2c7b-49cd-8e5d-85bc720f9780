# -*- coding: utf-8 -*-
"""
统一配置加载器 - 从根目录config.json读取所有配置
"""
import json
import os
from pathlib import Path
from typing import Dict, Any, List

class ConfigLoader:
    """统一配置加载器"""
    
    def __init__(self):
        self._config = None
        self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        # 获取根目录配置文件路径
        current_file = Path(__file__).resolve()
        config_file = current_file.parent / "config.json"
        
        # 默认配置
        default_config = {
            "app": {
                "name": "PPT生成助手",
                "version": "1.0.0",
                "debug": False
            },
            "ports": {
                "backend": 9527,
                "frontend": 9528,
                "other_services": 9529
            },
            "urls": {
                "backend": "http://localhost:9527",
                "frontend": "http://localhost:9528",
                "other_services": "http://localhost:9529"
            },
            "llm": {
                "api_key": "sk-2h1RwdRjqYc6FosI3aus",
                "model": "deepseek-v3-0324",
                "api_base": "http://*************/gateway/ai-service/v1",
                "timeout": 180,
                "max_tokens": 4000,
                "temperature": 0.7
            },
            "paths": {
                "base_dir": ".",
                "templates_dir": "templates",
                "template_analysis_dir": "template_analysis",
                "upload_dir": "uploads",
                "generated_dir": "generated",
                "static_dir": "static",
                "temp_processing_dir": "temp_processing"
            },
            "file": {
                "max_file_size": 52428800,
                "allowed_extensions": [".pptx", ".ppt"]
            },
            "ppt": {
                "default_slide_width": 10,
                "default_slide_height": 7.5,
                "max_slides": 50
            },
            "cors": {
                "allowed_origins": [
                    "http://localhost:9528",
                    "http://127.0.0.1:9528",
                    "http://localhost:3000",
                    "http://localhost:5173",
                    "http://127.0.0.1:3000",
                    "http://127.0.0.1:5173"
                ]
            }
        }
        
        # 尝试读取配置文件
        try:
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    self._config = json.load(f)
                # 合并默认配置，确保所有必要的键都存在
                for key, value in default_config.items():
                    if key not in self._config:
                        self._config[key] = value
                    elif isinstance(value, dict):
                        for sub_key, sub_value in value.items():
                            if sub_key not in self._config[key]:
                                self._config[key][sub_key] = sub_value
            else:
                self._config = default_config
        except Exception as e:
            print(f"读取配置文件失败: {e}")
            self._config = default_config
    
    def get(self, key: str, default=None) -> Any:
        """获取配置值"""
        keys = key.split('.')
        value = self._config
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        return value
    
    def get_app_config(self) -> Dict[str, Any]:
        """获取应用配置"""
        return self._config.get("app", {})
    
    def get_ports_config(self) -> Dict[str, int]:
        """获取端口配置"""
        return self._config.get("ports", {})
    
    def get_urls_config(self) -> Dict[str, str]:
        """获取URL配置"""
        return self._config.get("urls", {})
    
    def get_llm_config(self) -> Dict[str, Any]:
        """获取LLM配置"""
        return self._config.get("llm", {})
    
    def get_paths_config(self) -> Dict[str, str]:
        """获取路径配置"""
        return self._config.get("paths", {})
    
    def get_file_config(self) -> Dict[str, Any]:
        """获取文件配置"""
        return self._config.get("file", {})
    
    def get_ppt_config(self) -> Dict[str, Any]:
        """获取PPT配置"""
        return self._config.get("ppt", {})
    
    def get_cors_config(self) -> Dict[str, List[str]]:
        """获取CORS配置"""
        return self._config.get("cors", {})
    
    @property
    def config(self) -> Dict[str, Any]:
        """获取完整配置"""
        return self._config

# 创建全局配置实例
config = ConfigLoader()

# 为了向后兼容，提供一些常用的配置访问方式
APP_NAME = config.get("app.name")
APP_VERSION = config.get("app.version")
DEBUG = config.get("app.debug")

BACKEND_PORT = config.get("ports.backend")
FRONTEND_PORT = config.get("ports.frontend")

LLM_CONFIG = config.get_llm_config()
PATHS_CONFIG = config.get_paths_config()
CORS_ORIGINS = config.get("cors.allowed_origins")

# Settings类，保持与原有代码的兼容性
class Settings:
    def __init__(self):
        self._config = config
    
    @property
    def APP_NAME(self) -> str:
        return self._config.get("app.name")
    
    @property
    def APP_VERSION(self) -> str:
        return self._config.get("app.version")
    
    @property
    def DEBUG(self) -> bool:
        return self._config.get("app.debug")
    
    @property
    def BACKEND_PORT(self) -> int:
        return self._config.get("ports.backend")
    
    @property
    def FRONTEND_PORT(self) -> int:
        return self._config.get("ports.frontend")
    
    @property
    def OTHER_SERVICES_PORT(self) -> int:
        return self._config.get("ports.other_services")
    
    @property
    def ALLOWED_ORIGINS(self) -> List[str]:
        return self._config.get("cors.allowed_origins")
    
    @property
    def MAX_FILE_SIZE(self) -> int:
        return self._config.get("file.max_file_size")
    
    @property
    def ALLOWED_EXTENSIONS(self) -> set:
        return set(self._config.get("file.allowed_extensions"))
    
    @property
    def BASE_DIR(self) -> str:
        return self._config.get("paths.base_dir")
    
    @property
    def TEMPLATES_DIR(self) -> str:
        return self._config.get("paths.templates_dir")
    
    @property
    def TEMPLATE_ANALYSIS_DIR(self) -> str:
        return self._config.get("paths.template_analysis_dir")
    
    @property
    def UPLOAD_DIR(self) -> str:
        return self._config.get("paths.upload_dir")
    
    @property
    def GENERATED_DIR(self) -> str:
        return self._config.get("paths.generated_dir")
    
    @property
    def STATIC_DIR(self) -> str:
        return self._config.get("paths.static_dir")
    
    @property
    def TEMP_PROCESSING_DIR(self) -> str:
        return self._config.get("paths.temp_processing_dir")
    
    @property
    def MAX_TOKENS(self) -> int:
        return self._config.get("llm.max_tokens")
    
    @property
    def TEMPERATURE(self) -> float:
        return self._config.get("llm.temperature")
    
    @property
    def DEFAULT_SLIDE_WIDTH(self) -> int:
        return self._config.get("ppt.default_slide_width")
    
    @property
    def DEFAULT_SLIDE_HEIGHT(self) -> float:
        return self._config.get("ppt.default_slide_height")
    
    @property
    def MAX_SLIDES(self) -> int:
        return self._config.get("ppt.max_slides")
    
    # 兼容旧路径配置的方法
    def get_template_structure_file(self, template_id: str) -> str:
        """获取模板结构化JSON文件路径"""
        return f"{self.TEMPLATE_ANALYSIS_DIR}/{template_id}_structure.json"
    
    def get_template_analysis_file(self, template_id: str) -> str:
        """获取模板分析文件路径"""
        return f"{self.TEMPLATE_ANALYSIS_DIR}/{template_id}_analysis.json"
    
    def get_template_file(self, template_id: str) -> str:
        """获取模板文件路径"""
        return f"{self.TEMPLATES_DIR}/{template_id}.pptx"
    
    def get_template_preview_file(self, template_id: str) -> str:
        """获取模板预览文件路径"""
        return f"templates/previews/{template_id}_preview.png"
    
    def get_generated_file(self, filename: str) -> str:
        """获取生成文件路径"""
        return f"uploads/generated/{filename}"
    
    def get_temp_processing_file(self, filename: str) -> str:
        """获取临时处理文件路径"""
        return f"{self.TEMP_PROCESSING_DIR}/{filename}"

# 创建全局settings实例
settings = Settings()
