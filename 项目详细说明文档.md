# PPT Agent 项目详细说明文档

## 📋 项目概述

PPT Agent 是一个基于 AI 的智能 PPT 生成系统，集成了对话式大纲生成、模板管理、实时预览和多格式导出功能。项目采用前后端分离架构，使用 DeepSeek V3 大模型进行内容生成。

## 🏗️ 技术架构

### 前端 (React + Vite)
- **框架**: React 18 + Vite 4.5.14
- **样式**: Tailwind CSS
- **路由**: React Router DOM
- **HTTP客户端**: Axios
- **端口**: 9528 (严格模式，不自动切换)

### 后端 (FastAPI + Python)
- **框架**: FastAPI + Uvicorn
- **Python版本**: 3.9+
- **端口**: 9527 (固定)
- **数据验证**: Pydantic

### AI集成
- **大模型**: DeepSeek V3 API
- **端点**: http://*************/gateway/ai-service/v1
- **模型**: deepseek-v3-0324

## 📁 项目结构

```
pptAgent/
├── config.json                 # 统一配置文件
├── backend/                     # 后端服务
│   ├── app/
│   │   ├── main.py             # FastAPI 主应用
│   │   ├── api/                # API 路由
│   │   │   ├── chat.py         # 对话相关API
│   │   │   ├── generate.py     # PPT生成API
│   │   │   └── templates.py    # 模板管理API (旧系统)
│   │   ├── services/           # 业务逻辑
│   │   │   ├── chat_service.py # 对话服务
│   │   │   ├── template_manager.py # 模板管理
│   │   │   └── moffee_service.py   # PPT生成服务
│   │   └── template_routes.py  # 旧API系统路由
│   ├── run_server.py           # 服务启动脚本
│   └── templates/              # 模板存储目录
├── frontend/                   # 前端应用
│   ├── src/
│   │   ├── components/         # React组件
│   │   │   ├── Steps/          # 步骤组件
│   │   │   │   ├── TemplateStep.jsx    # 模板选择
│   │   │   │   ├── ChatStep.jsx        # 对话阶段
│   │   │   │   ├── OutlineStep.jsx     # 大纲确认
│   │   │   │   └── ThemeStep.jsx       # 主题选择
│   │   │   └── TemplateSelector/       # 模板选择器
│   │   ├── pages/
│   │   │   └── HomePage.jsx    # 主页面
│   │   ├── services/
│   │   │   └── api.js          # API服务
│   │   └── config/
│   │       └── config.js       # 前端配置
│   ├── vite.config.js          # Vite配置
│   └── package.json
├── scripts/                    # 管理脚本
│   ├── port_manager.py         # 端口管理工具
│   ├── start_services.ps1      # 服务启动脚本
│   └── check_ports.ps1         # 端口检查脚本
└── 核心开发方案.md             # 核心开发计划
```

## 🔧 端口配置

### 严格端口管理
- **后端服务**: 9527 (固定)
- **前端服务**: 9528 (严格模式，端口被占用时启动失败)
- **配置文件**: config.json 统一管理
- **前端配置**: vite.config.js 中设置 `strictPort: true`

### 端口检查命令
```bash
# Python快速检查
python -c "
import socket
def check_port(port):
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(1)
            return s.connect_ex(('127.0.0.1', port)) == 0
    except: return False
print('Backend (9527):', 'IN USE' if check_port(9527) else 'AVAILABLE')
print('Frontend (9528):', 'IN USE' if check_port(9528) else 'AVAILABLE')
"

# 详细端口信息
netstat -ano | findstr ":952"
```

## 🚀 启动服务

### 后端启动
```bash
cd backend
python run_server.py
# 或
uvicorn app.main:app --host 127.0.0.1 --port 9527 --reload
```

### 前端启动
```bash
cd frontend
npm run dev
# 服务将在 http://127.0.0.1:9528 启动
```

## 🔄 工作流程

### 1. 模板选择阶段
- **组件**: TemplateStep.jsx
- **API**: GET /api/templates/
- **功能**: 显示已上传的模板，支持预览和选择
- **状态检查**: template.status === 'ready'

### 2. 对话阶段
- **组件**: ChatStep.jsx
- **API**: POST /api/chat/template-chat
- **功能**: 用户描述需求，AI提供建议
- **数据流**: 真实后端调用，支持对话历史

### 3. 大纲生成
- **触发**: 点击"生成大纲"按钮
- **API**: POST /api/chat/outline 或 /api/chat/generate-outline
- **响应格式**: 
```json
{
  "success": true,
  "data": {
    "outline": "大纲内容或对象"
  },
  "message": "大纲生成成功"
}
```

### 4. 大纲确认
- **组件**: OutlineStep.jsx
- **功能**: 显示和编辑生成的大纲
- **类型处理**: 支持字符串和对象格式的大纲数据
- **验证**: 修复了 `outline.trim is not a function` 错误

### 5. 主题选择和PPT生成
- **组件**: ThemeStep.jsx
- **功能**: 选择PPT主题，生成最终文件

## 🐛 已修复的问题

### 1. 模板选择问题
- **问题**: 提示"请选择就绪的模板"
- **原因**: API响应缺少 status 字段
- **修复**: 
  - 在 TemplateInfo 模型中添加 `status: str = "ready"`
  - 在 template_manager.py 中添加 `"status": "ready"` 字段
  - 添加 `/api/templates/` 路由支持

### 2. 对话阶段无后端调用
- **问题**: 只有前端模拟响应
- **修复**: 在 handleSendMessage 中添加真实的 `/api/chat/template-chat` 调用

### 3. 大纲生成失败
- **问题**: 前端提示生成失败，但后端成功
- **原因**: API响应格式不匹配
- **修复**: 修改后端返回格式为 `{success: true, data: {outline: ...}}`

### 4. OutlineStep组件错误
- **问题**: `outline.trim is not a function`
- **原因**: outline可能是对象类型
- **修复**: 添加类型检查和转换逻辑

## 📡 API接口

### 模板相关
```
GET /api/templates/          # 获取模板列表
GET /api/templates/all       # 获取所有模板
POST /api/templates/upload   # 上传模板
```

### 对话相关
```
POST /api/chat/template-chat    # 模板感知对话
POST /api/chat/outline         # 生成大纲
POST /api/chat/generate-outline # 生成大纲(别名)
POST /api/chat/refine-outline   # 优化大纲
```

### 生成相关
```
POST /api/generate/preview   # 生成预览
POST /api/generate/pptx      # 生成PPTX文件
```

## 🔍 调试和测试

### 后端健康检查
```bash
curl http://localhost:9527/health
```

### 前端访问
```
http://127.0.0.1:9528
```

### API测试示例
```bash
# 测试大纲生成
curl -X POST http://localhost:9527/api/chat/outline \
  -H "Content-Type: application/json" \
  -d '{"user_input":"创建AI PPT","context":"测试"}'
```

## ⚠️ 注意事项

1. **端口管理**: 严格使用9527和9528端口，不使用9529
2. **数据类型**: 大纲数据支持字符串和对象两种格式
3. **API超时**: LLM调用可能较慢，建议设置30秒以上超时
4. **错误处理**: 所有API调用都有备用错误处理机制
5. **配置文件**: 所有配置集中在 config.json 中管理

## 🔄 开发状态

- ✅ 模板选择功能正常
- ✅ 对话阶段真实后端调用
- ✅ 大纲生成和显示正常
- ✅ 端口管理优化完成
- ✅ 错误处理机制完善
- 🔄 PPT生成和导出功能待测试

## 🛠️ 关键代码位置

### 前端关键文件
```javascript
// HomePage.jsx - 主要状态管理和流程控制
const [outline, setOutline] = useState('');  // 大纲状态
const handleSendMessage = async () => { ... }  // 对话处理
const handleGenerateOutline = async () => { ... }  // 大纲生成

// OutlineStep.jsx - 大纲显示和编辑
value={typeof outline === 'string' ? outline : JSON.stringify(outline, null, 2)}
disabled={!outline || (typeof outline === 'string' && !outline.trim())}

// TemplateSelector.jsx - 模板选择
if (template.status !== 'ready') { ... }  // 状态检查
```

### 后端关键文件
```python
# chat.py - API响应格式
return ChatResponse(
    success=True,
    data={"outline": outline},  # 包装在outline字段中
    message="大纲生成成功"
)

# template_routes.py - 模板API
@router.get("/")  # 支持 /api/templates/
@router.get("/all")  # 支持 /api/templates/all
class TemplateInfo(BaseModel):
    status: str = "ready"  # 添加状态字段
```

## 🔧 环境配置

### Python依赖
```bash
pip install fastapi uvicorn python-pptx requests aiohttp
```

### Node.js依赖
```bash
cd frontend
npm install react react-dom react-router-dom axios lucide-react
npm install -D vite @vitejs/plugin-react tailwindcss
```

### 配置文件检查
```json
// config.json
{
  "ports": {
    "backend": 9527,
    "frontend": 9528
  },
  "urls": {
    "backend": "http://localhost:9527",
    "frontend": "http://localhost:9528"
  }
}
```

## 📊 当前服务状态

根据最新检查，当前服务状态：
- ✅ 后端服务 (9527): 正常运行
- ✅ 前端服务 (9528): 正常运行
- ✅ 模板选择: 功能正常
- ✅ 对话生成: 真实后端调用
- ✅ 大纲生成: 数据格式已修复
- ✅ 端口管理: 严格模式已启用

## 🚨 常见问题解决

### 1. 端口被占用
```bash
# 检查占用进程
netstat -ano | findstr ":9528"
# 终止进程
taskkill /F /PID <PID>
```

### 2. 前端启动失败
- 检查 vite.config.js 中的 strictPort 设置
- 确保 config.json 格式正确
- 检查 node_modules 是否完整

### 3. API调用超时
- LLM调用可能需要15-30秒
- 检查网络连接到 *************
- 确认 DeepSeek API 密钥有效

### 4. 大纲显示问题
- 检查 outline 数据类型
- 确认 API 返回格式为 `{data: {outline: ...}}`
- 验证 OutlineStep 组件的类型处理

## 📞 新会话开发指南

在新会话窗口中开展开发工作时：

1. **首先阅读本文档** 了解项目完整状态
2. **检查服务状态** 使用端口检查命令
3. **查看核心开发方案** 了解下一步开发计划
4. **测试现有功能** 确保基础功能正常
5. **参考已修复问题** 避免重复问题

### 重要提醒
- 所有端口配置都已优化，请勿修改为9529
- 大纲数据类型处理已完善，支持字符串和对象
- API响应格式已标准化，请保持一致性
- 前端使用严格端口模式，确保服务一致性
