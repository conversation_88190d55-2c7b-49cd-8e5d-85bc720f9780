#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试新的基于成品PPT模板的API集成
"""

import requests
import json
import time

def test_new_template_ppt_api():
    """测试新的模板PPT API"""
    try:
        print('🚀 测试新的基于成品PPT模板的API')
        print('=' * 60)
        
        template_id = 'template_20250708_221451_adc64662'
        user_context = '''
        用户需求：生成一个关于人工智能在医疗诊断中应用的PPT
        
        用户对话：
        用户：我需要制作一个关于AI医疗诊断的演示文稿
        助手：好的，我来为您生成一个专业的AI医疗诊断PPT
        用户：重点介绍技术原理、应用场景和发展前景
        助手：明白了，我会重点突出这三个方面的内容
        '''
        
        print(f'📋 测试参数:')
        print(f'  模板ID: {template_id}')
        print(f'  用户上下文长度: {len(user_context)}字符')
        print()
        
        # 步骤1: 测试模板解析API
        print('📖 步骤1: 测试模板解析API')
        print('-' * 40)
        
        parse_url = 'http://localhost:9527/api/template-ppt/parse-template'
        parse_data = {"template_id": template_id}
        
        print(f'调用: {parse_url}')
        parse_response = requests.post(parse_url, json=parse_data, timeout=30)
        
        if parse_response.status_code == 200:
            parse_result = parse_response.json()
            if parse_result.get("success"):
                print('✅ 模板解析成功')
                template_structure = parse_result["data"]["template_structure"]
                print(f'  总页数: {parse_result["data"]["total_pages"]}')
                print(f'  总占位符: {parse_result["data"]["total_placeholders"]}')
            else:
                print(f'❌ 模板解析失败: {parse_result.get("message")}')
                return False
        else:
            print(f'❌ 模板解析API调用失败: {parse_response.status_code}')
            return False
        
        print()
        
        # 步骤2: 测试内容生成API
        print('🤖 步骤2: 测试内容生成API')
        print('-' * 40)
        
        generate_url = 'http://localhost:9527/api/template-ppt/generate-content'
        generate_data = {
            "template_id": template_id,
            "user_context": user_context,
            "output_filename": "test_api_integration.pptx"
        }
        
        print(f'调用: {generate_url}')
        print('⏳ 正在调用大模型生成内容...')
        
        generate_response = requests.post(generate_url, json=generate_data, timeout=120)
        
        if generate_response.status_code == 200:
            generate_result = generate_response.json()
            if generate_result.get("success"):
                print('✅ 内容生成成功')
                generated_content = generate_result["data"]["generated_content"]
                formatted_content = generate_result["data"]["formatted_content"]
                print(f'  生成页数: {len(generated_content)}')
                print(f'  格式化内容页数: {len(formatted_content)}')
                
                # 显示前2页的生成内容
                print('\n📝 前2页生成内容预览:')
                for i, (page_key, page_data) in enumerate(list(generated_content.items())[:2]):
                    print(f'{page_key}:')
                    for element_key, element_data in page_data.items():
                        if isinstance(element_data, dict):
                            content_preview = element_data.get("content", "")[:50]
                            print(f'  {element_key}: {content_preview}...')
                
            else:
                print(f'❌ 内容生成失败: {generate_result.get("message")}')
                return False
        else:
            print(f'❌ 内容生成API调用失败: {generate_response.status_code}')
            print(f'响应: {generate_response.text}')
            return False
        
        print()
        
        # 步骤3: 测试确认生成API
        print('📄 步骤3: 测试确认生成API')
        print('-' * 40)
        
        confirm_url = 'http://localhost:9527/api/template-ppt/confirm-and-generate'
        confirm_data = {
            "template_id": template_id,
            "confirmed_content": generated_content,
            "output_filename": "test_api_integration_final.pptx"
        }
        
        print(f'调用: {confirm_url}')
        confirm_response = requests.post(confirm_url, json=confirm_data, timeout=60)
        
        if confirm_response.status_code == 200:
            confirm_result = confirm_response.json()
            if confirm_result.get("success"):
                print('✅ PPT生成成功')
                file_data = confirm_result["data"]
                print(f'  文件路径: {file_data["file_path"]}')
                print(f'  文件大小: {file_data["file_size"]/1024/1024:.1f} MB')
                print(f'  下载URL: {file_data["download_url"]}')
                
                return True
            else:
                print(f'❌ PPT生成失败: {confirm_result.get("message")}')
                return False
        else:
            print(f'❌ 确认生成API调用失败: {confirm_response.status_code}')
            print(f'响应: {confirm_response.text}')
            return False
            
    except Exception as e:
        print(f'❌ 测试异常: {e}')
        import traceback
        traceback.print_exc()
        return False

def test_sse_with_json():
    """测试SSE是否使用新的API"""
    try:
        print('\n🔄 测试SSE是否使用新的API')
        print('-' * 40)
        
        # 创建JSON格式的内容
        json_outline = {
            "title": "AI医疗诊断技术",
            "subtitle": "人工智能在医疗领域的创新应用",
            "slides": [
                {
                    "title": "技术背景",
                    "content": ["AI技术发展现状", "医疗诊断面临的挑战"]
                },
                {
                    "title": "核心技术",
                    "content": ["深度学习算法", "图像识别技术", "数据分析方法"]
                }
            ]
        }
        
        template_id = 'template_20250708_221451_adc64662'
        content = json.dumps(json_outline, ensure_ascii=False)
        user_input = 'AI医疗诊断技术演示'
        
        sse_url = f'http://localhost:9527/api/sse/md2pptx/{template_id}'
        sse_data = {
            "content": content,
            "user_input": user_input,
            "apply_patches": True
        }
        
        print(f'调用SSE: {sse_url}')
        print('📡 发送JSON格式内容...')
        
        # 发送请求但不等待完整响应（只检查是否调用了新API）
        response = requests.post(sse_url, json=sse_data, timeout=10, stream=True)
        
        if response.status_code == 200:
            print('✅ SSE请求成功发送')
            print('⏳ 检查是否使用新的模板PPT生成器...')
            
            # 读取前几条SSE消息
            for i, line in enumerate(response.iter_lines(decode_unicode=True)):
                if i > 10:  # 只读取前10条消息
                    break
                    
                if line.startswith('data: '):
                    try:
                        data = json.loads(line[6:])
                        message = data.get('message', '')
                        
                        if '新的基于成品PPT模板的生成器' in message or '5步流程' in message:
                            print('✅ 检测到使用新的模板PPT生成器!')
                            return True
                        elif '专业模板引擎' in message:
                            print('⚠️  使用的是旧的专业模板引擎')
                            
                        print(f'  消息: {message}')
                    except:
                        continue
            
            print('⚠️  未明确检测到新API的使用')
            return False
        else:
            print(f'❌ SSE请求失败: {response.status_code}')
            return False
            
    except Exception as e:
        print(f'❌ SSE测试异常: {e}')
        return False

if __name__ == "__main__":
    print('🚀 开始测试新的基于成品PPT模板的API集成')
    
    # 测试新的API
    api_success = test_new_template_ppt_api()
    
    # 测试SSE集成
    sse_success = test_sse_with_json()
    
    print(f'\n🎯 测试结果:')
    print(f'  新API测试: {"✅ 成功" if api_success else "❌ 失败"}')
    print(f'  SSE集成测试: {"✅ 成功" if sse_success else "❌ 失败"}')
    
    if api_success and sse_success:
        print('\n🎉 新的基于成品PPT模板的API完全集成成功!')
        print('现在系统会使用您的新方案生成PPT!')
    elif api_success:
        print('\n✅ 新API工作正常，但SSE可能还在使用旧方案')
        print('建议在前端直接调用新API')
    else:
        print('\n❌ 新API集成需要进一步调试')
