import sys
sys.path.insert(0, './backend')

from app.services.outline_converter import outline_converter

# 测试JSON大纲
json_outline = {
    "title": "测试演示文稿",
    "subtitle": "格式转换测试",
    "slides": [
        {
            "title": "封面",
            "content": ["测试演示文稿", "格式转换测试", "2025年"]
        },
        {
            "title": "内容页",
            "content": ["项目1", "项目2", "项目3"]
        }
    ]
}

print('🔄 测试JSON到Markdown转换...')
markdown_result = outline_converter.json_to_markdown(json_outline, 'template_20250708_221451_adc64662.pptx')

print('✅ 转换成功！')
print('Markdown内容:')
print('=' * 50)
print(markdown_result)
print('=' * 50)
