#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent))

def test_safe_version():
    """测试安全版本"""
    try:
        from app.services.professional_ppt_generator import professional_ppt_generator
        
        print('🛡️  测试安全版本的专业PPT生成器...')
        print('安全改进：')
        print('1. 不破坏原PPT结构')
        print('2. 更安全的文字替换方法')
        print('3. 错误容错机制')
        print('4. 详细的调试日志')
        print()
        
        # 测试参数
        template_id = 'template_20250708_221451_adc64662'
        user_request = '人工智能在教育领域的创新应用'
        output_filename = 'test_safe_version.pptx'
        
        print(f'模板: {template_id}')
        print(f'需求: {user_request}')
        print()
        
        # 执行生成（使用测试模式）
        result = professional_ppt_generator.generate_ppt_from_template(
            template_id, user_request, output_filename, test_mode=True
        )
        
        if result.get('success'):
            print('✅ 生成成功!')
            print(f'文件: {result["file_path"]}')
            print(f'大小: {result["file_size"]/1024/1024:.1f} MB')
            
            # 尝试加载PPT验证结构
            print('\n🔍 验证PPT结构:')
            
            try:
                from pptx import Presentation
                prs = Presentation(result["file_path"])
                print(f'✅ PPT结构正常')
                print(f'页数: {len(prs.slides)}')
                
                # 尝试访问每一页
                accessible_slides = 0
                for i in range(len(prs.slides)):
                    try:
                        slide = prs.slides[i]
                        accessible_slides += 1
                    except Exception as e:
                        print(f'❌ 第{i+1}页访问失败: {e}')
                
                print(f'可访问页数: {accessible_slides}/{len(prs.slides)}')
                
                if accessible_slides == len(prs.slides):
                    print('✅ 所有页面都可以正常访问')
                    
                    # 检查内容质量
                    print('\n📝 内容质量检查:')
                    
                    problem_count = 0
                    for i in range(min(3, len(prs.slides))):  # 检查前3页
                        try:
                            slide = prs.slides[i]
                            text_shapes = []
                            
                            for shape in slide.shapes:
                                try:
                                    if hasattr(shape, 'text') and shape.text and shape.text.strip():
                                        text_shapes.append(shape)
                                except:
                                    continue
                            
                            print(f'第{i+1}页: {len(text_shapes)}个文字形状')
                            
                            for j, shape in enumerate(text_shapes[:2]):
                                try:
                                    text_content = shape.text.strip()
                                    text_preview = text_content[:60].replace('\n', ' ')
                                    print(f'  文字{j+1}: {text_preview}')
                                    
                                    # 检查问题
                                    if any(keyword in text_content for keyword in [
                                        "请生成", "PPT", "提示词", "占位符", "这里是关于", "补充内容以达到要求长度"
                                    ]):
                                        print(f'    ❌ 发现提示词内容')
                                        problem_count += 1
                                    
                                    if "{{" in text_content and "}}" in text_content:
                                        print(f'    ❌ 发现占位符未替换')
                                        problem_count += 1
                                        
                                except Exception as e:
                                    print(f'    ⚠️  文字读取失败: {e}')
                                    
                        except Exception as e:
                            print(f'第{i+1}页处理失败: {e}')
                    
                    print(f'\n📊 质量评估:')
                    print(f'发现问题: {problem_count}个')
                    
                    if problem_count == 0:
                        print('✅ 内容质量良好，所有修复都成功！')
                        return True
                    else:
                        print(f'⚠️  仍有{problem_count}个问题')
                        return False
                else:
                    print('❌ PPT结构仍有问题')
                    return False
                    
            except Exception as e:
                print(f'❌ PPT结构验证失败: {e}')
                return False
            
        else:
            print('❌ 生成失败!')
            print(f'错误: {result.get("error")}')
            return False
            
    except Exception as e:
        print(f'❌ 测试异常: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_safe_version()
    print(f'\n🎯 最终结果: {"✅ 完全成功" if success else "❌ 需要继续修复"}')
