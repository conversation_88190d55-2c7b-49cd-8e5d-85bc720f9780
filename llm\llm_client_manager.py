# -*- coding: utf-8 -*-
"""
大模型客户端主管理器 (此类现在命名为 LLMClient 以满足 llm_utils.py 的导入需求)
整合连接管理、提示词处理、响应处理等功能
"""

import logging

from .connection_manager import ConnectionManager
from .prompt_processor import PromptProcessor
from .response_processor import ResponseProcessor

logger = logging.getLogger(__name__)

class LLMClient: # Renamed class back to LLMClient
    """大模型客户端，提供与大模型交互的方法，并管理连接、提示词和响应处理。"""
    
    def __init__(self, config=None):
        """
        初始化大模型客户端

        Args:
            config: 大模型配置，如果为None则使用默认配置 (通常是 MODEL_CONFIG)
        """
        self._current_temperature = 0.01  # 默认temperature
        if config is None:
            try:
                from config import MODEL_CONFIG
                config = MODEL_CONFIG
                logger.info("LLMClient (in llm.llm_client_manager): 使用来自 config.py 的 MODEL_CONFIG。")
            except ImportError:
                logger.error("LLMClient (in llm.llm_client_manager): 无法从 config.py 导入 MODEL_CONFIG，将使用空配置。")
                config = {} # Fallback to empty config if import fails
        
        self.config = config
        
        self._connection_manager = None
        self._prompt_processor = None
        self._response_processor = None
        
        logger.info("大模型客户端 (llm.llm_client_manager.LLMClient) 初始化成功")
    
    def _get_connection_manager(self):
        """获取连接管理器（延迟初始化）"""
        if self._connection_manager is None:
            self._connection_manager = ConnectionManager(self.config)
        return self._connection_manager
    
    def _get_prompt_processor(self):
        """获取提示词处理器（延迟初始化）"""
        if self._prompt_processor is None:
            self._prompt_processor = PromptProcessor()
        return self._prompt_processor
    
    def _get_response_processor(self):
        """获取响应处理器（延迟初始化）"""
        if self._response_processor is None:
            self._response_processor = ResponseProcessor()
        return self._response_processor
    
    def process_prompt(self, prompt, placeholder=None):
        """
        处理提示词，替换占位符
        
        Args:
            prompt: 提示词
            placeholder: 占位符替换字典
            
        Returns:
            str: 处理后的提示词
        """
        processor = self._get_prompt_processor()
        return processor.process_prompt(prompt, placeholder)
    
    def standardize_prompt(self, prompt):
        """
        标准化提示词格式
        
        Args:
            prompt: 提示词
            
        Returns:
            str: 标准化后的提示词
        """
        processor = self._get_prompt_processor()
        return processor.standardize_prompt(prompt)
    
    def call_model(self, prompt, temperature=0.01, max_tokens=64000, placeholder=None, max_retries=10, retry_delay=5, is_test=False):
        """
        调用大模型
        
        Args:
            prompt: 提示词
            temperature: 温度参数
            max_tokens: 最大生成token数
            placeholder: 占位符替换字典
            max_retries: 最大重试次数
            retry_delay: 重试延迟时间（秒）
            is_test: 是否是测试请求
            
        Returns:
            str: 大模型返回的文本
        """
        prompt_processor = self._get_prompt_processor()
        processed_prompt = prompt_processor.process_prompt(prompt, placeholder)
        standardized_prompt = prompt_processor.standardize_prompt(processed_prompt)
        
        messages = [{"role": "user", "content": standardized_prompt}]
        
        connection_manager = self._get_connection_manager()
        return connection_manager.call_model_with_retry(
            messages=messages,
            temperature=temperature,
            max_tokens=max_tokens,
            max_retries=max_retries,
            retry_delay=retry_delay,
            is_test=is_test
        )

    def set_temperature(self, temperature):
        """设置当前temperature值"""
        self._current_temperature = temperature

    def get_temperature(self):
        """获取当前temperature值"""
        return self._current_temperature
    
    def batch_classify_work_events(self, work_events, flat_paths):
        """
        批量处理工单，分析是否需要新分类
        
        Args:
            work_events: 工单数据列表
            flat_paths: 扁平化的分类路径列表
            
        Returns:
            dict: 工单分类结果，包含matches列表
        """
        try:
            prompt_processor = self._get_prompt_processor()
            from prompt_templates import PROMPTS
            template = PROMPTS['category_matching']
            prompt = prompt_processor.build_classification_prompt(work_events, flat_paths, template)
            
            # 简化配置，不保存响应文件
            if False:  # 禁用文件保存功能
                work_event_ids = [event.get('id', '') for event in work_events if event.get('id')]
                prompt_processor.save_prompt_to_file(prompt, "classification_prompt", work_event_ids)
                if len(work_events) > 0:
                    try:
                        with open("first_batch_prompt.txt", "w", encoding="utf-8") as f: f.write(prompt)
                        logger.info("✅ 第一批工单提示词已保存到first_batch_prompt.txt")
                    except Exception as e: logger.warning(f"保存first_batch_prompt.txt失败: {e}")
            
            response = self.call_model(prompt, temperature=self._current_temperature)
            
            if False:  # 禁用文件保存功能
                work_event_ids_for_response = [event.get('id', '') for event in work_events if event.get('id')]
                prompt_processor.save_response_to_file(response, "classification_response", work_event_ids_for_response)
            
            response_processor = self._get_response_processor()
            result = response_processor.process_classification_response(response, work_events)
            
            is_valid, error_msg = response_processor.validate_classification_result(result)
            if not is_valid: logger.warning(f"分类结果验证失败: {error_msg}")
            
            return result
            
        except Exception as e:
            logger.error(f"批量分类工单失败: {str(e)}", exc_info=True)
            response_processor = self._get_response_processor()
            return response_processor._create_default_matches(work_events)
    
    def aggregate_categories(self, existing_categories, new_categories):
        """
        聚合分类体系
        
        Args:
            existing_categories: 现有分类体系
            new_categories: 新生成的分类体系
            
        Returns:
            dict: 聚合后的分类体系，包含categories列表
        """
        try:
            prompt_processor = self._get_prompt_processor()
            from prompt_templates import PROMPTS
            template = PROMPTS['category_aggregation']
            prompt = prompt_processor.build_aggregation_prompt(new_categories, template)
            
            from config import TASK_CONFIG
            if TASK_CONFIG.get('save_filtered_model_response', False):
                prompt_processor.save_prompt_to_file(prompt, "aggregation_prompt")
            
            max_retries = 15
            json_retries = 5  
            
            for json_retry in range(json_retries):
                try:
                    response = self.call_model(prompt, temperature=0.01, max_retries=max_retries)
                    if TASK_CONFIG.get('save_filtered_model_response', False):
                        prompt_processor.save_response_to_file(response, f"aggregation_response_retry_{json_retry}")
                    
                    response_processor = self._get_response_processor()
                    result = response_processor.process_aggregation_response(response)
                    
                    is_valid, error_msg = response_processor.validate_aggregation_result(result)
                    if not is_valid:
                        logger.warning(f"聚合结果验证失败: {error_msg}")
                        if json_retry < json_retries - 1:
                            logger.warning(f"聚合结果验证失败，尝试重新调用大模型 (尝试 {json_retry+1}/{json_retries})")
                            continue
                    return result
                except Exception as e:
                    logger.error(f"聚合分类单次尝试失败: {str(e)}", exc_info=True) 
                    if json_retry == json_retries - 1:
                        logger.error("所有聚合重试均失败。")
                        response_processor = self._get_response_processor()
                        return response_processor._create_default_categories()
                    else:
                        logger.warning(f"聚合分类失败，尝试重新调用大模型 (尝试 {json_retry+1}/{json_retries})")
                        continue
            
            response_processor = self._get_response_processor()
            return response_processor._create_default_categories()
            
        except Exception as e:
            logger.error(f"聚合分类顶层异常: {str(e)}", exc_info=True)
            response_processor = self._get_response_processor()
            return response_processor._create_default_categories()
    
    def test_connection(self):
        """测试连接"""
        try:
            connection_manager = self._get_connection_manager()
            return connection_manager.test_connection()
        except Exception as e:
            logger.error(f"测试连接失败: {str(e)}", exc_info=True)
            return False, f"测试连接失败: {str(e)}"
    
    def validate_config(self):
        """验证配置"""
        try:
            self._get_connection_manager() 
            return True, "配置验证成功"
        except Exception as e:
            logger.error(f"配置验证失败: {str(e)}", exc_info=True)
            return False, f"配置验证失败: {str(e)}"
    
    def get_model_info(self):
        """获取模型信息"""
        return {
            "model": self.config.get('model', ''),
            "api_base": self.config.get('api_base', ''),
            "timeout": self.config.get('timeout', 60),
            "use_ssh": self.config.get('use_ssh', False)
        }
    
    def close(self):
        """关闭连接"""
        if self._connection_manager:
            self._connection_manager.close()
            self._connection_manager = None
        logger.info("大模型客户端 (llm.llm_client_manager.LLMClient) 已关闭")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
    
    def format_response_for_logging(self, response, max_length=200):
        prompt_processor = self._get_prompt_processor()
        return prompt_processor.format_response_for_logging(response, max_length)
    
    def validate_prompt_template(self, template, required_placeholders=None):
        prompt_processor = self._get_prompt_processor()
        return prompt_processor.validate_prompt_template(template, required_placeholders)
    
    def extract_placeholders(self, template):
        prompt_processor = self._get_prompt_processor()
        return prompt_processor.extract_placeholders(template)

    def get_tag_merge_suggestions(self, merge_prompt: str, temperature: float = 0.2, max_tokens: int = 64000) -> str:
        logger.info(f"LLMClient: Sending tag merge prompt to model (first 200 chars): {merge_prompt[:200]}...")
        standardized_prompt = self._get_prompt_processor().standardize_prompt(merge_prompt)
        messages = [{"role": "user", "content": standardized_prompt}]
        connection_manager = self._get_connection_manager()
        response_text = connection_manager.call_model_with_retry(
            messages=messages,
            temperature=temperature,
            max_tokens=max_tokens,
        )
        logger.info(f"LLMClient: Received tag merge suggestion response (first 200 chars): {response_text[:200]}...")
        return response_text
    
    def batch_classify_work_events_with_filtered_tags(self, work_events, filtered_categories_str):
        try:
            prompt_processor = self._get_prompt_processor()
            from prompt_templates import PROMPTS
            template = PROMPTS['category_matching']
            
            # 简化配置，使用默认值
            use_aggregation = False
            work_events_text = prompt_processor.build_work_events_text(work_events, use_aggregation)
            
            prompt = template.replace('{existing_categories}', filtered_categories_str)
            prompt = prompt.replace('{work_events}', work_events_text)
            prompt = prompt_processor.standardize_prompt(prompt)
            
            if TASK_CONFIG.get('save_filtered_model_response', False):
                work_event_ids = [event.get('id', '') for event in work_events if event.get('id')]
                prompt_processor.save_prompt_to_file(prompt, "filtered_classification_prompt", work_event_ids)
                if len(work_events) > 0:
                    try:
                        with open("first_batch_prompt.txt", "w", encoding="utf-8") as f: f.write(prompt)
                        logger.info("✅ 第一批工单提示词已保存到first_batch_prompt.txt（筛选标签版本）")
                    except Exception as e: logger.warning(f"保存first_batch_prompt.txt失败: {e}")
            
            response = self.call_model(prompt, temperature=0.01)
            
            if TASK_CONFIG.get('save_filtered_model_response', False):
                work_event_ids_for_response = [event.get('id', '') for event in work_events if event.get('id')]
                prompt_processor.save_response_to_file(response, "filtered_classification_response", work_event_ids_for_response)
            
            response_processor = self._get_response_processor()
            result = response_processor.process_classification_response(response, work_events)
            
            is_valid, error_msg = response_processor.validate_classification_result(result)
            if not is_valid: logger.warning(f"筛选标签分类结果验证失败: {error_msg}")
            
            return result
            
        except Exception as e:
            logger.error(f"使用筛选标签批量分类工单失败: {str(e)}", exc_info=True)
            response_processor = self._get_response_processor()
            return response_processor._create_default_matches(work_events)
