"""
简单测试专业PPT生成器
"""

import sys
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 添加路径
sys.path.insert(0, str(Path(__file__).parent))

try:
    from app.services.professional_ppt_generator import professional_ppt_generator
    
    print('🔄 简单测试专业PPT生成器...')
    
    template_id = 'template_20250708_221451_adc64662'
    user_request = '请生成一个关于人工智能发展的PPT'
    output_filename = 'simple_test.pptx'
    
    print(f'模板ID: {template_id}')
    print(f'用户需求: {user_request}')
    print()
    
    # 执行生成
    result = professional_ppt_generator.generate_ppt_from_template(
        template_id, user_request, output_filename
    )
    
    if result.get('success'):
        print('✅ 生成成功!')
        print(f'文件: {result["file_path"]}')
        print(f'大小: {result["file_size"]/1024/1024:.1f} MB')
    else:
        print('❌ 生成失败!')
        print(f'错误: {result.get("error", "未知错误")}')

except Exception as e:
    print(f'❌ 测试异常: {e}')
    import traceback
    traceback.print_exc()
