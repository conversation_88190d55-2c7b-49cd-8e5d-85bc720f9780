#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from pathlib import Path
from pptx import Presentation

def check_ppt_result():
    """检查PPT生成结果"""
    
    file_path = "uploads/generated/end_to_end_test.pptx"
    
    try:
        print("检查PPT生成结果...")
        print(f"文件: {file_path}")
        
        if not Path(file_path).exists():
            print("❌ 文件不存在")
            return
        
        # 加载PPT
        prs = Presentation(file_path)
        file_size = Path(file_path).stat().st_size
        
        print(f"✅ 文件加载成功")
        print(f"📊 总页数: {len(prs.slides)}")
        print(f"📏 文件大小: {file_size/1024/1024:.1f} MB")
        
        # 检查内容质量
        has_original_text = False
        has_placeholder_text = False
        text_samples = []
        
        print(f"\n📄 内容检查:")
        
        for i, slide in enumerate(prs.slides[:5]):  # 检查前5页
            text_shapes = [shape for shape in slide.shapes if hasattr(shape, 'text') and shape.text.strip()]
            print(f"第{i+1}页: {len(text_shapes)}个文字形状")
            
            for j, shape in enumerate(text_shapes[:3]):  # 每页检查前3个文字
                text_content = shape.text.strip()
                text_preview = text_content[:60].replace('\n', ' ') if text_content else '(空)'
                print(f"  文字{j+1}: {text_preview}")
                
                # 检查问题
                if "{{" in text_content and "}}" in text_content:
                    has_placeholder_text = True
                    print(f"    ⚠️  发现占位符: {text_content}")
                
                if any(keyword in text_content for keyword in ["主讲人", "AiPPT", "PowerPoint design", "202X"]):
                    has_original_text = True
                    print(f"    ⚠️  发现原文字: {text_content[:50]}")
        
        print(f"\n🔍 问题检测:")
        print(f"原文字残留: {'❌ 有' if has_original_text else '✅ 无'}")
        print(f"占位符残留: {'❌ 有' if has_placeholder_text else '✅ 无'}")
        
        # 总体评估
        if file_size > 5 * 1024 * 1024:  # 大于5MB
            print(f"✅ 文件大小正常，保留了设计元素")
        else:
            print(f"⚠️  文件较小，可能丢失了设计元素")
        
        if not has_original_text and not has_placeholder_text:
            print(f"✅ 文字替换成功")
        else:
            print(f"❌ 文字替换有问题")
        
        print(f"\n🎯 测试结论:")
        if not has_original_text and not has_placeholder_text and file_size > 5 * 1024 * 1024:
            print("✅ 测试通过！专业PPT生成器工作正常")
        else:
            print("❌ 测试发现问题，需要进一步调整")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_ppt_result()
