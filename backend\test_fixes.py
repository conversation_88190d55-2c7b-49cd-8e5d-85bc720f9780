#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from pathlib import Path
from pptx import Presentation

sys.path.insert(0, str(Path(__file__).parent))

def test_fixes():
    """测试修复后的版本"""
    try:
        from app.services.professional_ppt_generator import professional_ppt_generator
        
        print('🔧 测试修复后的专业PPT生成器...')
        print('修复内容：')
        print('1. 重新设计提示词，明确每页数据结构')
        print('2. 确保占位符全局唯一')
        print('3. 改进后备内容生成，避免提示词内容')
        print('4. 添加内容质量验证')
        print()
        
        # 测试参数
        template_id = 'template_20250708_221451_adc64662'
        user_request = '人工智能在医疗诊断中的应用'
        output_filename = 'test_fixes.pptx'
        
        print(f'模板: {template_id}')
        print(f'需求: {user_request}')
        print()
        
        # 执行生成（使用测试模式）
        result = professional_ppt_generator.generate_ppt_from_template(
            template_id, user_request, output_filename, test_mode=True
        )
        
        if result.get('success'):
            print('✅ 生成成功!')
            print(f'文件: {result["file_path"]}')
            print(f'大小: {result["file_size"]/1024/1024:.1f} MB')
            
            # 详细验证生成结果
            print('\n🔍 详细验证结果:')
            
            # 加载PPT进行验证
            prs = Presentation(result["file_path"])
            print(f'页数: {len(prs.slides)}')
            
            # 检查前5页的内容质量
            problem_count = 0
            
            for i, slide in enumerate(prs.slides[:5]):
                print(f'\n--- 第{i+1}页 ---')
                text_shapes = [shape for shape in slide.shapes if hasattr(shape, 'text') and shape.text.strip()]
                print(f'文字形状数: {len(text_shapes)}')
                
                for j, shape in enumerate(text_shapes[:3]):
                    text_content = shape.text.strip()
                    text_preview = text_content[:80].replace('\n', ' ') if text_content else '(空)'
                    print(f'  文字{j+1}: {text_preview}')
                    
                    # 检查问题
                    if any(keyword in text_content for keyword in [
                        "请生成", "PPT", "提示词", "占位符", "用户需求", "这里是关于", "补充内容以达到要求长度"
                    ]):
                        print(f'    ❌ 发现提示词内容: {text_content[:50]}')
                        problem_count += 1
                    
                    if "{{" in text_content and "}}" in text_content:
                        print(f'    ❌ 发现占位符未替换: {text_content}')
                        problem_count += 1
                    
                    # 检查内容重复
                    if i > 0:  # 从第二页开始检查
                        prev_slide = prs.slides[i-1]
                        prev_texts = [s.text for s in prev_slide.shapes if hasattr(s, 'text')]
                        if text_content in prev_texts:
                            print(f'    ⚠️  发现重复内容: {text_content[:30]}')
            
            print(f'\n📊 问题统计:')
            print(f'发现问题数量: {problem_count}')
            
            if problem_count == 0:
                print('✅ 所有修复都成功！内容质量良好')
            else:
                print(f'❌ 仍有{problem_count}个问题需要进一步修复')
            
            return problem_count == 0
            
        else:
            print('❌ 生成失败!')
            print(f'错误: {result.get("error")}')
            return False
            
    except Exception as e:
        print(f'❌ 测试异常: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_fixes()
    print(f'\n🎯 测试结果: {"通过" if success else "需要进一步修复"}')
