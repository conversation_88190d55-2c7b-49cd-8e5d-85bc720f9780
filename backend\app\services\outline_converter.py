"""
大纲格式转换服务
将JSON格式的大纲转换为md2pptx兼容的Markdown格式
"""

import json
import logging
import shutil
import os
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class OutlineConverter:
    """大纲格式转换器"""
    
    def __init__(self):
        # 设置路径
        self.project_root = Path(__file__).parent.parent.parent.parent
        self.templates_dir = self.project_root / "backend" / "templates"
        self.md2pptx_dir = self.project_root / "md2pptx"

    def _ensure_template_available(self, template_name: str) -> str:
        """
        确保模板文件在md2pptx目录中可用

        Args:
            template_name: 模板文件名

        Returns:
            可用的模板文件名
        """
        if not template_name or template_name == 'Martin Template.pptx':
            return 'Martin Template.pptx'  # 默认模板已存在

        # 生成兼容模板名称
        base_name = template_name.replace('.pptx', '')
        compatible_name = f"{base_name}_compatible.pptx"

        # 检查兼容模板是否已存在
        compatible_template = self.md2pptx_dir / compatible_name
        if compatible_template.exists():
            logger.info(f"使用现有兼容模板: {compatible_name}")
            return compatible_name

        # 尝试创建兼容模板
        source_template = self.templates_dir / template_name
        if source_template.exists():
            try:
                # 创建兼容模板
                self._create_compatible_template(source_template, compatible_template)
                logger.info(f"兼容模板已创建: {compatible_name}")
                return compatible_name
            except Exception as e:
                logger.error(f"创建兼容模板失败: {e}")

        # 如果都失败了，使用默认模板
        logger.warning(f"模板不可用，使用默认模板: {template_name}")
        return 'Martin Template.pptx'

    def _create_compatible_template(self, source_path: Path, target_path: Path):
        """
        创建兼容的模板文件

        Args:
            source_path: 源模板路径
            target_path: 目标兼容模板路径
        """
        try:
            from pptx import Presentation

            # 使用默认模板作为基础（确保有完整的布局）
            default_template_path = self.md2pptx_dir / "Martin Template.pptx"

            if default_template_path.exists():
                # 复制默认模板作为兼容模板
                shutil.copy2(default_template_path, target_path)
                logger.info(f"基于默认模板创建兼容模板: {target_path.name}")
            else:
                # 如果默认模板不存在，直接复制源模板
                shutil.copy2(source_path, target_path)
                logger.warning(f"默认模板不存在，直接复制源模板: {target_path.name}")

        except Exception as e:
            logger.error(f"创建兼容模板时出错: {e}")
            # 回退：直接复制源模板
            shutil.copy2(source_path, target_path)
    
    def json_to_markdown(self, json_outline: str, template_name: Optional[str] = None) -> str:
        """
        将JSON格式的大纲转换为md2pptx兼容的Markdown格式
        
        Args:
            json_outline: JSON格式的大纲字符串
            template_name: 模板名称
            
        Returns:
            Markdown格式的内容
        """
        try:
            # 解析JSON
            if isinstance(json_outline, str):
                outline_data = json.loads(json_outline)
            else:
                outline_data = json_outline
            
            # 构建Markdown内容
            markdown_lines = []
            
            # 添加元数据
            markdown_lines.extend(self._build_metadata(outline_data, template_name))
            
            # 添加分隔符
            markdown_lines.append("")
            markdown_lines.append("---")
            markdown_lines.append("")
            
            # 添加幻灯片内容
            slides = outline_data.get('slides', [])
            for i, slide in enumerate(slides):
                slide_md = self._convert_slide_to_markdown(slide, i)
                markdown_lines.extend(slide_md)
                
                # 添加幻灯片分隔符（除了最后一张）
                if i < len(slides) - 1:
                    markdown_lines.append("")
                    markdown_lines.append("---")
                    markdown_lines.append("")
            
            result = "\n".join(markdown_lines)
            logger.info(f"JSON转Markdown完成，生成{len(slides)}张幻灯片")
            return result
            
        except Exception as e:
            logger.error(f"JSON转Markdown失败: {e}")
            return self._get_fallback_markdown(json_outline, template_name)
    
    def _build_metadata(self, outline_data: Dict[str, Any], template_name: Optional[str]) -> list:
        """构建元数据部分"""
        metadata_lines = []
        
        # 标题
        title = outline_data.get('title', '演示文稿')
        metadata_lines.append(f"title: {title}")
        
        # 副标题
        subtitle = outline_data.get('subtitle', '')
        if subtitle:
            metadata_lines.append(f"subtitle: {subtitle}")
        
        # 作者
        metadata_lines.append("author: AI助手")
        
        # 日期
        current_date = datetime.now().strftime("%Y-%m-%d")
        metadata_lines.append(f"date: {current_date}")
        
        # 模板 - 确保模板可用
        available_template = self._ensure_template_available(template_name)
        metadata_lines.append(f"template: {available_template}")
        
        # 其他设置
        metadata_lines.append("pageTitleSize: 24")
        metadata_lines.append("baseTextSize: 18")
        metadata_lines.append("numbers: no")
        
        return metadata_lines
    
    def _convert_slide_to_markdown(self, slide: Dict[str, Any], slide_index: int) -> list:
        """将单个幻灯片转换为Markdown"""
        slide_lines = []
        
        # 幻灯片标题
        title = slide.get('title', f'幻灯片 {slide_index + 1}')
        
        # 根据幻灯片类型决定标题级别
        if slide_index == 0:
            # 封面页使用一级标题
            slide_lines.append(f"# {title}")
        else:
            # 其他页面使用二级标题
            slide_lines.append(f"## {title}")
        
        slide_lines.append("")
        
        # 幻灯片内容
        content = slide.get('content', [])
        if isinstance(content, list):
            for item in content:
                if isinstance(item, str):
                    # 简单文本项
                    if item.strip():
                        slide_lines.append(f"- {item.strip()}")
                elif isinstance(item, dict):
                    # 复杂内容项
                    self._add_complex_content(slide_lines, item)
        elif isinstance(content, str):
            # 单个字符串内容
            slide_lines.append(content)
        
        return slide_lines
    
    def _add_complex_content(self, slide_lines: list, content_item: Dict[str, Any]):
        """添加复杂内容项"""
        if 'title' in content_item:
            slide_lines.append(f"### {content_item['title']}")
            slide_lines.append("")
        
        if 'items' in content_item:
            items = content_item['items']
            if isinstance(items, list):
                for item in items:
                    if isinstance(item, str):
                        slide_lines.append(f"- {item}")
                    elif isinstance(item, dict) and 'text' in item:
                        slide_lines.append(f"- {item['text']}")
        
        if 'description' in content_item:
            slide_lines.append(content_item['description'])
            slide_lines.append("")
    
    def _get_fallback_markdown(self, original_content: str, template_name: Optional[str]) -> str:
        """获取备用Markdown内容"""
        template_line = f"template: {template_name}" if template_name else "template: Martin Template.pptx"
        
        return f"""title: 演示文稿
author: AI助手
date: {datetime.now().strftime("%Y-%m-%d")}
{template_line}
pageTitleSize: 24
baseTextSize: 18
numbers: no

---

# 演示文稿

## 内容概述

基于提供的大纲生成演示文稿。

---

## 详细内容

{original_content[:500]}...

---

## 总结

感谢您的观看！
"""

    def markdown_to_json(self, markdown_content: str) -> Dict[str, Any]:
        """
        将Markdown格式转换为JSON格式（用于编辑界面）
        
        Args:
            markdown_content: Markdown内容
            
        Returns:
            JSON格式的大纲
        """
        try:
            lines = markdown_content.split('\n')
            
            # 解析元数据
            metadata = {}
            content_start = 0
            
            for i, line in enumerate(lines):
                if line.strip() == '---':
                    content_start = i + 1
                    break
                if ':' in line and not line.startswith('#'):
                    key, value = line.split(':', 1)
                    metadata[key.strip()] = value.strip()
            
            # 解析幻灯片
            slides = []
            current_slide = None
            
            for line in lines[content_start:]:
                line = line.strip()
                
                if line.startswith('# ') or line.startswith('## '):
                    # 新幻灯片
                    if current_slide:
                        slides.append(current_slide)
                    
                    title = line.lstrip('#').strip()
                    current_slide = {
                        'title': title,
                        'content': []
                    }
                elif line.startswith('- ') and current_slide:
                    # 列表项
                    current_slide['content'].append(line[2:].strip())
                elif line and current_slide and not line == '---':
                    # 其他内容
                    current_slide['content'].append(line)
            
            # 添加最后一张幻灯片
            if current_slide:
                slides.append(current_slide)
            
            result = {
                'title': metadata.get('title', '演示文稿'),
                'subtitle': metadata.get('subtitle', ''),
                'slides': slides
            }
            
            logger.info(f"Markdown转JSON完成，解析{len(slides)}张幻灯片")
            return result
            
        except Exception as e:
            logger.error(f"Markdown转JSON失败: {e}")
            return {
                'title': '演示文稿',
                'subtitle': '',
                'slides': [
                    {
                        'title': '内容',
                        'content': [markdown_content[:200] + '...']
                    }
                ]
            }


# 创建全局实例
outline_converter = OutlineConverter()
