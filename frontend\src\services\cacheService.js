/**
 * 缓存服务
 * 用于缓存PPT生成内容，避免重复调用大模型
 */

class CacheService {
  constructor() {
    this.cacheKey = 'ppt_generation_cache';
    this.maxCacheSize = 50; // 最多缓存50个条目
    this.cacheExpiry = 24 * 60 * 60 * 1000; // 24小时过期
  }

  /**
   * 生成缓存键
   * @param {string} templateId - 模板ID
   * @param {string} userInput - 用户输入
   * @returns {string} 缓存键
   */
  generateCacheKey(templateId, userInput) {
    // 使用模板ID和用户输入的hash作为缓存键
    const content = `${templateId}_${userInput}`;
    return this.simpleHash(content);
  }

  /**
   * 简单hash函数
   * @param {string} str - 要hash的字符串
   * @returns {string} hash值
   */
  simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * 获取缓存
   * @returns {Object} 缓存对象
   */
  getCache() {
    try {
      const cached = localStorage.getItem(this.cacheKey);
      return cached ? JSON.parse(cached) : {};
    } catch (error) {
      console.warn('读取缓存失败:', error);
      return {};
    }
  }

  /**
   * 保存缓存
   * @param {Object} cache - 缓存对象
   */
  saveCache(cache) {
    try {
      localStorage.setItem(this.cacheKey, JSON.stringify(cache));
    } catch (error) {
      console.warn('保存缓存失败:', error);
    }
  }

  /**
   * 检查缓存是否存在且未过期
   * @param {string} templateId - 模板ID
   * @param {string} userInput - 用户输入
   * @returns {Object|null} 缓存的内容或null
   */
  getCachedContent(templateId, userInput) {
    const cacheKey = this.generateCacheKey(templateId, userInput);
    const cache = this.getCache();
    
    if (cache[cacheKey]) {
      const cachedItem = cache[cacheKey];
      const now = Date.now();
      
      // 检查是否过期
      if (now - cachedItem.timestamp < this.cacheExpiry) {
        console.log('🎯 使用缓存内容:', cacheKey);
        return {
          content: cachedItem.content,
          fromCache: true,
          cachedAt: new Date(cachedItem.timestamp).toLocaleString()
        };
      } else {
        // 过期了，删除这个缓存项
        delete cache[cacheKey];
        this.saveCache(cache);
      }
    }
    
    return null;
  }

  /**
   * 缓存内容
   * @param {string} templateId - 模板ID
   * @param {string} userInput - 用户输入
   * @param {string} content - 生成的内容
   */
  cacheContent(templateId, userInput, content) {
    const cacheKey = this.generateCacheKey(templateId, userInput);
    const cache = this.getCache();
    
    // 添加新的缓存项
    cache[cacheKey] = {
      templateId,
      userInput: userInput.substring(0, 100), // 只保存前100个字符用于显示
      content,
      timestamp: Date.now()
    };
    
    // 检查缓存大小，如果超过限制则删除最旧的
    const cacheKeys = Object.keys(cache);
    if (cacheKeys.length > this.maxCacheSize) {
      // 按时间戳排序，删除最旧的
      const sortedKeys = cacheKeys.sort((a, b) => 
        cache[a].timestamp - cache[b].timestamp
      );
      
      const keysToDelete = sortedKeys.slice(0, cacheKeys.length - this.maxCacheSize);
      keysToDelete.forEach(key => delete cache[key]);
    }
    
    this.saveCache(cache);
    console.log('💾 内容已缓存:', cacheKey);
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计
   */
  getCacheStats() {
    const cache = this.getCache();
    const cacheKeys = Object.keys(cache);
    
    return {
      totalItems: cacheKeys.length,
      maxSize: this.maxCacheSize,
      expiryHours: this.cacheExpiry / (60 * 60 * 1000),
      oldestItem: cacheKeys.length > 0 ? 
        Math.min(...cacheKeys.map(key => cache[key].timestamp)) : null,
      newestItem: cacheKeys.length > 0 ? 
        Math.max(...cacheKeys.map(key => cache[key].timestamp)) : null
    };
  }

  /**
   * 清空所有缓存
   */
  clearCache() {
    localStorage.removeItem(this.cacheKey);
    console.log('🗑️ 缓存已清空');
  }

  /**
   * 获取缓存列表（用于管理界面）
   * @returns {Array} 缓存项列表
   */
  getCacheList() {
    const cache = this.getCache();
    return Object.keys(cache).map(key => ({
      key,
      templateId: cache[key].templateId,
      userInput: cache[key].userInput,
      timestamp: cache[key].timestamp,
      cachedAt: new Date(cache[key].timestamp).toLocaleString(),
      size: JSON.stringify(cache[key].content).length
    })).sort((a, b) => b.timestamp - a.timestamp);
  }

  /**
   * 删除特定缓存项
   * @param {string} cacheKey - 缓存键
   */
  deleteCacheItem(cacheKey) {
    const cache = this.getCache();
    if (cache[cacheKey]) {
      delete cache[cacheKey];
      this.saveCache(cache);
      console.log('🗑️ 缓存项已删除:', cacheKey);
    }
  }
}

// 创建单例实例
const cacheService = new CacheService();

export default cacheService;
