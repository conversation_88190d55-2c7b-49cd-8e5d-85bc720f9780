"""
专业PPT生成器 - 严格按照方案实现
基于成品PPT为模板生成PPT的完整解决方案
"""

import logging
import json
import shutil
import time
from pathlib import Path
from typing import Dict, List, Any, Tuple
from pptx import Presentation
from pptx.util import Pt
from pptx.enum.shapes import MSO_SHAPE_TYPE
import re
import hashlib

logger = logging.getLogger(__name__)


class ProfessionalPPTGenerator:
    """专业PPT生成器 - 完整实现方案要求"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent.parent.parent
        self.templates_dir = self.project_root / "backend" / "templates"
        self.temp_dir = self.project_root / "backend" / "temp_processing"
        self.output_dir = self.project_root / "backend" / "uploads" / "generated"
        
        # 确保目录存在
        self.temp_dir.mkdir(exist_ok=True)
        self.output_dir.mkdir(exist_ok=True)
    
    def generate_ppt_from_template(self, template_id: str, user_request: str,
                                 output_filename: str, test_mode: bool = False) -> Dict[str, Any]:
        """
        完整的PPT生成流程
        
        Args:
            template_id: 模板ID
            user_request: 用户需求
            output_filename: 输出文件名
            
        Returns:
            生成结果
        """
        try:
            logger.info(f"开始专业PPT生成流程: {template_id}")

            # 步骤1: 解析现有PPT内容
            template_path = self.templates_dir / f"{template_id}.pptx"
            if not template_path.exists():
                raise FileNotFoundError(f"模板文件不存在: {template_path}")

            logger.info(f"模板文件路径: {template_path}")
            structure_data = self._parse_ppt_content(str(template_path))
            
            # 步骤2: 替换文字为占位符
            logger.info("开始步骤2: 创建占位符模板")
            placeholder_template_path = self._create_placeholder_template(
                str(template_path), structure_data, template_id
            )
            logger.info(f"占位符模板创建完成: {placeholder_template_path}")

            # 步骤3: 生成内容描述
            logger.info("开始步骤3: 生成内容描述")
            content_requirements = self._generate_content_description(structure_data)
            logger.info(f"内容描述生成完成: {len(content_requirements['slides_requirements'])}页要求")

            # 步骤4: 大模型生成内容
            logger.info("开始步骤4: 大模型生成内容")
            if test_mode:
                logger.info("测试模式：使用后备内容")
                generated_content = self._generate_fallback_content(content_requirements, user_request)
            else:
                generated_content = self._generate_content_with_llm(
                    content_requirements, user_request
                )
            logger.info(f"大模型内容生成完成: {len(generated_content.get('slides', []))}页内容")

            # 步骤5: 填充内容到模板
            logger.info("开始步骤5: 填充内容到模板")
            final_result = self._fill_content_to_template(
                placeholder_template_path, generated_content, structure_data, output_filename
            )
            logger.info("专业PPT生成流程完成")
            
            return final_result
            
        except Exception as e:
            logger.error(f"专业PPT生成失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "engine_type": "professional_ppt_generator"
            }
    
    def _parse_ppt_content(self, template_path: str) -> Dict[str, Any]:
        """
        步骤1: 解析现有PPT内容
        提取每页的所有文字内容，识别类型、位置和字数
        """
        logger.info("步骤1: 解析PPT内容...")
        
        prs = Presentation(template_path)
        structure_data = {
            "template_info": {
                "total_slides": len(prs.slides),
                "slide_width": prs.slide_width,
                "slide_height": prs.slide_height
            },
            "slides": []
        }
        
        for slide_idx, slide in enumerate(prs.slides):
            slide_data = {
                "slide_index": slide_idx,
                "text_elements": []
            }
            
            # 获取所有文字形状
            text_shapes = [shape for shape in slide.shapes 
                          if hasattr(shape, 'text') and shape.text.strip()]
            
            for shape_idx, shape in enumerate(text_shapes):
                text_content = shape.text.strip()
                
                # 生成全局唯一占位符ID
                placeholder_id = f"slide_{slide_idx:02d}_text_{shape_idx:02d}_{int(time.time()*1000) % 10000}"
                
                # 识别文字类型
                text_type = self._identify_text_type(text_content, shape)
                
                # 计算字数
                word_count = len(text_content)
                
                # 提取样式信息
                style_info = self._extract_style_info(shape)
                
                text_element = {
                    "placeholder_id": placeholder_id,
                    "original_text": text_content,
                    "text_type": text_type,
                    "word_count": word_count,
                    "word_limit": word_count,  # 初始限制等于原字数
                    "position": {
                        "left": shape.left,
                        "top": shape.top,
                        "width": shape.width,
                        "height": shape.height
                    },
                    "style_info": style_info,
                    "shape_index": shape_idx
                }
                
                slide_data["text_elements"].append(text_element)
            
            structure_data["slides"].append(slide_data)
        
        logger.info(f"解析完成: {len(prs.slides)}页，{sum(len(s['text_elements']) for s in structure_data['slides'])}个文字元素")
        return structure_data
    
    def _identify_text_type(self, text_content: str, shape) -> str:
        """识别文字类型"""
        text_length = len(text_content)
        
        # 基于长度和内容特征判断
        if text_length <= 15:
            return "title"
        elif text_length <= 30:
            return "subtitle"
        elif '•' in text_content or '\n' in text_content:
            return "list"
        elif text_length > 50:
            return "paragraph"
        else:
            return "content"
    
    def _extract_style_info(self, shape) -> Dict[str, Any]:
        """提取样式信息"""
        style_info = {
            "font_name": None,
            "font_size": None,
            "font_bold": False,
            "font_italic": False,
            "alignment": None
        }
        
        try:
            if hasattr(shape, 'text_frame') and shape.text_frame.paragraphs:
                para = shape.text_frame.paragraphs[0]
                if para.runs:
                    run = para.runs[0]
                    font = run.font
                    
                    style_info.update({
                        "font_name": font.name,
                        "font_size": font.size.pt if font.size else None,
                        "font_bold": font.bold,
                        "font_italic": font.italic,
                        "alignment": str(para.alignment) if para.alignment else None
                    })
        except Exception as e:
            logger.warning(f"提取样式信息失败: {e}")
        
        return style_info
    
    def _create_placeholder_template(self, template_path: str, structure_data: Dict[str, Any],
                                   template_id: str) -> str:
        """
        步骤2: 创建占位符模板（安全方法）
        不修改原模板，而是记录映射关系
        """
        logger.info("步骤2: 创建占位符模板...")

        # 直接复制原模板作为占位符模板
        # 我们不在这里修改文字，而是在最后一步直接替换
        import shutil
        placeholder_template_path = self.temp_dir / f"{template_id}_placeholder.pptx"
        shutil.copy2(template_path, placeholder_template_path)

        logger.info(f"占位符模板创建完成（保持原样）: {placeholder_template_path}")
        return str(placeholder_template_path)
    
    def _generate_content_description(self, structure_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        步骤3: 生成内容描述
        生成每页占位符的内容要求
        """
        logger.info("步骤3: 生成内容描述...")
        
        content_requirements = {
            "total_slides": structure_data["template_info"]["total_slides"],
            "slides_requirements": []
        }
        
        for slide_data in structure_data["slides"]:
            slide_requirements = {
                "slide_index": slide_data["slide_index"],
                "text_requirements": []
            }
            
            for text_element in slide_data["text_elements"]:
                requirement = {
                    "placeholder_id": text_element["placeholder_id"],
                    "text_type": text_element["text_type"],
                    "word_limit": text_element["word_limit"],
                    "word_range": {
                        "min": max(1, text_element["word_limit"] - 5),
                        "max": text_element["word_limit"] + 5
                    },
                    "original_example": text_element["original_text"][:50]
                }
                slide_requirements["text_requirements"].append(requirement)
            
            content_requirements["slides_requirements"].append(slide_requirements)
        
        logger.info(f"内容描述生成完成: {len(content_requirements['slides_requirements'])}页要求")
        return content_requirements
    
    def _generate_content_with_llm(self, content_requirements: Dict[str, Any], 
                                  user_request: str) -> Dict[str, Any]:
        """
        步骤4: 大模型生成内容
        基于内容要求和用户需求生成内容
        """
        logger.info("步骤4: 大模型生成内容...")
        
        # 构建详细提示词
        prompt = self._build_generation_prompt(content_requirements, user_request)
        
        # 调用LLM
        try:
            # 导入LLM客户端
            import sys
            llm_path = Path(__file__).parent.parent.parent / "llm"
            if str(llm_path) not in sys.path:
                sys.path.insert(0, str(llm_path))

            import llm_client_manager
            llm_client = llm_client_manager.LLMClient()

            logger.info("调用大模型生成内容...")
            response = llm_client.call_model(prompt, temperature=0.7, max_tokens=8000)

            # 解析响应
            generated_content = self._parse_llm_response(response, content_requirements)

            logger.info("大模型内容生成完成")
            return generated_content

        except Exception as e:
            logger.warning(f"大模型生成失败，使用后备内容: {e}")
            # 返回示例内容作为后备
            return self._generate_fallback_content(content_requirements, user_request)
    
    def _build_generation_prompt(self, content_requirements: Dict[str, Any],
                               user_request: str) -> str:
        """构建精确的生成提示词"""
        prompt_parts = [
            "你是一个专业的PPT内容生成助手。请根据用户需求和PPT模板结构生成精确的内容。",
            "",
            f"用户需求：{user_request}",
            "",
            f"PPT模板结构：总共{content_requirements['total_slides']}页",
            "每页的文字部分结构如下：",
            ""
        ]

        # 详细描述每页的结构
        for slide_req in content_requirements["slides_requirements"]:
            slide_index = slide_req['slide_index']
            prompt_parts.append(f"=== 第{slide_index + 1}页结构 ===")
            prompt_parts.append(f"该页包含{len(slide_req['text_requirements'])}个文字部分：")

            for i, text_req in enumerate(slide_req["text_requirements"]):
                placeholder_id = text_req['placeholder_id']
                text_type = text_req['text_type']
                word_range = text_req['word_range']
                original_example = text_req.get('original_example', '')

                prompt_parts.append(f"  文字部分{i+1}:")
                prompt_parts.append(f"    - 占位符编码: {placeholder_id}")
                prompt_parts.append(f"    - 内容类型: {text_type}")
                prompt_parts.append(f"    - 字数要求: {word_range['min']}-{word_range['max']}字")
                prompt_parts.append(f"    - 原文示例: \"{original_example[:30]}...\"")
                prompt_parts.append(f"    - 在页面中的作用: {self._get_text_role_description(text_type, i)}")
                prompt_parts.append("")

        prompt_parts.extend([
            "=== 生成要求 ===",
            "1. 严格按照每个占位符的字数要求生成内容",
            "2. 内容必须与用户需求高度相关",
            "3. 每个占位符的内容要符合其类型和作用",
            "4. 保持整个PPT的逻辑连贯性",
            "5. 不要包含任何提示词或说明性文字",
            "",
            "=== 返回格式 ===",
            "请严格按照以下JSON格式返回，每个占位符对应一个具体内容：",
            "{",
            '  "slides": [',
            '    {',
            '      "slide_index": 0,',
            '      "content": {',
            '        "slide_0_text_0": "第一个文字部分的具体内容",',
            '        "slide_0_text_1": "第二个文字部分的具体内容"',
            '      }',
            '    },',
            '    {',
            '      "slide_index": 1,',
            '      "content": {',
            '        "slide_1_text_0": "第二页第一个文字部分的具体内容"',
            '      }',
            '    }',
            '  ]',
            "}",
            "",
            "注意：返回的内容必须是实际的PPT内容，不要包含任何解释性文字。"
        ])

        return "\n".join(prompt_parts)

    def _get_text_role_description(self, text_type: str, position: int) -> str:
        """获取文字部分的作用描述"""
        role_map = {
            "title": "页面主标题，概括本页核心主题",
            "subtitle": "页面副标题，补充说明主题",
            "content": "页面主要内容，详细阐述相关信息",
            "list": "要点列表，条理化展示关键信息",
            "paragraph": "段落文本，深入分析和说明"
        }

        base_role = role_map.get(text_type, "页面文字内容")

        if position == 0:
            return f"{base_role}（页面首要内容）"
        else:
            return f"{base_role}（页面第{position+1}个文字部分）"
    
    def _parse_llm_response(self, response: str, content_requirements: Dict[str, Any]) -> Dict[str, Any]:
        """解析LLM响应并验证内容质量"""
        try:
            # 提取JSON
            json_start = response.find("{")
            json_end = response.rfind("}") + 1

            if json_start != -1 and json_end > json_start:
                json_content = response[json_start:json_end]
                parsed_content = json.loads(json_content)

                # 验证内容质量
                if self._validate_generated_content_quality(parsed_content, content_requirements):
                    logger.info("LLM生成内容质量验证通过")
                    return parsed_content
                else:
                    logger.warning("LLM生成内容质量不符合要求，使用后备内容")
                    return self._generate_fallback_content(content_requirements, "LLM内容质量不达标")
            else:
                raise ValueError("无法找到有效的JSON内容")

        except Exception as e:
            logger.error(f"解析LLM响应失败: {e}")
            return self._generate_fallback_content(content_requirements, "LLM解析失败")

    def _validate_generated_content_quality(self, content: Dict[str, Any],
                                          requirements: Dict[str, Any]) -> bool:
        """验证生成内容的质量"""
        try:
            slides = content.get("slides", [])
            required_slides = requirements.get("slides_requirements", [])

            if len(slides) != len(required_slides):
                logger.warning(f"页数不匹配: 生成{len(slides)}页，要求{len(required_slides)}页")
                return False

            for slide, req_slide in zip(slides, required_slides):
                slide_content = slide.get("content", {})
                required_placeholders = [req["placeholder_id"] for req in req_slide["text_requirements"]]

                # 检查占位符是否完整
                for placeholder_id in required_placeholders:
                    if placeholder_id not in slide_content:
                        logger.warning(f"缺少占位符: {placeholder_id}")
                        return False

                    content_text = slide_content[placeholder_id]

                    # 检查是否包含明显的提示词内容（放宽检查）
                    if any(keyword in content_text for keyword in [
                        "请生成一个", "这里是关于", "补充内容以达到要求长度"
                    ]):
                        logger.warning(f"内容包含提示词: {content_text[:50]}")
                        return False

                    # 检查内容长度是否合理（放宽标准）
                    if len(content_text.strip()) < 1:
                        logger.warning(f"内容为空: {content_text}")
                        return False

            return True

        except Exception as e:
            logger.error(f"验证内容质量失败: {e}")
            return False
    
    def _generate_fallback_content(self, content_requirements: Dict[str, Any], 
                                 user_request: str) -> Dict[str, Any]:
        """生成后备内容"""
        logger.info("生成后备内容...")
        
        fallback_content = {"slides": []}
        
        for slide_req in content_requirements["slides_requirements"]:
            slide_content = {
                "slide_index": slide_req["slide_index"],
                "content": {}
            }
            
            for text_req in slide_req["text_requirements"]:
                placeholder_id = text_req["placeholder_id"]
                text_type = text_req["text_type"]
                word_limit = text_req["word_limit"]
                
                # 根据类型和用户需求生成实际内容
                topic = self._extract_topic_from_request(user_request)

                if text_type == "title":
                    if slide_req['slide_index'] == 0:
                        content = topic
                    else:
                        content = self._generate_section_title(slide_req['slide_index'], topic)
                elif text_type == "subtitle":
                    content = self._generate_subtitle(slide_req['slide_index'], topic)
                elif text_type == "list":
                    content = self._generate_list_content(slide_req['slide_index'], topic)
                else:
                    content = self._generate_paragraph_content(slide_req['slide_index'], topic)
                
                # 调整长度
                if len(content) > word_limit:
                    content = content[:word_limit]
                elif len(content) < word_limit - 5:
                    content += "，补充内容以达到要求长度"
                
                slide_content["content"][placeholder_id] = content
            
            fallback_content["slides"].append(slide_content)
        
        return fallback_content

    def _extract_topic_from_request(self, user_request: str) -> str:
        """从用户请求中提取主题"""
        # 清理用户请求，提取核心主题
        topic = user_request.replace('请生成一个关于', '').replace('的PPT', '')
        topic = topic.replace('请生成', '').replace('PPT', '').strip()

        # 如果主题太短，使用默认主题
        if len(topic) < 3:
            topic = "专业主题演示"

        return topic

    def _generate_section_title(self, slide_index: int, topic: str) -> str:
        """生成章节标题"""
        section_titles = [
            f"{topic}概述",
            f"{topic}背景",
            f"核心技术",
            f"应用场景",
            f"案例分析",
            f"发展趋势",
            f"技术挑战",
            f"解决方案",
            f"实施策略",
            f"效果评估",
            f"未来展望",
            f"总结与思考"
        ]

        if slide_index < len(section_titles):
            return section_titles[slide_index]
        else:
            return f"{topic}要点{slide_index - len(section_titles) + 1}"

    def _generate_subtitle(self, slide_index: int, topic: str) -> str:
        """生成副标题"""
        subtitles = [
            "全面了解核心概念",
            "深入分析发展历程",
            "探索关键技术原理",
            "发现实际应用价值",
            "学习成功实践经验",
            "把握未来发展方向",
            "识别潜在技术难点",
            "制定有效应对策略",
            "规划具体实施路径",
            "衡量预期实施效果",
            "展望长远发展前景",
            "总结关键成功要素"
        ]

        if slide_index < len(subtitles):
            return subtitles[slide_index]
        else:
            return "深入分析与详细说明"

    def _generate_list_content(self, slide_index: int, topic: str) -> str:
        """生成列表内容"""
        list_contents = [
            f"• {topic}的核心定义\n• 主要技术特点\n• 发展历程概览",
            f"• 技术发展背景\n• 市场需求驱动\n• 政策环境支持",
            f"• 核心算法原理\n• 关键技术架构\n• 创新技术突破",
            f"• 行业应用领域\n• 具体使用场景\n• 应用价值体现",
            f"• 典型成功案例\n• 实施过程分析\n• 效果评估结果",
            f"• 技术发展趋势\n• 市场前景预测\n• 创新方向展望",
            f"• 技术实现难点\n• 应用推广障碍\n• 标准化挑战",
            f"• 技术解决路径\n• 实施策略建议\n• 风险控制措施",
            f"• 分阶段实施计划\n• 资源配置方案\n• 时间节点安排",
            f"• 效果评估指标\n• 成功标准定义\n• 持续改进机制",
            f"• 长期发展规划\n• 技术演进路线\n• 战略布局建议",
            f"• 关键成功因素\n• 经验教训总结\n• 后续行动建议"
        ]

        if slide_index < len(list_contents):
            return list_contents[slide_index]
        else:
            return f"• {topic}相关要点\n• 重要技术特征\n• 实际应用价值"

    def _generate_paragraph_content(self, slide_index: int, topic: str) -> str:
        """生成段落内容"""
        paragraph_contents = [
            f"{topic}是当前技术发展的重要方向，具有广阔的应用前景和巨大的发展潜力。",
            f"随着技术的不断进步，{topic}在各个领域都展现出了强大的应用价值。",
            f"核心技术的突破为{topic}的广泛应用奠定了坚实的基础。",
            f"在实际应用中，{topic}已经在多个场景中取得了显著的成效。",
            f"通过深入分析成功案例，我们可以更好地理解{topic}的实际价值。",
            f"未来{topic}的发展将朝着更加智能化、高效化的方向发展。",
            f"尽管{topic}发展迅速，但仍然面临着一些技术和应用方面的挑战。",
            f"针对现有挑战，业界提出了多种有效的解决方案和应对策略。",
            f"成功实施{topic}需要制定详细的规划和科学的实施策略。",
            f"通过建立完善的评估体系，可以有效衡量{topic}的实施效果。",
            f"展望未来，{topic}将在更多领域发挥重要作用，创造更大价值。",
            f"总的来说，{topic}代表了技术发展的重要趋势，值得深入研究和推广应用。"
        ]

        if slide_index < len(paragraph_contents):
            return paragraph_contents[slide_index]
        else:
            return f"{topic}在该领域具有重要的理论意义和实践价值，值得进一步深入研究。"

    def _fill_content_to_template(self, placeholder_template_path: str,
                                generated_content: Dict[str, Any],
                                structure_data: Dict[str, Any],
                                output_filename: str) -> Dict[str, Any]:
        """
        步骤5: 填充内容到模板
        将生成的内容插入对应占位符，保持原样式
        """
        logger.info("步骤5: 填充内容到模板...")

        try:
            # 加载占位符模板
            prs = Presentation(placeholder_template_path)

            # 逐页填充内容
            for slide_idx, slide in enumerate(prs.slides):
                if slide_idx < len(generated_content["slides"]):
                    slide_content = generated_content["slides"][slide_idx]["content"]
                    slide_structure = structure_data["slides"][slide_idx]

                    self._fill_slide_content(slide, slide_content, slide_structure)

            # 保存最终PPT
            output_path = self.output_dir / output_filename
            prs.save(str(output_path))

            # 验证生成结果
            validation_result = self._validate_generated_ppt(str(output_path), structure_data)

            result = {
                "success": True,
                "file_path": str(output_path),
                "output_path": str(output_path),
                "slides_count": len(generated_content["slides"]),
                "file_size": output_path.stat().st_size if output_path.exists() else 0,
                "engine_type": "professional_ppt_generator",
                "validation": validation_result
            }

            logger.info(f"PPT生成成功: {output_path}")
            logger.info(f"验证结果: {validation_result}")
            return result

        except Exception as e:
            logger.error(f"填充内容失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "engine_type": "professional_ppt_generator"
            }

    def _fill_slide_content(self, slide, slide_content: Dict[str, str],
                           slide_structure: Dict[str, Any]):
        """安全地填充单张幻灯片内容"""
        try:
            # 获取所有文字形状
            text_shapes = []
            for shape in slide.shapes:
                try:
                    if hasattr(shape, 'text') and shape.text is not None:
                        text_shapes.append(shape)
                except Exception as e:
                    logger.debug(f"跳过问题形状: {e}")
                    continue

            logger.debug(f"找到 {len(text_shapes)} 个文字形状")

            # 按结构顺序填充内容
            for shape_idx, shape in enumerate(text_shapes):
                try:
                    if shape_idx < len(slide_structure["text_elements"]):
                        text_element = slide_structure["text_elements"][shape_idx]
                        placeholder_id = text_element["placeholder_id"]

                        if placeholder_id in slide_content:
                            new_content = slide_content[placeholder_id]

                            # 安全地替换内容
                            self._replace_text_keep_style(shape, new_content, text_element["style_info"])

                            logger.debug(f"填充内容: {placeholder_id} -> {new_content[:30]}...")
                        else:
                            logger.debug(f"未找到内容: {placeholder_id}")
                    else:
                        logger.debug(f"形状索引超出范围: {shape_idx}")

                except Exception as shape_error:
                    logger.warning(f"处理形状 {shape_idx} 失败: {shape_error}")
                    continue

        except Exception as e:
            logger.error(f"填充幻灯片内容失败: {e}")
            # 不抛出异常，继续处理其他幻灯片

    def _replace_text_keep_style(self, shape, new_content: str, style_info: Dict[str, Any]):
        """最安全的文字替换方法"""
        try:
            # 方法1：直接替换文字（最安全）
            original_text = shape.text
            shape.text = new_content

            # 尝试恢复样式（如果失败不影响主要功能）
            try:
                if hasattr(shape, 'text_frame') and shape.text_frame.paragraphs:
                    for para in shape.text_frame.paragraphs:
                        if para.runs:
                            for run in para.runs:
                                font = run.font

                                # 只恢复基本样式，避免复杂操作
                                if style_info.get("font_size") and isinstance(style_info["font_size"], (int, float)):
                                    try:
                                        font.size = Pt(style_info["font_size"])
                                    except:
                                        pass

                                if style_info.get("font_bold") is not None:
                                    try:
                                        font.bold = style_info["font_bold"]
                                    except:
                                        pass
            except Exception as style_error:
                logger.debug(f"样式恢复失败（不影响主要功能）: {style_error}")

            logger.debug(f"文字替换成功: {original_text[:30]} -> {new_content[:30]}")

        except Exception as e:
            logger.error(f"文字替换失败: {e}")
            # 如果替换失败，至少记录错误，但不中断流程
            pass

    def _validate_generated_ppt(self, output_path: str, structure_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证生成的PPT是否符合要求"""
        try:
            from pptx import Presentation

            prs = Presentation(output_path)
            validation_result = {
                "total_slides": len(prs.slides),
                "expected_slides": structure_data["template_info"]["total_slides"],
                "slides_match": len(prs.slides) == structure_data["template_info"]["total_slides"],
                "text_replacement_check": [],
                "has_original_text": False,
                "has_placeholder_text": False
            }

            # 检查前3页的文字替换情况
            for i, slide in enumerate(prs.slides[:3]):
                slide_check = {
                    "slide_index": i,
                    "text_shapes_count": 0,
                    "texts": []
                }

                try:
                    # 安全地获取文字形状
                    text_shapes = []
                    for shape in slide.shapes:
                        try:
                            if hasattr(shape, 'text') and shape.text and shape.text.strip():
                                text_shapes.append(shape)
                        except Exception as shape_error:
                            logger.debug(f"跳过问题形状: {shape_error}")
                            continue

                    slide_check["text_shapes_count"] = len(text_shapes)

                    for shape in text_shapes[:3]:  # 只检查前3个文字形状
                        try:
                            text_content = shape.text.strip()
                            slide_check["texts"].append(text_content[:100])

                            # 检查是否还有占位符
                            if "{{" in text_content and "}}" in text_content:
                                validation_result["has_placeholder_text"] = True

                            # 检查是否有原始文字（简单检测）
                            if any(keyword in text_content for keyword in ["主讲人", "AiPPT", "PowerPoint design"]):
                                validation_result["has_original_text"] = True
                        except Exception as text_error:
                            logger.debug(f"跳过问题文字: {text_error}")
                            continue

                except Exception as slide_error:
                    logger.debug(f"跳过问题幻灯片: {slide_error}")
                    continue

                validation_result["text_replacement_check"].append(slide_check)

            return validation_result

        except Exception as e:
            logger.error(f"验证PPT失败: {e}")
            return {"error": str(e), "validation_skipped": True}


# 创建全局实例
professional_ppt_generator = ProfessionalPPTGenerator()
