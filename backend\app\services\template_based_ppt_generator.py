#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
基于成品PPT模板的PPT生成器
按照新方案的5步流程实现
"""

import json
import re
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from pptx import Presentation
from pptx.util import Pt

logger = logging.getLogger(__name__)

class TemplatePPTGenerator:
    """基于成品PPT模板的PPT生成器"""

    def __init__(self):
        self.templates_dir = Path("templates")  # 修正模板目录路径
        self.generated_dir = Path("uploads/generated")
        self.temp_dir = Path("temp")

        # 确保目录存在
        for dir_path in [self.templates_dir, self.generated_dir, self.temp_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)

        # 当前PPT的占位符计数器（每次重启从1开始）
        self.placeholder_counter = 0
    
    def step1_parse_template_ppt(self, template_path: str) -> Dict[str, Any]:
        """
        步骤1: 成品PPT数据解析

        Args:
            template_path: PPT模板文件路径

        Returns:
            Dict: 结构化描述每页的文字（JSON格式）
        """
        logger.info(f"步骤1: 开始解析PPT模板 - {template_path}")

        try:
            # 重置计数器
            self.placeholder_counter = 0

            prs = Presentation(template_path)
            template_structure = {}

            for page_idx, slide in enumerate(prs.slides, 1):
                page_key = f"page_{page_idx}"
                page_data = {}

                # 获取所有文字形状
                text_shapes = []
                for shape in slide.shapes:
                    if hasattr(shape, 'text') and shape.text and shape.text.strip():
                        text_shapes.append(shape)

                logger.debug(f"第{page_idx}页找到{len(text_shapes)}个文字形状")

                # 按位置和大小分析文字类型
                for shape_idx, shape in enumerate(text_shapes):
                    text_content = shape.text.strip()
                    word_count = self._count_chinese_chars(text_content)

                    # 分析文字类型
                    text_type = self._analyze_text_type(shape, shape_idx, len(text_shapes), slide)

                    # 生成唯一占位符ID
                    self.placeholder_counter += 1
                    placeholder_id = f"PLACEHOLDER_{self.placeholder_counter:03d}"

                    # 构建文字元素数据
                    element_key = f"{text_type}_{shape_idx + 1}" if shape_idx > 0 or text_type != "title" else text_type
                    page_data[element_key] = {
                        "type": text_type,
                        "content": text_content,
                        "word_limit": max(word_count, 5),  # 最少5个字
                        "PlaceholderId": placeholder_id
                    }

                    logger.debug(f"  {element_key}: {text_type}, {word_count}字, {placeholder_id}")

                template_structure[page_key] = page_data

            logger.info(f"步骤1完成: 解析了{len(template_structure)}页内容，生成{self.placeholder_counter}个占位符")
            return {
                "success": True,
                "template_structure": template_structure,
                "total_pages": len(template_structure),
                "total_placeholders": self.placeholder_counter,
                "template_path": template_path
            }

        except Exception as e:
            logger.error(f"步骤1失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return {
                "success": False,
                "error": str(e),
                "error_detail": traceback.format_exc()
            }
    
    def _count_chinese_chars(self, text: str) -> int:
        """计算中文字数"""
        # 移除空白字符
        text = re.sub(r'\s+', '', text)
        # 计算中文字符数
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        # 计算英文单词数
        english_words = len(re.findall(r'[a-zA-Z]+', text))
        # 计算数字
        numbers = len(re.findall(r'\d+', text))

        # 总字数 = 中文字符 + 英文单词 + 数字
        total = chinese_chars + english_words + numbers
        return max(total, 1)  # 至少1个字

    def _analyze_text_type(self, shape, shape_index: int, total_shapes: int, slide) -> str:
        """
        分析文字类型（供参考，大模型可重新定义）
        """
        try:
            # 获取字体大小
            font_size = self._get_font_size(shape)

            # 获取位置信息（使用简化方法）
            slide_height = 7200000  # 使用标准幻灯片高度（10英寸）
            top_ratio = shape.top / slide_height if slide_height > 0 else 0

            text_content = shape.text.strip()

            # 判断规则
            if shape_index == 0 and font_size >= 28:
                return "title"  # 第一个且大字体 = 标题
            elif shape_index <= 1 and font_size >= 20:
                return "subtitle"  # 前两个且中等字体 = 副标题
            elif "•" in text_content or "·" in text_content or text_content.count('\n') >= 2:
                return "list"  # 包含项目符号或多行 = 列表
            elif font_size <= 12 and top_ratio > 0.8:
                return "footer"  # 小字体且在底部 = 页脚
            elif len(text_content) <= 20 and font_size >= 16:
                return "subtitle"  # 短文本且中等字体 = 副标题
            else:
                return "text"  # 其他 = 正文

        except Exception as e:
            logger.debug(f"文字类型分析失败，使用默认规则: {e}")
            # 默认分类
            if shape_index == 0:
                return "title"
            elif shape_index == 1:
                return "subtitle"
            else:
                return "text"

    def _get_font_size(self, shape) -> float:
        """获取字体大小"""
        try:
            if hasattr(shape, 'text_frame') and shape.text_frame.paragraphs:
                para = shape.text_frame.paragraphs[0]
                if para.runs:
                    run = para.runs[0]
                    if run.font.size:
                        return run.font.size.pt
            return 14.0  # 默认字体大小
        except Exception:
            return 14.0
    
    def step2_create_template_file(self, template_path: str, template_structure: Dict[str, Any]) -> str:
        """
        步骤2: 生成成品PPT对应的模板文件
        
        Args:
            template_path: 原PPT文件路径
            template_structure: 步骤1解析的结构化数据
            
        Returns:
            str: 带占位符的模板PPT文件路径
        """
        logger.info("步骤2: 开始生成模板文件")
        
        try:
            # 复制原PPT
            prs = Presentation(template_path)
            
            # 创建占位符映射
            placeholder_mapping = {}
            
            for page_idx, slide in enumerate(prs.slides, 1):
                page_key = f"page_{page_idx}"
                if page_key not in template_structure:
                    continue
                
                page_data = template_structure[page_key]
                
                # 获取文字形状
                text_shapes = []
                for shape in slide.shapes:
                    if hasattr(shape, 'text') and shape.text.strip():
                        text_shapes.append(shape)
                
                # 替换文字为占位符
                element_idx = 0
                for element_key, element_data in page_data.items():
                    if element_idx < len(text_shapes):
                        shape = text_shapes[element_idx]
                        placeholder_id = element_data["PlaceholderId"]
                        
                        # 保存原始文字和样式信息
                        placeholder_mapping[placeholder_id] = {
                            "original_text": shape.text,
                            "page": page_idx,
                            "element": element_key
                        }
                        
                        # 替换为占位符
                        shape.text = f"{{{{{placeholder_id}}}}}"
                        element_idx += 1
            
            # 保存模板文件
            template_filename = f"template_{Path(template_path).stem}_placeholder.pptx"
            template_file_path = self.temp_dir / template_filename
            prs.save(str(template_file_path))
            
            logger.info(f"步骤2完成: 模板文件已保存 - {template_file_path}")
            return str(template_file_path)
            
        except Exception as e:
            logger.error(f"步骤2失败: {e}")
            raise
    
    def step3_generate_prompt(self, user_context: str, template_structure: Dict[str, Any]) -> str:
        """
        步骤3: 动态生成提示词（使用模板管理器的统一方法）

        Args:
            user_context: 用户对话上下文（最新3000字）
            template_structure: 模板结构化数据

        Returns:
            str: 生成的提示词
        """
        logger.info("步骤3: 开始生成提示词")

        # 使用模板管理器的动态提示词生成方法
        from services.template_manager import template_manager

        # 获取模板信息
        template_info = {
            "name": "基于数据组件的文书编辑工具2",  # 这里可以从模板详情中获取
            "total_pages": len(template_structure),
            "total_placeholders": sum(len(page_data) for page_data in template_structure.values())
        }

        # 调用模板管理器的动态提示词生成
        prompt_result = template_manager._generate_dynamic_prompt(
            self.template_id,
            template_info,
            user_context
        )

        if prompt_result.get("success"):
            prompt = prompt_result["prompt"]
            logger.info("步骤3完成: 提示词生成完成")
            return prompt
        else:
            # 如果失败，使用备用方法
            logger.warning(f"模板管理器提示词生成失败: {prompt_result.get('error')}，使用备用方法")

            # 限制用户上下文长度
            if len(user_context) > 3000:
                user_context = user_context[-3000:]

            # 构建备用提示词
            prompt = f"""# 任务
你是一个ppt编写专家，下面就请结合用户问答过程中对于ppt的生成需求和最终需要生成的数据格式要求生成ppt内容。

# 用户的问答信息是
{user_context}

# 输出的数据结构是
{json.dumps(template_structure, ensure_ascii=False, indent=2)}

# 生成要求
请严格按照输出的数据结构的每页的文字结构生成内容，生成的页数内容要保持一致，每页需要生成的文字类型和文字数量一致，生成内容字数与模板相差不超过±5个字，type和PlaceholderId需要保持与原本内容一致，content就是新生成的内容。
暂不考虑支持用户指定页数，完全根据ppt模板的页数生成。
数据结构中的文字类型"type"是基于粗略的规则判断，只作为参考，你可以根据当前页面的内容重新定义。

请为所有{len(template_structure)}页生成完整内容，确保每个占位符都有合适的内容。"""

            logger.info("步骤3完成: 备用提示词生成完成")
            return prompt

    def step4_generate_content_with_llm(self, prompt: str) -> Dict[str, Any]:
        """
        步骤4: 大模型生成内容

        Args:
            prompt: 动态生成的提示词

        Returns:
            Dict: 大模型生成的内容
        """
        logger.info("步骤4: 开始调用大模型生成内容")

        try:
            # 这里需要集成现有的大模型调用服务
            import sys
            from pathlib import Path
            sys.path.append(str(Path(__file__).parent.parent.parent))
            from llm.connection_manager import ConnectionManager
            from llm.config import MODEL_CONFIG

            # 创建连接管理器实例
            connection_manager = ConnectionManager(MODEL_CONFIG)

            # 调用大模型
            response = connection_manager.call_model_with_retry([{
                "role": "user",
                "content": prompt
            }])

            # 解析响应
            content = response.get("content", "")

            # 尝试解析JSON
            try:
                # 提取JSON内容
                json_start = content.find("{")
                json_end = content.rfind("}") + 1

                if json_start != -1 and json_end > json_start:
                    json_content = content[json_start:json_end]
                    generated_content = json.loads(json_content)

                    logger.info("步骤4完成: 大模型内容生成成功")
                    return {
                        "success": True,
                        "generated_content": generated_content,
                        "raw_response": content
                    }
                else:
                    raise ValueError("响应中未找到有效的JSON内容")

            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败: {e}")
                return {
                    "success": False,
                    "error": f"大模型响应格式错误: {e}",
                    "raw_response": content
                }

        except Exception as e:
            logger.error(f"步骤4失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return {
                "success": False,
                "error": str(e),
                "error_detail": traceback.format_exc()
            }

    def step5_fill_content_to_template(self, template_file_path: str, generated_content: Dict[str, Any],
                                     output_filename: str) -> Dict[str, Any]:
        """
        步骤5: 填充内容到模板

        Args:
            template_file_path: 带占位符的模板文件路径
            generated_content: 大模型生成的内容
            output_filename: 输出文件名

        Returns:
            Dict: 生成结果
        """
        logger.info("步骤5: 开始填充内容到模板")

        try:
            # 加载模板PPT
            prs = Presentation(template_file_path)

            # 遍历每页进行内容替换
            for page_idx, slide in enumerate(prs.slides, 1):
                page_key = f"page_{page_idx}"

                if page_key not in generated_content:
                    logger.warning(f"生成内容中缺少{page_key}")
                    continue

                page_content = generated_content[page_key]

                # 获取所有文字形状
                text_shapes = []
                for shape in slide.shapes:
                    if hasattr(shape, 'text') and shape.text and shape.text.strip():
                        text_shapes.append(shape)

                # 替换占位符为实际内容
                for shape in text_shapes:
                    shape_text = shape.text.strip()

                    # 检查是否包含占位符
                    if shape_text.startswith("{{") and shape_text.endswith("}}"):
                        placeholder_id = shape_text[2:-2]  # 移除{{}}

                        # 在生成内容中查找对应的内容
                        replacement_content = self._find_content_by_placeholder(page_content, placeholder_id)

                        if replacement_content:
                            # 替换内容，保持样式
                            shape.text = replacement_content
                            logger.debug(f"替换占位符 {placeholder_id} -> {replacement_content[:30]}...")
                        else:
                            logger.warning(f"未找到占位符 {placeholder_id} 对应的内容")

            # 保存生成的PPT
            output_path = self.generated_dir / output_filename
            prs.save(str(output_path))

            # 获取文件大小
            file_size = output_path.stat().st_size

            logger.info(f"步骤5完成: PPT已生成 - {output_path}")
            return {
                "success": True,
                "file_path": str(output_path),
                "file_size": file_size,
                "filename": output_filename
            }

        except Exception as e:
            logger.error(f"步骤5失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return {
                "success": False,
                "error": str(e),
                "error_detail": traceback.format_exc()
            }

    def _find_content_by_placeholder(self, page_content: Dict[str, Any], placeholder_id: str) -> Optional[str]:
        """根据占位符ID查找对应的内容"""
        for element_key, element_data in page_content.items():
            if isinstance(element_data, dict) and element_data.get("PlaceholderId") == placeholder_id:
                return element_data.get("content", "")
        return None

    def generate_ppt_from_template(self, template_id: str, user_context: str, output_filename: str) -> Dict[str, Any]:
        """
        完整的5步流程：基于成品PPT模板生成新PPT

        Args:
            template_id: 模板ID
            user_context: 用户对话上下文
            output_filename: 输出文件名

        Returns:
            Dict: 生成结果
        """
        logger.info(f"开始基于模板生成PPT: {template_id}")

        try:
            # 获取模板文件路径
            template_path = self._get_template_path(template_id)
            if not template_path:
                return {
                    "success": False,
                    "error": f"模板文件不存在: {template_id}"
                }

            # 步骤1: 解析模板PPT
            logger.info("执行步骤1: 解析模板PPT")
            parse_result = self.step1_parse_template_ppt(template_path)
            if not parse_result["success"]:
                return parse_result

            template_structure = parse_result["template_structure"]

            # 步骤2: 创建模板文件
            logger.info("执行步骤2: 创建模板文件")
            template_file_path = self.step2_create_template_file(template_path, template_structure)

            # 步骤3: 生成提示词
            logger.info("执行步骤3: 生成提示词")
            prompt = self.step3_generate_prompt(user_context, template_structure)

            # 步骤4: 大模型生成内容
            logger.info("执行步骤4: 大模型生成内容")
            llm_result = self.step4_generate_content_with_llm(prompt)
            if not llm_result["success"]:
                return llm_result

            generated_content = llm_result["generated_content"]

            # 步骤5: 填充内容到模板
            logger.info("执行步骤5: 填充内容到模板")
            final_result = self.step5_fill_content_to_template(
                template_file_path, generated_content, output_filename
            )

            if final_result["success"]:
                # 添加额外信息
                final_result.update({
                    "template_id": template_id,
                    "total_pages": parse_result["total_pages"],
                    "total_placeholders": parse_result["total_placeholders"],
                    "template_structure": template_structure,
                    "generated_content": generated_content
                })

            return final_result

        except Exception as e:
            logger.error(f"PPT生成失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return {
                "success": False,
                "error": str(e),
                "error_detail": traceback.format_exc()
            }

    def _get_template_path(self, template_id: str) -> Optional[str]:
        """获取模板文件路径"""
        # 查找模板文件
        for ext in ['.pptx', '.ppt']:
            template_path = self.templates_dir / f"{template_id}{ext}"
            if template_path.exists():
                return str(template_path)
        return None

    def reparse_template(self, template_id: str) -> Dict[str, Any]:
        """重新解析模板（用于"重新解析模板"功能）"""
        logger.info(f"重新解析模板: {template_id}")

        template_path = self._get_template_path(template_id)
        if not template_path:
            return {
                "success": False,
                "error": f"模板文件不存在: {template_id}"
            }

        return self.step1_parse_template_ppt(template_path)


# 创建全局实例
template_ppt_generator = TemplatePPTGenerator()
