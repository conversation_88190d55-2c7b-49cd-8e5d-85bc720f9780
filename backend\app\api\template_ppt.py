#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
基于成品PPT模板的API路由
按照新方案实现
"""

import logging
from fastapi import APIRouter, HTTPException, UploadFile, File
from pydantic import BaseModel
from typing import Dict, Any, Optional

from ..services.template_based_ppt_generator import template_ppt_generator

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/template-ppt", tags=["template-ppt"])

class GenerateRequest(BaseModel):
    template_id: str
    user_context: str
    output_filename: Optional[str] = None

class ReparseRequest(BaseModel):
    template_id: str

class ContentConfirmRequest(BaseModel):
    template_id: str
    confirmed_content: Dict[str, Any]
    output_filename: Optional[str] = None

@router.post("/parse-template")
async def parse_template(request: ReparseRequest):
    """
    解析或重新解析PPT模板
    对应方案中的步骤1
    """
    try:
        logger.info(f"收到模板解析请求: {request.template_id}")
        
        result = template_ppt_generator.reparse_template(request.template_id)
        
        if result["success"]:
            return {
                "success": True,
                "message": "模板解析成功",
                "data": {
                    "template_structure": result["template_structure"],
                    "total_pages": result["total_pages"],
                    "total_placeholders": result["total_placeholders"]
                }
            }
        else:
            return {
                "success": False,
                "message": "模板解析失败",
                "error": result["error"]
            }
            
    except Exception as e:
        logger.error(f"模板解析API失败: {e}")
        raise HTTPException(status_code=500, detail=f"模板解析失败: {str(e)}")

@router.post("/generate-content")
async def generate_content(request: GenerateRequest):
    """
    生成PPT内容（不直接生成文件）
    对应方案中的步骤1-4，返回内容供用户确认
    """
    try:
        logger.info(f"收到内容生成请求: {request.template_id}")
        
        # 获取模板路径
        template_path = template_ppt_generator._get_template_path(request.template_id)
        if not template_path:
            raise HTTPException(status_code=404, detail=f"模板文件不存在: {request.template_id}")
        
        # 步骤1: 解析模板
        parse_result = template_ppt_generator.step1_parse_template_ppt(template_path)
        if not parse_result["success"]:
            return {
                "success": False,
                "message": "模板解析失败",
                "error": parse_result["error"]
            }
        
        template_structure = parse_result["template_structure"]
        
        # 步骤2: 创建模板文件
        template_file_path = template_ppt_generator.step2_create_template_file(template_path, template_structure)
        
        # 步骤3: 生成提示词
        prompt = template_ppt_generator.step3_generate_prompt(request.user_context, template_structure)
        
        # 步骤4: 大模型生成内容
        llm_result = template_ppt_generator.step4_generate_content_with_llm(prompt)
        if not llm_result["success"]:
            return {
                "success": False,
                "message": "内容生成失败",
                "error": llm_result["error"]
            }
        
        generated_content = llm_result["generated_content"]
        
        # 转换为前端友好的格式
        formatted_content = _format_content_for_frontend(generated_content)
        
        return {
            "success": True,
            "message": "内容生成成功",
            "data": {
                "template_structure": template_structure,
                "generated_content": generated_content,
                "formatted_content": formatted_content,
                "template_file_path": template_file_path,
                "total_pages": parse_result["total_pages"]
            }
        }
        
    except Exception as e:
        logger.error(f"内容生成API失败: {e}")
        raise HTTPException(status_code=500, detail=f"内容生成失败: {str(e)}")

@router.post("/confirm-and-generate")
async def confirm_and_generate(request: ContentConfirmRequest):
    """
    确认内容并生成最终PPT文件
    对应方案中的步骤5
    """
    try:
        logger.info(f"收到确认生成请求: {request.template_id}")
        
        # 获取模板路径和结构
        template_path = template_ppt_generator._get_template_path(request.template_id)
        if not template_path:
            raise HTTPException(status_code=404, detail=f"模板文件不存在: {request.template_id}")
        
        # 重新解析模板以获取模板文件路径
        parse_result = template_ppt_generator.step1_parse_template_ppt(template_path)
        if not parse_result["success"]:
            return {
                "success": False,
                "message": "模板解析失败",
                "error": parse_result["error"]
            }
        
        template_structure = parse_result["template_structure"]
        template_file_path = template_ppt_generator.step2_create_template_file(template_path, template_structure)
        
        # 生成输出文件名
        output_filename = request.output_filename or f"generated_{request.template_id}.pptx"
        
        # 步骤5: 填充内容到模板
        result = template_ppt_generator.step5_fill_content_to_template(
            template_file_path, request.confirmed_content, output_filename
        )
        
        if result["success"]:
            return {
                "success": True,
                "message": "PPT生成成功",
                "data": {
                    "file_path": result["file_path"],
                    "file_size": result["file_size"],
                    "filename": result["filename"],
                    "download_url": f"/files/generated/{result['filename']}"
                }
            }
        else:
            return {
                "success": False,
                "message": "PPT生成失败",
                "error": result["error"]
            }
            
    except Exception as e:
        logger.error(f"确认生成API失败: {e}")
        raise HTTPException(status_code=500, detail=f"PPT生成失败: {str(e)}")

def _format_content_for_frontend(generated_content: Dict[str, Any]) -> Dict[str, Any]:
    """
    将生成的内容格式化为前端友好的格式
    按页展示，每个文字元素独立可编辑
    """
    formatted = {}
    
    for page_key, page_content in generated_content.items():
        page_num = page_key.replace("page_", "")
        formatted[page_key] = {
            "page_number": int(page_num),
            "elements": []
        }
        
        for element_key, element_data in page_content.items():
            if isinstance(element_data, dict):
                formatted[page_key]["elements"].append({
                    "element_key": element_key,
                    "type": element_data.get("type", "text"),
                    "content": element_data.get("content", ""),
                    "word_limit": element_data.get("word_limit", 50),
                    "placeholder_id": element_data.get("PlaceholderId", ""),
                    "display_name": _get_display_name(element_data.get("type", "text"))
                })
    
    return formatted

def _get_display_name(text_type: str) -> str:
    """获取文字类型的显示名称"""
    type_names = {
        "title": "标题",
        "subtitle": "副标题", 
        "text": "正文",
        "list": "列表",
        "footer": "页脚"
    }
    return type_names.get(text_type, "文本")
