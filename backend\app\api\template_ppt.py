#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
基于成品PPT模板的API路由
按照新方案实现
"""

import logging
from fastapi import APIRouter, HTTPException, UploadFile, File
from pydantic import BaseModel
from typing import Dict, Any, Optional

from ..services.professional_ppt_generator import ProfessionalPPTGenerator

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/template-ppt", tags=["template-ppt"])

class GenerateRequest(BaseModel):
    template_id: str
    user_context: str
    output_filename: Optional[str] = None

class ReparseRequest(BaseModel):
    template_id: str

class ContentConfirmRequest(BaseModel):
    template_id: str
    confirmed_content: Dict[str, Any]
    output_filename: Optional[str] = None

@router.post("/parse-template")
async def parse_template(request: ReparseRequest):
    """
    解析或重新解析PPT模板
    使用专业PPT生成器进行模板分析
    """
    try:
        logger.info(f"收到模板解析请求: {request.template_id}")

        # 使用专业PPT生成器进行模板分析
        generator = ProfessionalPPTGenerator()

        # 构建模板路径
        template_path = generator.templates_dir / f"{request.template_id}.pptx"
        if not template_path.exists():
            raise HTTPException(status_code=404, detail=f"模板文件不存在: {request.template_id}")

        # 解析模板内容
        structure_data = generator._parse_ppt_content(str(template_path))

        return {
            "success": True,
            "message": "模板解析成功",
            "data": {
                "template_structure": structure_data,
                "total_pages": structure_data["template_info"]["total_slides"],
                "total_placeholders": sum(len(slide["text_elements"]) for slide in structure_data["slides"])
            }
        }

    except Exception as e:
        logger.error(f"模板解析API失败: {e}")
        raise HTTPException(status_code=500, detail=f"模板解析失败: {str(e)}")

@router.post("/generate-content")
async def generate_content(request: GenerateRequest):
    """
    生成PPT内容（不直接生成文件）
    使用专业PPT生成器进行内容生成
    """
    try:
        logger.info(f"收到内容生成请求: {request.template_id}")

        # 使用专业PPT生成器
        generator = ProfessionalPPTGenerator()

        # 构建模板路径
        template_path = generator.templates_dir / f"{request.template_id}.pptx"
        if not template_path.exists():
            raise HTTPException(status_code=404, detail=f"模板文件不存在: {request.template_id}")

        # 步骤1-3: 解析模板并生成内容描述
        structure_data = generator._parse_ppt_content(str(template_path))
        content_requirements = generator._generate_content_description(structure_data)

        # 步骤4: 大模型生成内容
        generated_content = generator._generate_content_with_llm(
            content_requirements, request.user_context
        )

        # 转换为前端友好的格式
        formatted_content = _format_content_for_frontend(generated_content)

        return {
            "success": True,
            "message": "内容生成成功",
            "data": {
                "template_structure": structure_data,
                "generated_content": generated_content,
                "formatted_content": formatted_content,
                "total_pages": structure_data["template_info"]["total_slides"]
            }
        }
        
    except Exception as e:
        logger.error(f"内容生成API失败: {e}")
        raise HTTPException(status_code=500, detail=f"内容生成失败: {str(e)}")

@router.post("/confirm-and-generate")
async def confirm_and_generate(request: ContentConfirmRequest):
    """
    确认内容并生成最终PPT文件
    使用专业PPT生成器完成最终生成
    """
    try:
        logger.info(f"收到确认生成请求: {request.template_id}")

        # 使用专业PPT生成器
        generator = ProfessionalPPTGenerator()

        # 生成输出文件名
        output_filename = request.output_filename or f"generated_{request.template_id}.pptx"

        # 直接使用专业PPT生成器的完整流程
        # 将确认的内容转换为用户请求格式
        user_request = _convert_confirmed_content_to_request(request.confirmed_content)

        result = generator.generate_ppt_from_template(
            template_id=request.template_id,
            user_request=user_request,
            output_filename=output_filename
        )
        
        if result.get("success"):
            return {
                "success": True,
                "message": "PPT生成成功",
                "data": {
                    "file_path": result.get("file_path", ""),
                    "file_size": result.get("file_size", 0),
                    "output_path": result.get("output_path", ""),
                    "slides_count": result.get("slides_count", 0)
                }
            }
        else:
            return {
                "success": False,
                "message": "PPT生成失败",
                "error": result.get("error", "未知错误")
            }
            
    except Exception as e:
        logger.error(f"确认生成API失败: {e}")
        raise HTTPException(status_code=500, detail=f"PPT生成失败: {str(e)}")

def _format_content_for_frontend(generated_content: Dict[str, Any]) -> Dict[str, Any]:
    """
    将生成的内容格式化为前端友好的格式
    按页展示，每个文字元素独立可编辑
    """
    formatted = {}
    
    for page_key, page_content in generated_content.items():
        page_num = page_key.replace("page_", "")
        formatted[page_key] = {
            "page_number": int(page_num),
            "elements": []
        }
        
        for element_key, element_data in page_content.items():
            if isinstance(element_data, dict):
                formatted[page_key]["elements"].append({
                    "element_key": element_key,
                    "type": element_data.get("type", "text"),
                    "content": element_data.get("content", ""),
                    "word_limit": element_data.get("word_limit", 50),
                    "placeholder_id": element_data.get("PlaceholderId", ""),
                    "display_name": _get_display_name(element_data.get("type", "text"))
                })
    
    return formatted

def _get_display_name(text_type: str) -> str:
    """获取文字类型的显示名称"""
    type_names = {
        "title": "标题",
        "subtitle": "副标题",
        "text": "正文",
        "list": "列表",
        "footer": "页脚"
    }
    return type_names.get(text_type, "文本")

def _convert_confirmed_content_to_request(confirmed_content: Dict[str, Any]) -> str:
    """将确认的内容转换为用户请求格式"""
    try:
        # 将确认的内容转换为文本描述
        request_parts = ["用户确认的PPT内容："]

        if isinstance(confirmed_content, dict):
            for page_key, page_content in confirmed_content.items():
                if isinstance(page_content, dict):
                    request_parts.append(f"\n{page_key}:")
                    for element_key, element_data in page_content.items():
                        if isinstance(element_data, dict):
                            content = element_data.get("content", "")
                            element_type = element_data.get("type", "text")
                            request_parts.append(f"  {element_type}: {content}")
                        else:
                            request_parts.append(f"  {element_key}: {element_data}")

        return "\n".join(request_parts)
    except Exception as e:
        logger.warning(f"转换确认内容失败，使用原始内容: {e}")
        return str(confirmed_content)
