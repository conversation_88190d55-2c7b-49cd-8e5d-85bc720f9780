# -*- coding: utf-8 -*-
"""
PPT生成系统 - LLM配置文件
从根目录的config.json统一读取配置
"""
import json
from pathlib import Path

# 获取根目录配置文件路径
current_dir = Path(__file__).parent
root_dir = current_dir.parent
config_file = root_dir / "config.json"

# 默认配置
DEFAULT_MODEL_CONFIG = {
    'api_key': 'sk-2h1RwdRjqYc6FosI3aus',
    'model': 'deepseek-v3-0324',
    'api_base': 'http://192.168.78.35/gateway/ai-service/v1',
    'timeout': 180,  # API请求超时时间（秒）
}

# 尝试从根目录配置文件读取
try:
    if config_file.exists():
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        MODEL_CONFIG = config_data.get('llm', DEFAULT_MODEL_CONFIG)
    else:
        MODEL_CONFIG = DEFAULT_MODEL_CONFIG
except Exception as e:
    print(f"读取配置文件失败: {e}")
    MODEL_CONFIG = DEFAULT_MODEL_CONFIG

if __name__ == '__main__':
    # 用于测试打印配置内容
    print("MODEL_CONFIG:", MODEL_CONFIG)
    print("配置文件路径:", config_file)
    print("配置文件存在:", config_file.exists())
