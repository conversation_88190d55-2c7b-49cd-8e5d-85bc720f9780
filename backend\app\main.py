"""
FastAPI主应用
"""
import sys
import os
import logging
from pathlib import Path
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles

# 添加路径
current_dir = Path(__file__).parent.parent
sys.path.insert(0, str(current_dir))
sys.path.insert(0, str(current_dir / "llm"))
sys.path.insert(0, str(current_dir.parent / "moffee-base"))

# 分别导入，避免一个失败影响全部
settings = None
chat = None
generate = None
sse = None
templates_router = None
template_router = None

try:
    from config import settings
except ImportError as e:
    logging.warning(f"配置导入失败: {e}")
    # 创建默认设置
    class DefaultSettings:
        APP_NAME = "PPT生成助手"
        APP_VERSION = "1.0.0"
        DEBUG = True
        ALLOWED_ORIGINS = ["http://localhost:3000", "http://127.0.0.1:3000"]
        UPLOAD_DIR = "uploads"
    settings = DefaultSettings()

try:
    from app.api import chat, generate, sse
except ImportError as e:
    logging.warning(f"API模块导入失败: {e}")

try:
    from app.api.templates import router as templates_router
    logging.info("模板API路由导入成功")
except ImportError as e:
    logging.warning(f"模板API导入失败: {e}")
    import traceback
    logging.warning(f"详细错误信息: {traceback.format_exc()}")
    templates_router = None

try:
    from app.template_routes import router as template_router
except ImportError as e:
    logging.warning(f"模板路由导入失败: {e}")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

try:
    from app.api.template_ppt import router as template_ppt_router
    logging.info("新模板PPT API路由导入成功")
except ImportError as e:
    logging.warning(f"新模板PPT API导入失败: {e}")
    template_ppt_router = None

# 创建FastAPI应用
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="基于对话的智能PPT生成系统"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 静态文件服务
upload_dir = os.path.abspath(settings.UPLOAD_DIR)
if not os.path.exists(upload_dir):
    os.makedirs(upload_dir)

# 创建静态文件目录
static_dir = os.path.join(upload_dir, "static")
if not os.path.exists(static_dir):
    os.makedirs(static_dir)

# 挂载静态文件目录
app.mount("/files", StaticFiles(directory=upload_dir), name="files")
app.mount("/static", StaticFiles(directory=static_dir), name="static")

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": f"欢迎使用{settings.APP_NAME}",
        "version": settings.APP_VERSION,
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy"}

# 注册API路由
if chat:
    app.include_router(chat.router, prefix="/api/chat", tags=["对话"])
if generate:
    app.include_router(generate.router, prefix="/api/generate", tags=["生成"])
if sse:
    app.include_router(sse.router, prefix="/api/sse", tags=["流式生成"])
if templates_router:
    app.include_router(templates_router, prefix="/api/templates", tags=["模板管理"])
if template_router:
    app.include_router(template_router, prefix="/api/template", tags=["模板路由"])
if template_ppt_router:
    app.include_router(template_ppt_router, tags=["新模板PPT"])
    logging.info("新模板PPT API路由注册成功")
else:
    logging.warning("新模板PPT API路由为None，未注册")

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logging.info("PPT Agent API 启动成功")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logging.info("PPT Agent API 关闭")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG
    )
