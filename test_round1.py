import sys
sys.path.insert(0, './backend')

try:
    from app.services.professional_ppt_generator import professional_ppt_generator
    
    print('🔄 第1轮测试：专业PPT生成器...')
    print()
    
    template_id = 'template_20250708_221451_adc64662'
    user_request = '请生成一个关于人工智能技术发展的PPT，包括技术背景、核心算法、应用场景和未来趋势'
    output_filename = 'round1_test.pptx'
    
    print(f'模板ID: {template_id}')
    print(f'用户需求: {user_request}')
    print(f'输出文件: {output_filename}')
    print()
    
    # 执行完整流程
    result = professional_ppt_generator.generate_ppt_from_template(
        template_id, user_request, output_filename
    )
    
    if result.get('success'):
        print('✅ 第1轮测试成功!')
        print(f'   - 文件路径: {result["file_path"]}')
        print(f'   - 文件大小: {result["file_size"]/1024/1024:.1f} MB')
        print(f'   - 幻灯片数: {result["slides_count"]}')
        print(f'   - 引擎类型: {result["engine_type"]}')
        
        # 验证生成的PPT
        from pptx import Presentation
        prs = Presentation(result["file_path"])
        
        print()
        print('📊 生成结果验证:')
        print(f'   - 总页数: {len(prs.slides)}')
        
        # 检查前3页的内容
        for i, slide in enumerate(prs.slides[:3]):
            text_shapes = [shape for shape in slide.shapes if hasattr(shape, 'text') and shape.text.strip()]
            print(f'   - 第{i+1}页: {len(text_shapes)}个文字元素')
            
            for j, shape in enumerate(text_shapes[:2]):  # 只显示前2个
                text_preview = shape.text[:50].replace('\n', ' ') if shape.text else '(空)'
                print(f'     文字{j+1}: {text_preview}')
        
        print()
        print('🎯 第1轮评估:')
        print('✅ 基本流程完成')
        print('✅ 文件生成成功')
        print('⚠️  需要验证是否还有原文字残留')
        print('⚠️  需要验证字数控制是否精确')
        
    else:
        print('❌ 第1轮测试失败!')
        print(f'   错误: {result.get("error", "未知错误")}')

except Exception as e:
    print(f'❌ 第1轮测试异常: {e}')
    import traceback
    traceback.print_exc()
