# -*- coding: utf-8 -*-
"""
PPT生成系统 - 响应处理模块
负责处理大模型响应、JSON解析、数据转换等
专为PPT生成优化
"""

import logging
import json
import re

logger = logging.getLogger(__name__)

class ResponseProcessor:
    """响应处理器 - 专为PPT生成优化"""
    
    def __init__(self):
        """初始化响应处理器"""
        logger.debug("初始化PPT响应处理器")
    
    def process_ppt_response(self, response):
        """
        处理PPT生成响应
        
        Args:
            response: 大模型响应
            
        Returns:
            dict: 处理后的PPT内容
        """
        if not response:
            logger.error("响应为空")
            return {"success": False, "error": "响应为空"}
        
        logger.info(f"处理PPT生成响应，响应长度: {len(response)}")
        
        # 尝试提取JSON格式的结构化内容
        json_content = self._extract_json_from_response(response)
        if json_content:
            logger.info("成功提取JSON格式的PPT内容")
            return {"success": True, "content": json_content, "format": "json"}
        
        # 如果没有JSON，直接返回文本内容
        logger.info("未找到JSON格式，返回文本内容")
        cleaned_content = self._clean_text_response(response)
        return {"success": True, "content": cleaned_content, "format": "text"}

    def _extract_json_from_response(self, response):
        """
        从响应文本中提取JSON内容
        """
        try:
            # 优先查找 ```json ... ``` 代码块
            json_pattern = r'```json\s*\n(.*?)\n```'
            json_matches = re.findall(json_pattern, response, re.DOTALL)
            if json_matches:
                json_str = json_matches[0].strip()
                try:
                    return json.loads(json_str)
                except json.JSONDecodeError:
                    logger.warning("JSON代码块解析失败")
            
            # 尝试查找独立的JSON对象 `{...}`
            obj_pattern = r'\{[\s\S]*?\}'
            potential_objects = re.findall(obj_pattern, response)
            for obj_str in potential_objects:
                try:
                    return json.loads(obj_str)
                except json.JSONDecodeError:
                    continue
            
            # 尝试查找独立的JSON数组 `[...]`
            array_pattern = r'\[[\s\S]*?\]'
            potential_arrays = re.findall(array_pattern, response)
            for array_str in potential_arrays:
                try:
                    return json.loads(array_str)
                except json.JSONDecodeError:
                    continue
            
            logger.debug("在响应中未找到有效的JSON结构")
            return None
            
        except Exception as e:
            logger.error(f"提取JSON时发生错误: {e}")
            return None
    
    def _clean_text_response(self, response):
        """
        清理文本响应，移除不必要的格式
        """
        if not response:
            return ""
        
        # 移除多余的空行
        cleaned = re.sub(r'\n\s*\n\s*\n', '\n\n', response)
        
        # 移除首尾空白
        cleaned = cleaned.strip()
        
        return cleaned
    
    def extract_ppt_outline(self, response):
        """
        从响应中提取PPT大纲结构
        """
        try:
            # 尝试提取JSON格式的大纲
            json_content = self._extract_json_from_response(response)
            if json_content and isinstance(json_content, (list, dict)):
                return {"success": True, "outline": json_content, "format": "json"}
            
            # 如果没有JSON，尝试解析markdown格式的大纲
            markdown_outline = self._parse_markdown_outline(response)
            if markdown_outline:
                return {"success": True, "outline": markdown_outline, "format": "markdown"}
            
            # 如果都没有，返回原始文本
            return {"success": True, "outline": response, "format": "text"}
            
        except Exception as e:
            logger.error(f"提取PPT大纲时发生错误: {e}")
            return {"success": False, "error": str(e)}
    
    def _parse_markdown_outline(self, text):
        """
        解析markdown格式的大纲
        """
        try:
            lines = text.split('\n')
            outline = []
            current_slide = None
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                # 检测标题（# ## ###）
                if line.startswith('#'):
                    level = len(line) - len(line.lstrip('#'))
                    title = line.lstrip('#').strip()
                    
                    if level == 1:  # 主标题，新的幻灯片
                        if current_slide:
                            outline.append(current_slide)
                        current_slide = {
                            "title": title,
                            "content": [],
                            "level": level
                        }
                    elif level == 2 and current_slide:  # 子标题
                        current_slide["content"].append({
                            "type": "subtitle",
                            "text": title
                        })
                    elif level >= 3 and current_slide:  # 更小的标题
                        current_slide["content"].append({
                            "type": "point",
                            "text": title
                        })
                
                # 检测列表项
                elif line.startswith(('-', '*', '+')):
                    if current_slide:
                        current_slide["content"].append({
                            "type": "bullet",
                            "text": line[1:].strip()
                        })
                
                # 检测数字列表
                elif re.match(r'^\d+\.', line):
                    if current_slide:
                        current_slide["content"].append({
                            "type": "numbered",
                            "text": re.sub(r'^\d+\.\s*', '', line)
                        })
                
                # 普通文本
                elif current_slide:
                    current_slide["content"].append({
                        "type": "text",
                        "text": line
                    })
            
            # 添加最后一个幻灯片
            if current_slide:
                outline.append(current_slide)
            
            return outline if outline else None
            
        except Exception as e:
            logger.error(f"解析markdown大纲时发生错误: {e}")
            return None

    def validate_response(self, response):
        """
        验证响应的有效性
        """
        if not response:
            return False, "响应为空"
        
        if not isinstance(response, str):
            return False, "响应必须是字符串"
        
        if len(response.strip()) < 10:
            return False, "响应内容过短"
        
        return True, "响应有效"

    def format_response_for_logging(self, response, max_length=200):
        """
        格式化响应用于日志记录
        """
        if not response:
            return "空响应"
        
        formatted = re.sub(r'\s+', ' ', str(response).strip())
        if len(formatted) > max_length:
            formatted = formatted[:max_length] + "..."
        
        return formatted

    def extract_error_info(self, response):
        """
        从响应中提取错误信息
        """
        if not response:
            return None
        
        # 查找常见的错误模式
        error_patterns = [
            r'错误[:：]\s*(.+)',
            r'error[:：]\s*(.+)',
            r'失败[:：]\s*(.+)',
            r'异常[:：]\s*(.+)'
        ]
        
        for pattern in error_patterns:
            match = re.search(pattern, response, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        
        return None
