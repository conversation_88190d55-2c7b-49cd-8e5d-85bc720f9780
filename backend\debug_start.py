#!/usr/bin/env python3
"""
调试版本的后端启动脚本
"""

import sys
import os
from pathlib import Path
import logging

# 配置详细日志
logging.basicConfig(
    level=logging.DEBUG, 
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def main():
    logger.info("=" * 50)
    logger.info("开始启动PPT Agent后端服务")
    logger.info("=" * 50)
    
    try:
        # 1. 设置路径
        current_dir = Path(__file__).parent
        logger.info(f"当前目录: {current_dir}")

        # 确保backend目录在最前面，避免与llm/config.py冲突
        sys.path.insert(0, str(current_dir))
        llm_path = current_dir / "llm"
        sys.path.append(str(llm_path))  # 使用append而不是insert
        logger.info("Python路径设置完成")
        logger.info(f"Python路径: {sys.path[:3]}...")
        
        # 2. 测试配置导入
        logger.info("测试配置导入...")
        try:
            from config import settings
            logger.info(f"✅ 配置导入成功，CORS源: {len(settings.ALLOWED_ORIGINS)} 个")
        except Exception as e:
            logger.error(f"❌ 配置导入失败: {e}")
            return False
        
        # 3. 测试模板管理器
        logger.info("测试模板管理器...")
        try:
            from services.template_manager import template_manager
            templates = template_manager.get_template_list()
            logger.info(f"✅ 模板管理器正常，{len(templates)} 个模板")
        except Exception as e:
            logger.error(f"❌ 模板管理器失败: {e}")
            return False
        
        # 4. 测试应用导入
        logger.info("测试应用导入...")
        try:
            from app.main import app
            logger.info("✅ 应用导入成功")
        except Exception as e:
            logger.error(f"❌ 应用导入失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False
        
        # 5. 启动服务器
        logger.info("启动uvicorn服务器...")
        import uvicorn
        uvicorn.run(
            app,
            host="127.0.0.1",
            port=9527,
            log_level="info",
            access_log=True
        )
        
    except Exception as e:
        logger.error(f"启动失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    main()
