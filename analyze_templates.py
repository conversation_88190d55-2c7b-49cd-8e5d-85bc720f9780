from pptx import Presentation

# 详细分析我们的模板
print('=== 我们的模板分析 ===')
our_template = Presentation('backend/templates/template_20250708_221451_adc64662.pptx')
print(f'文件: template_20250708_221451_adc64662.pptx')
print(f'幻灯片数量: {len(our_template.slides)}')
print(f'布局数量: {len(our_template.slide_layouts)}')
print(f'母版数量: {len(our_template.slide_masters)}')

print('\n布局详情:')
for i, layout in enumerate(our_template.slide_layouts):
    print(f'  [{i}] 名称: "{layout.name}" 占位符: {len(layout.placeholders)}')

print('\n=== 默认模板分析 ===')
default_template = Presentation('md2pptx/Martin Template.pptx')
print(f'文件: Martin Template.pptx')
print(f'幻灯片数量: {len(default_template.slides)}')
print(f'布局数量: {len(default_template.slide_layouts)}')
print(f'母版数量: {len(default_template.slide_masters)}')

print('\n布局详情:')
for i, layout in enumerate(default_template.slide_layouts):
    print(f'  [{i}] 名称: "{layout.name}" 占位符: {len(layout.placeholders)}')

print('\n=== md2pptx需要的布局索引 ===')
required_layouts = {
    0: 'titleSlideLayout (标题幻灯片)',
    1: 'sectionSlideLayout (章节标题)', 
    2: 'contentSlideLayout (内容幻灯片)',
    5: 'titleOnlyLayout (仅标题)',
    6: 'blanklayout (空白)'
}

for index, desc in required_layouts.items():
    our_has = index < len(our_template.slide_layouts)
    default_has = index < len(default_template.slide_layouts)
    print(f'  [{index}] {desc}')
    print(f'      我们的模板: {"✅" if our_has else "❌"} 默认模板: {"✅" if default_has else "❌"}')

print('\n=== 我们模板的幻灯片内容分析 ===')
for i, slide in enumerate(our_template.slides[:3]):  # 只看前3张
    print(f'幻灯片 {i+1}:')
    for j, shape in enumerate(slide.shapes):
        if hasattr(shape, 'text'):
            text_preview = shape.text[:50].replace('\n', ' ') if shape.text else '(空)'
            print(f'  形状{j}: {shape.shape_type} - "{text_preview}"')
        else:
            print(f'  形状{j}: {shape.shape_type} - (无文本)')
