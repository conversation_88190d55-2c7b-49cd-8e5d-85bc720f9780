"""
模板管理API路由
"""

import os
import tempfile
from pathlib import Path
from typing import List, Optional

from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends
from fastapi.responses import FileResponse
from pydantic import BaseModel

import sys
from pathlib import Path

# 添加backend目录到路径
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from services.template_manager import template_manager

router = APIRouter(tags=["templates"])


class TemplateInfo(BaseModel):
    """模板信息模型"""
    id: str
    name: str
    description: str
    category: str
    tags: List[str]
    upload_time: str
    file_size: int
    status: str = "ready"  # 添加status字段，默认为ready


class TemplateDetails(BaseModel):
    """模板详细信息模型"""
    id: str
    name: str
    description: str
    category: str
    tags: List[str]
    upload_time: str
    file_size: int
    file_hash: str
    analysis: dict


class UploadResponse(BaseModel):
    """上传响应模型"""
    success: bool
    message: str
    template_id: Optional[str] = None
    metadata: Optional[dict] = None


class AIPromptResponse(BaseModel):
    """AI提示词响应模型"""
    success: bool
    prompt: Optional[str] = None
    template_info: Optional[dict] = None
    error: Optional[str] = None


@router.get("/", response_model=List[TemplateInfo])
@router.get("/all", response_model=List[TemplateInfo])
async def get_templates():
    """获取所有模板列表"""
    try:
        import logging
        logger = logging.getLogger(__name__)

        templates = template_manager.get_template_list()
        logger.info(f"旧API系统调用模板管理器，返回 {len(templates)} 个模板")

        if templates:
            first_template = templates[0]
            logger.info(f"第一个模板字段: {list(first_template.keys())}")
            logger.info(f"是否有status字段: {'status' in first_template}")

        return templates
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模板列表失败: {str(e)}")


@router.get("/{template_id}", response_model=TemplateDetails)
async def get_template_details(template_id: str):
    """获取模板详细信息"""
    try:
        template_details = template_manager.get_template_details(template_id)
        if not template_details:
            raise HTTPException(status_code=404, detail="模板不存在")
        return template_details
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模板详情失败: {str(e)}")


@router.post("/upload", response_model=UploadResponse)
async def upload_template(
    file: UploadFile = File(...),
    name: str = Form(...),
    description: str = Form(""),
    category: str = Form("general"),
    tags: str = Form("")
):
    """上传PPT模板"""
    try:
        # 验证文件类型
        if not file.filename.lower().endswith(('.pptx', '.pptm')):
            raise HTTPException(
                status_code=400, 
                detail="不支持的文件格式，请上传.pptx或.pptm文件"
            )
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix=Path(file.filename).suffix) as temp_file:
            # 读取上传的文件内容
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        try:
            # 上传模板
            result = template_manager.upload_template(
                file_path=temp_file_path,
                template_name=name,
                description=description,
                category=category,
                tags=tags
            )
            
            if result["success"]:
                return UploadResponse(
                    success=True,
                    message="模板上传成功",
                    template_id=result["template_id"],
                    metadata=result["metadata"]
                )
            else:
                raise HTTPException(status_code=400, detail=result["error"])
                
        finally:
            # 清理临时文件
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
                
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"上传模板失败: {str(e)}")


@router.delete("/{template_id}")
async def delete_template(template_id: str):
    """删除模板"""
    try:
        result = template_manager.delete_template(template_id)
        if result["success"]:
            return {"success": True, "message": result["message"]}
        else:
            raise HTTPException(status_code=400, detail=result["error"])
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除模板失败: {str(e)}")


@router.get("/{template_id}/download")
async def download_template(template_id: str):
    """下载模板文件"""
    try:
        template_details = template_manager.get_template_details(template_id)
        if not template_details:
            raise HTTPException(status_code=404, detail="模板不存在")
        
        file_path = template_details["file_path"]
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="模板文件不存在")
        
        # 生成下载文件名
        filename = f"{template_details['name']}{Path(file_path).suffix}"
        
        return FileResponse(
            path=file_path,
            filename=filename,
            media_type="application/vnd.openxmlformats-officedocument.presentationml.presentation"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"下载模板失败: {str(e)}")


@router.post("/{template_id}/analyze")
async def analyze_template(template_id: str):
    """重新分析模板（集成新的基于成品PPT的解析）"""
    try:
        import logging
        logger = logging.getLogger(__name__)

        logger.info(f"开始重新分析模板: {template_id}")

        # 1. 执行旧的分析（保持兼容性）
        old_result = template_manager.reanalyze_template(template_id)

        # 2. 执行新的基于成品PPT的解析
        try:
            from app.services.template_based_ppt_generator import template_ppt_generator
            new_result = template_ppt_generator.reparse_template(template_id)

            if new_result["success"]:
                logger.info(f"新方案解析成功: {template_id}")

                # 保存新解析结果到文件
                import json
                from pathlib import Path

                # 创建解析结果目录
                analysis_dir = Path("template_analysis")
                analysis_dir.mkdir(exist_ok=True)

                # 保存结构化JSON
                analysis_file = analysis_dir / f"{template_id}_structure.json"
                with open(analysis_file, 'w', encoding='utf-8') as f:
                    json.dump(new_result["template_structure"], f, ensure_ascii=False, indent=2)

                logger.info(f"结构化JSON已保存: {analysis_file}")

                # 合并结果
                combined_result = {
                    "success": True,
                    "template_id": template_id,
                    "analysis": old_result.get("analysis", {}) if old_result["success"] else {},
                    "new_structure": new_result["template_structure"],
                    "total_pages": new_result["total_pages"],
                    "total_placeholders": new_result["total_placeholders"],
                    "structure_file": str(analysis_file),
                    "message": "模板重新分析完成（包含新方案结构化数据）"
                }

                return combined_result
            else:
                logger.warning(f"新方案解析失败: {new_result.get('error')}")

        except Exception as new_error:
            logger.warning(f"新方案解析异常: {new_error}")

        # 3. 如果新方案失败，返回旧方案结果
        if old_result["success"]:
            logger.info(f"模板重新分析成功（仅旧方案）: {template_id}")
            return {
                "success": True,
                "template_id": template_id,
                "analysis": old_result["analysis"],
                "message": "模板重新分析完成（旧方案）"
            }
        else:
            logger.error(f"模板重新分析失败: {old_result.get('error')}")
            raise HTTPException(status_code=400, detail=old_result["error"])

    except HTTPException:
        raise
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"重新分析模板异常: {str(e)}")
        raise HTTPException(status_code=500, detail=f"重新分析模板失败: {str(e)}")


@router.get("/{template_id}/ai-prompt", response_model=AIPromptResponse)
async def get_ai_prompt(template_id: str):
    """获取基于模板的AI提示词"""
    try:
        result = template_manager.generate_ai_prompt(template_id)
        
        if result["success"]:
            return AIPromptResponse(
                success=True,
                prompt=result["prompt"],
                template_info=result["template_info"]
            )
        else:
            return AIPromptResponse(
                success=False,
                error=result["error"]
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成AI提示词失败: {str(e)}")


@router.get("/{template_id}/analysis")
async def get_template_analysis(template_id: str):
    """获取模板分析结果"""
    try:
        import logging
        logger = logging.getLogger(__name__)

        template_details = template_manager.get_template_details(template_id)
        if not template_details:
            logger.error(f"模板不存在: {template_id}")
            raise HTTPException(status_code=404, detail="模板不存在")

        analysis = template_details.get("analysis", {})

        # 构建前端期望的结构
        structure = {
            "slide_layouts": analysis.get("slide_layouts", []),
            "theme_colors": analysis.get("theme_colors", {}),
            "fonts": analysis.get("fonts", {}),
            "slide_size": analysis.get("slide_size", {}),
            "md2pptx_mapping": analysis.get("md2pptx_mapping", {}),
            "recommended_metadata": analysis.get("recommended_metadata", {})
        }

        logger.info(f"返回模板分析结果: {template_id}, 布局数量: {len(structure['slide_layouts'])}")

        return {
            "success": True,
            "template_id": template_id,
            "structure": structure,
            "analysis_summary": {
                "total_layouts": len(structure["slide_layouts"]),
                "has_theme_colors": bool(structure["theme_colors"]),
                "has_fonts": bool(structure["fonts"])
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"获取模板分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取模板分析失败: {str(e)}")


@router.get("/{template_id}/metadata")
async def get_template_metadata(template_id: str):
    """获取模板的md2pptx元数据配置"""
    try:
        template_details = template_manager.get_template_details(template_id)
        if not template_details:
            raise HTTPException(status_code=404, detail="模板不存在")

        analysis = template_details["analysis"]

        return {
            "success": True,
            "md2pptx_mapping": analysis["md2pptx_mapping"],
            "recommended_metadata": analysis["recommended_metadata"],
            "slide_layouts": analysis["slide_layouts"]
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模板元数据失败: {str(e)}")


@router.get("/categories/list")
async def get_template_categories():
    """获取所有模板分类"""
    try:
        templates = template_manager.get_template_list()
        categories = list(set(template["category"] for template in templates))
        categories.sort()
        
        return {
            "success": True,
            "categories": categories
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模板分类失败: {str(e)}")


@router.get("/categories/{category}")
async def get_templates_by_category(category: str):
    """根据分类获取模板"""
    try:
        all_templates = template_manager.get_template_list()
        category_templates = [
            template for template in all_templates 
            if template["category"] == category
        ]
        
        return {
            "success": True,
            "category": category,
            "templates": category_templates,
            "count": len(category_templates)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取分类模板失败: {str(e)}")


@router.delete("/{template_id}")
async def delete_template(template_id: str):
    """删除模板"""
    try:
        import logging
        logger = logging.getLogger(__name__)

        logger.info(f"收到删除模板请求: {template_id}")

        result = template_manager.delete_template(template_id)

        if result["success"]:
            logger.info(f"模板删除成功: {template_id}")
            return {
                "success": True,
                "template_id": template_id,
                "message": "模板删除成功"
            }
        else:
            logger.error(f"模板删除失败: {result.get('error')}")
            raise HTTPException(status_code=400, detail=result["error"])

    except HTTPException:
        raise
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"删除模板异常: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除模板失败: {str(e)}")


@router.get("/{template_id}/preview")
async def get_template_preview(template_id: str):
    """获取模板预览图片"""
    try:
        import logging
        logger = logging.getLogger(__name__)

        # 获取模板详情
        template_details = template_manager.get_template_details(template_id)
        if not template_details:
            raise HTTPException(status_code=404, detail="模板不存在")

        # 检查是否有预览图片路径
        analysis = template_details.get("analysis", {})
        preview_path = analysis.get("preview_image")

        if preview_path:
            # 构建完整的文件路径
            full_path = template_manager.templates_dir / preview_path
            if full_path.exists():
                logger.info(f"返回预览图片: {full_path}")
                return FileResponse(
                    path=str(full_path),
                    media_type="image/png",
                    filename=f"{template_id}_preview.png"
                )

        # 如果没有预览图片，返回默认图片或生成一个
        logger.warning(f"模板 {template_id} 没有预览图片，尝试生成")

        # 尝试重新生成预览图片
        file_path = template_details.get("file_path")
        if file_path and os.path.exists(file_path):
            try:
                from pptx import Presentation
                prs = Presentation(file_path)
                preview_path = template_manager._generate_preview_image(prs, file_path)

                if preview_path:
                    # 更新模板元数据
                    analysis["preview_image"] = preview_path
                    template_details["analysis"] = analysis
                    template_manager.metadata[template_id] = template_details
                    template_manager._save_metadata()

                    full_path = template_manager.templates_dir / preview_path
                    if full_path.exists():
                        return FileResponse(
                            path=str(full_path),
                            media_type="image/png",
                            filename=f"{template_id}_preview.png"
                        )
            except Exception as e:
                logger.error(f"生成预览图片失败: {e}")

        # 返回404如果无法生成预览图片
        raise HTTPException(status_code=404, detail="无法获取预览图片")

    except HTTPException:
        raise
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"获取预览图片异常: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取预览图片失败: {str(e)}")


@router.get("/{template_id}/slides")
async def get_template_slides(template_id: str):
    """获取模板所有幻灯片预览"""
    try:
        import logging
        logger = logging.getLogger(__name__)

        # 获取模板详情
        template_details = template_manager.get_template_details(template_id)
        if not template_details:
            raise HTTPException(status_code=404, detail="模板不存在")

        # 检查是否有幻灯片预览
        analysis = template_details.get("analysis", {})
        slide_previews = analysis.get("slide_previews", [])

        if not slide_previews:
            raise HTTPException(status_code=404, detail="没有幻灯片预览")

        # 检查预览文件是否存在
        valid_previews = []
        for preview_path in slide_previews:
            full_path = template_manager.templates_dir / preview_path
            if full_path.exists():
                valid_previews.append({
                    "path": f"/api/templates/{template_id}/slide/{len(valid_previews) + 1}",
                    "slide_number": len(valid_previews) + 1
                })

        return {
            "success": True,
            "template_id": template_id,
            "total_slides": len(valid_previews),
            "slides": valid_previews
        }

    except HTTPException:
        raise
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"获取幻灯片预览异常: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取幻灯片预览失败: {str(e)}")


@router.get("/{template_id}/slide/{slide_number}")
async def get_template_slide(template_id: str, slide_number: int):
    """获取指定幻灯片预览图片"""
    try:
        import logging
        logger = logging.getLogger(__name__)

        # 获取模板详情
        template_details = template_manager.get_template_details(template_id)
        if not template_details:
            raise HTTPException(status_code=404, detail="模板不存在")

        # 检查是否有幻灯片预览
        analysis = template_details.get("analysis", {})
        slide_previews = analysis.get("slide_previews", [])

        if not slide_previews or slide_number < 1 or slide_number > len(slide_previews):
            raise HTTPException(status_code=404, detail="幻灯片不存在")

        # 获取指定幻灯片的预览路径
        preview_path = slide_previews[slide_number - 1]
        full_path = template_manager.templates_dir / preview_path

        if full_path.exists():
            return FileResponse(
                path=str(full_path),
                media_type="image/png",
                filename=f"{template_id}_slide_{slide_number}.png"
            )
        else:
            raise HTTPException(status_code=404, detail="幻灯片预览图片不存在")

    except HTTPException:
        raise
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"获取幻灯片预览异常: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取幻灯片预览失败: {str(e)}")


@router.get("/libreoffice/test")
async def test_libreoffice():
    """测试LibreOffice是否可用"""
    try:
        import logging
        logger = logging.getLogger(__name__)

        from services.libreoffice_converter import libreoffice_converter

        # 执行LibreOffice测试
        test_result = libreoffice_converter.test_conversion()

        logger.info(f"LibreOffice测试结果: {test_result}")

        return {
            "success": True,
            "libreoffice": test_result
        }

    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"LibreOffice测试异常: {str(e)}")
        return {
            "success": False,
            "error": f"测试失败: {str(e)}"
        }
