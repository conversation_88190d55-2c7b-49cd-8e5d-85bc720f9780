"""
PPT模板处理器
将成品PPT转换为带占位符的模板，并支持内容回填
"""

import logging
import json
import shutil
from pathlib import Path
from typing import Dict, List, Any
from pptx import Presentation
from pptx.util import Pt

logger = logging.getLogger(__name__)


class PPTTemplateProcessor:
    """PPT模板处理器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent.parent.parent
        self.templates_dir = self.project_root / "backend" / "templates"
        self.processed_templates_dir = self.project_root / "backend" / "processed_templates"
        import sys
        import os
        # 添加根目录到Python路径
        root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
        if root_dir not in sys.path:
            sys.path.insert(0, root_dir)

        from config_loader import settings
        self.analysis_cache_dir = self.project_root / "backend" / settings.TEMPLATE_ANALYSIS_DIR
        self.output_dir = self.project_root / "backend" / "uploads" / "generated"
        
        # 确保目录存在
        self.processed_templates_dir.mkdir(exist_ok=True)
        self.output_dir.mkdir(exist_ok=True)
    
    def create_placeholder_template(self, template_id: str, analysis_result: Dict[str, Any]) -> str:
        """
        基于分析结果创建占位符模板
        
        Args:
            template_id: 模板ID
            analysis_result: 模板分析结果
            
        Returns:
            占位符模板文件路径
        """
        try:
            logger.info(f"开始创建占位符模板: {template_id}")
            
            # 加载原始PPT
            original_path = self.templates_dir / f"{template_id}.pptx"
            prs = Presentation(str(original_path))
            
            # 替换文字为占位符
            for slide_idx, slide in enumerate(prs.slides):
                slide_analysis = analysis_result["slides_analysis"][slide_idx]
                self._replace_text_with_placeholders(slide, slide_analysis)
            
            # 保存占位符模板
            placeholder_template_path = self.processed_templates_dir / f"{template_id}_placeholder.pptx"
            prs.save(str(placeholder_template_path))
            
            logger.info(f"占位符模板创建完成: {placeholder_template_path}")
            return str(placeholder_template_path)
            
        except Exception as e:
            logger.error(f"创建占位符模板失败: {e}")
            raise
    
    def _replace_text_with_placeholders(self, slide, slide_analysis: Dict[str, Any]):
        """将幻灯片文字替换为占位符"""
        text_elements = slide_analysis["text_elements"]
        
        # 获取所有文字形状
        text_shapes = [shape for shape in slide.shapes if hasattr(shape, 'text')]
        
        # 按位置匹配文字形状和分析结果
        for i, text_element in enumerate(text_elements):
            if i < len(text_shapes):
                shape = text_shapes[i]
                placeholder_text = f"{{{{ {text_element['placeholder_id']} }}}}"
                
                try:
                    # 保留原有样式，只替换文字内容
                    shape.text = placeholder_text
                    logger.debug(f"替换占位符: {text_element['placeholder_id']}")
                except Exception as e:
                    logger.warning(f"替换占位符失败: {e}")
    
    def generate_ppt_from_content(self, template_id: str, generated_content: Dict[str, Any], 
                                 analysis_result: Dict[str, Any], output_filename: str) -> Dict[str, Any]:
        """
        基于生成的内容和模板创建新PPT
        
        Args:
            template_id: 模板ID
            generated_content: 大模型生成的内容
            analysis_result: 模板分析结果
            output_filename: 输出文件名
            
        Returns:
            生成结果
        """
        try:
            logger.info(f"开始基于内容生成PPT: {template_id}")
            
            # 加载原始模板（保留所有设计元素）
            original_path = self.templates_dir / f"{template_id}.pptx"
            prs = Presentation(str(original_path))
            
            # 验证内容结构
            if not self._validate_content_structure(generated_content, analysis_result):
                raise ValueError("生成的内容结构与模板不匹配")
            
            # 逐页替换内容
            slides_content = generated_content.get("slides", [])
            for slide_idx, slide in enumerate(prs.slides):
                if slide_idx < len(slides_content):
                    slide_content = slides_content[slide_idx]
                    slide_analysis = analysis_result["slides_analysis"][slide_idx]
                    self._fill_slide_content(slide, slide_content, slide_analysis)
            
            # 保存新PPT
            output_path = self.output_dir / output_filename
            prs.save(str(output_path))
            
            result = {
                "success": True,
                "file_path": str(output_path),
                "output_path": str(output_path),
                "slides_count": len(slides_content),
                "template_used": template_id,
                "file_size": output_path.stat().st_size if output_path.exists() else 0,
                "engine_type": "template_based"
            }
            
            logger.info(f"PPT生成成功: {output_path}")
            return result
            
        except Exception as e:
            logger.error(f"基于内容生成PPT失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "engine_type": "template_based"
            }
    
    def _validate_content_structure(self, generated_content: Dict[str, Any], 
                                   analysis_result: Dict[str, Any]) -> bool:
        """验证生成内容的结构是否与模板匹配"""
        try:
            slides_content = generated_content.get("slides", [])
            slides_analysis = analysis_result.get("slides_analysis", [])
            
            if len(slides_content) != len(slides_analysis):
                logger.warning(f"页数不匹配: 生成{len(slides_content)}页，模板{len(slides_analysis)}页")
                return False
            
            # 验证每页的文字元素数量
            for i, (slide_content, slide_analysis) in enumerate(zip(slides_content, slides_analysis)):
                content_elements = slide_content.get("text_elements", {})
                required_elements = slide_analysis.get("text_elements", [])
                
                required_ids = {elem["placeholder_id"] for elem in required_elements}
                provided_ids = set(content_elements.keys())
                
                if not required_ids.issubset(provided_ids):
                    missing_ids = required_ids - provided_ids
                    logger.warning(f"第{i+1}页缺少占位符: {missing_ids}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证内容结构失败: {e}")
            return False
    
    def _fill_slide_content(self, slide, slide_content: Dict[str, Any], 
                           slide_analysis: Dict[str, Any]):
        """填充单张幻灯片的内容"""
        try:
            text_elements_content = slide_content.get("text_elements", {})
            text_elements_analysis = slide_analysis.get("text_elements", [])
            
            # 获取所有文字形状
            text_shapes = [shape for shape in slide.shapes if hasattr(shape, 'text')]
            
            # 按分析结果的顺序填充内容
            for i, text_element_analysis in enumerate(text_elements_analysis):
                placeholder_id = text_element_analysis["placeholder_id"]
                
                if placeholder_id in text_elements_content and i < len(text_shapes):
                    new_content = text_elements_content[placeholder_id]
                    shape = text_shapes[i]
                    
                    # 保留原有样式，替换文字内容
                    self._replace_text_preserve_style(shape, new_content, text_element_analysis)
                    
                    logger.debug(f"填充内容: {placeholder_id} -> {new_content[:30]}...")
            
        except Exception as e:
            logger.error(f"填充幻灯片内容失败: {e}")
    
    def _replace_text_preserve_style(self, shape, new_content: str, 
                                   text_element_analysis: Dict[str, Any]):
        """替换文字内容但保留样式"""
        try:
            # 获取原有字体信息
            original_font_info = text_element_analysis.get("font_info", {})
            
            # 替换文字
            shape.text = new_content
            
            # 尝试保持原有样式
            if hasattr(shape, 'text_frame') and shape.text_frame.paragraphs:
                for para in shape.text_frame.paragraphs:
                    if para.runs:
                        for run in para.runs:
                            font = run.font
                            
                            # 恢复字体样式
                            if original_font_info.get("font_name"):
                                font.name = original_font_info["font_name"]
                            
                            if original_font_info.get("font_size"):
                                font.size = Pt(original_font_info["font_size"])
                            
                            if original_font_info.get("font_bold") is not None:
                                font.bold = original_font_info["font_bold"]
                            
                            if original_font_info.get("font_italic") is not None:
                                font.italic = original_font_info["font_italic"]
            
        except Exception as e:
            logger.warning(f"保留样式失败，使用默认样式: {e}")
            shape.text = new_content
    
    def get_template_requirements(self, template_id: str) -> Dict[str, Any]:
        """
        获取模板的生成要求
        
        Args:
            template_id: 模板ID
            
        Returns:
            模板要求信息
        """
        try:
            analysis_file = self.analysis_cache_dir / f"{template_id}_analysis.json"
            
            if not analysis_file.exists():
                raise FileNotFoundError(f"模板分析文件不存在: {analysis_file}")
            
            with open(analysis_file, 'r', encoding='utf-8') as f:
                analysis_result = json.load(f)
            
            return analysis_result.get("generation_requirements", {})
            
        except Exception as e:
            logger.error(f"获取模板要求失败: {e}")
            return {}
    
    def load_template_analysis(self, template_id: str) -> Dict[str, Any]:
        """加载模板分析结果"""
        try:
            analysis_file = self.analysis_cache_dir / f"{template_id}_analysis.json"
            
            if not analysis_file.exists():
                return {}
            
            with open(analysis_file, 'r', encoding='utf-8') as f:
                return json.load(f)
                
        except Exception as e:
            logger.error(f"加载模板分析失败: {e}")
            return {}


# 创建全局实例
ppt_template_processor = PPTTemplateProcessor()
