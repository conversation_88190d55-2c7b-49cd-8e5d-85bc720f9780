#!/usr/bin/env python3
"""
临时脚本：分析PPT模板文件
"""

import sys
import os
from pathlib import Path

# 添加当前目录到路径
sys.path.insert(0, '.')

from pptx import Presentation

def analyze_template_file():
    """详细分析模板文件"""
    file_path = 'templates/template_20250708_045658_adc64662.pptx'
    
    print('=== 模板文件详细分析 ===')
    print(f'文件路径: {file_path}')
    print(f'文件存在: {os.path.exists(file_path)}')
    
    if not os.path.exists(file_path):
        print('❌ 文件不存在')
        return
    
    print(f'文件大小: {os.path.getsize(file_path)} bytes')

    try:
        prs = Presentation(file_path)
        print(f'\n=== PPT基本信息 ===')
        print(f'幻灯片数量: {len(prs.slides)}')
        print(f'幻灯片尺寸: {prs.slide_width} x {prs.slide_height}')
        
        # 检查母版
        slide_master = prs.slide_master
        print(f'\n=== 母版信息 ===')
        print(f'母版布局数量: {len(slide_master.slide_layouts)}')
        
        # 详细检查每个布局
        for i, layout in enumerate(slide_master.slide_layouts):
            print(f'\n--- 布局 {i} ---')
            print(f'  名称: "{layout.name}"')
            print(f'  占位符数量: {len(layout.placeholders)}')
            
            # 检查占位符
            for j, placeholder in enumerate(layout.placeholders):
                try:
                    ph_type = placeholder.placeholder_format.type
                    ph_idx = placeholder.placeholder_format.idx
                    ph_name = getattr(placeholder, 'name', '无名称')
                    print(f'    占位符{j}: 类型={ph_type}, 索引={ph_idx}, 名称="{ph_name}"')
                except Exception as e:
                    print(f'    占位符{j}: 获取信息失败 - {e}')
            
            # 检查形状
            print(f'  形状数量: {len(layout.shapes)}')
            for j, shape in enumerate(layout.shapes[:5]):  # 只显示前5个
                try:
                    shape_type = shape.shape_type
                    has_text = hasattr(shape, 'text_frame')
                    shape_name = getattr(shape, 'name', '无名称')
                    print(f'    形状{j}: 类型={shape_type}, 有文本框={has_text}, 名称="{shape_name}"')
                    
                    # 如果有文本框，检查文本内容
                    if has_text and shape.text_frame.text.strip():
                        text_preview = shape.text_frame.text[:50].replace('\n', ' ')
                        print(f'      文本预览: "{text_preview}..."')
                        
                except Exception as e:
                    print(f'    形状{j}: 获取信息失败 - {e}')
        
        # 检查实际幻灯片
        print(f'\n=== 实际幻灯片信息 ===')
        for i, slide in enumerate(prs.slides[:3]):  # 只检查前3张
            print(f'幻灯片{i}: 形状数量={len(slide.shapes)}')
            
            # 检查幻灯片中的文本内容
            text_content = []
            for shape in slide.shapes:
                if hasattr(shape, 'text_frame') and shape.text_frame.text.strip():
                    text_content.append(shape.text_frame.text.strip()[:30])
            
            if text_content:
                print(f'  文本内容: {text_content[:3]}')
        
        # 尝试检查主题信息
        print(f'\n=== 主题信息 ===')
        try:
            if hasattr(slide_master, 'theme'):
                print('✅ 母版有theme属性')
                theme = slide_master.theme
                if hasattr(theme, 'color_scheme'):
                    print('✅ 主题有color_scheme')
                else:
                    print('❌ 主题没有color_scheme')
            else:
                print('❌ 母版没有theme属性')
        except Exception as e:
            print(f'检查主题失败: {e}')
            
    except Exception as e:
        print(f'分析失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    analyze_template_file()
