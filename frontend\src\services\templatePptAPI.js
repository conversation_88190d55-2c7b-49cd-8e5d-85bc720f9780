// 新的基于成品PPT模板的API服务
import api from './api';

export const templatePptAPI = {
  // 解析模板
  parseTemplate: async (templateId) => {
    const response = await api.post('/api/template-ppt/parse-template', {
      template_id: templateId
    });
    return response.data;
  },

  // 生成内容（步骤1-4）
  generateContent: async (templateId, userContext, outputFilename = null) => {
    const response = await api.post('/api/template-ppt/generate-content', {
      template_id: templateId,
      user_context: userContext,
      output_filename: outputFilename
    });
    return response.data;
  },

  // 确认内容并生成PPT（步骤5）
  confirmAndGenerate: async (templateId, confirmedContent, outputFilename = null) => {
    const response = await api.post('/api/template-ppt/confirm-and-generate', {
      template_id: templateId,
      confirmed_content: confirmedContent,
      output_filename: outputFilename
    });
    return response.data;
  }
};

export default templatePptAPI;
