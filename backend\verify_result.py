#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from pathlib import Path
from pptx import Presentation

# 添加路径
sys.path.insert(0, str(Path(__file__).parent))

def verify_ppt(file_path):
    """验证PPT文件"""
    try:
        print(f"\n🔍 验证文件: {file_path}")
        
        if not Path(file_path).exists():
            print("❌ 文件不存在")
            return False
        
        prs = Presentation(file_path)
        print(f"✅ 文件加载成功")
        print(f"📊 总页数: {len(prs.slides)}")
        print(f"📏 文件大小: {Path(file_path).stat().st_size / 1024 / 1024:.1f} MB")
        
        # 检查前3页的内容
        for i, slide in enumerate(prs.slides[:3]):
            print(f"\n--- 第{i+1}页 ---")
            text_shapes = [shape for shape in slide.shapes if hasattr(shape, 'text') and shape.text.strip()]
            print(f"文字形状数量: {len(text_shapes)}")
            
            for j, shape in enumerate(text_shapes[:3]):
                text_content = shape.text.strip()
                text_preview = text_content[:80].replace('\n', ' ') if text_content else '(空)'
                print(f"  文字{j+1}: {text_preview}")
                
                # 检查问题
                if "{{" in text_content and "}}" in text_content:
                    print(f"    ⚠️  发现占位符未替换: {text_content}")
                
                if any(keyword in text_content for keyword in ["主讲人", "AiPPT", "PowerPoint design"]):
                    print(f"    ⚠️  发现原始文字残留: {text_content[:50]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    print("🔍 开始验证生成结果...")
    
    # 验证最新生成的文件
    files_to_check = [
        "uploads/generated/quick_test.pptx",
        "uploads/generated/simple_test.pptx"
    ]
    
    for file_path in files_to_check:
        verify_ppt(file_path)
    
    print("\n✅ 验证完成")

if __name__ == "__main__":
    main()
