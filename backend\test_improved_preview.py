#!/usr/bin/env python3
"""
测试改进后的预览生成
"""

import sys
import os
from pathlib import Path
import io

# 添加当前目录到路径
sys.path.insert(0, '.')

def test_improved_preview():
    """测试改进后的预览生成"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        from pptx import Presentation
        
        # 找到模板文件
        template_files = list(Path("templates").glob("*.pptx"))
        if not template_files:
            print("❌ 没有找到PPT模板文件")
            return
        
        template_file = template_files[0]
        print(f"测试文件: {template_file}")
        
        # 加载PPT
        prs = Presentation(str(template_file))
        print(f"✅ PPT加载成功，{len(prs.slides)} 张幻灯片")
        
        if len(prs.slides) > 0:
            slide = prs.slides[0]
            
            # 提取内容（使用改进后的逻辑）
            text_content = []
            images = []
            colors = []
            
            print(f"第一张幻灯片有 {len(slide.shapes)} 个形状")
            
            for i, shape in enumerate(slide.shapes):
                try:
                    # 提取文本
                    if hasattr(shape, 'text_frame') and shape.text_frame.text.strip():
                        text = shape.text_frame.text.strip()
                        text_content.append(text)
                        print(f"形状{i}文本: {text[:30]}...")
                    
                    # 提取图片
                    if hasattr(shape, 'image'):
                        try:
                            image = shape.image
                            images.append({
                                'format': image.ext,
                                'size': len(image.blob),
                                'data': image.blob
                            })
                            print(f"形状{i}图片: {image.ext}, {len(image.blob)} bytes")
                        except Exception as e:
                            print(f"提取图片失败: {e}")
                    
                    # 提取颜色
                    if hasattr(shape, 'fill'):
                        try:
                            fill = shape.fill
                            if hasattr(fill, 'fore_color') and hasattr(fill.fore_color, 'rgb'):
                                colors.append(str(fill.fore_color.rgb))
                        except Exception as e:
                            pass  # 忽略颜色提取错误
                            
                except Exception as e:
                    print(f"处理形状{i}失败: {e}")
            
            print(f"\n提取结果: {len(text_content)}个文本, {len(images)}张图片, {len(colors)}种颜色")
            
            # 生成改进后的预览图片
            img = Image.new('RGB', (400, 300), color='white')
            
            # 如果有图片，使用第一张图片作为背景
            if images:
                try:
                    print("尝试使用PPT中的图片作为背景...")
                    # 使用最大的图片作为背景
                    largest_image = max(images, key=lambda x: x['size'])
                    print(f"使用图片: {largest_image['format']}, {largest_image['size']} bytes")
                    
                    bg_image = Image.open(io.BytesIO(largest_image['data']))
                    print(f"原始图片尺寸: {bg_image.size}")
                    
                    # 调整背景图片大小
                    bg_image = bg_image.resize((400, 300), Image.Resampling.LANCZOS)
                    
                    # 创建半透明遮罩
                    overlay = Image.new('RGBA', (400, 300), (255, 255, 255, 128))
                    bg_image = bg_image.convert('RGBA')
                    img = Image.alpha_composite(bg_image, overlay).convert('RGB')
                    
                    print("✅ 成功使用PPT中的图片作为背景")
                    
                except Exception as e:
                    print(f"❌ 使用PPT图片作为背景失败: {e}")
                    import traceback
                    traceback.print_exc()
                    
                    # 如果失败，使用渐变背景
                    print("使用渐变背景...")
                    for y in range(300):
                        color_value = int(255 - (y * 50 / 300))
                        for x in range(400):
                            img.putpixel((x, y), (color_value, color_value, 255))
            else:
                print("没有找到图片，使用默认背景")
            
            draw = ImageDraw.Draw(img)
            
            # 绘制边框
            draw.rectangle([0, 0, 399, 299], outline='#1976d2', width=2)
            
            # 绘制文本
            try:
                font = ImageFont.truetype("C:/Windows/Fonts/msyh.ttc", 16)
                title_font = ImageFont.truetype("C:/Windows/Fonts/msyh.ttc", 20)
            except:
                font = ImageFont.load_default()
                title_font = ImageFont.load_default()
            
            y_offset = 20
            if text_content:
                title = text_content[0][:30] + "..." if len(text_content[0]) > 30 else text_content[0]
                draw.text((20, y_offset), title, fill='#333333', font=title_font)
                y_offset += 40
                
                for i, text in enumerate(text_content[1:3]):
                    if y_offset > 250:
                        break
                    content = text[:40] + "..." if len(text) > 40 else text
                    draw.text((20, y_offset), content, fill='#666666', font=font)
                    y_offset += 25
            else:
                draw.text((20, 20), "PPT模板预览", fill='#333333', font=title_font)
            
            # 保存测试预览图片
            test_path = "test_improved_preview.png"
            img.save(test_path, 'PNG', quality=85)
            print(f"✅ 改进后的预览图片已保存: {test_path}")
            
            # 检查文件大小
            if os.path.exists(test_path):
                size = os.path.getsize(test_path)
                print(f"文件大小: {size} bytes")
                
                # 与之前的预览图片对比
                old_preview = "test_preview2.png"
                if os.path.exists(old_preview):
                    old_size = os.path.getsize(old_preview)
                    print(f"对比: 旧预览 {old_size} bytes vs 新预览 {size} bytes")
                    if size > old_size * 1.5:
                        print("✅ 新预览图片明显更大，可能包含了背景图片")
                    else:
                        print("❌ 新预览图片大小没有明显变化")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_improved_preview()
