#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from pathlib import Path

# 添加路径
sys.path.insert(0, str(Path(__file__).parent))

def main():
    try:
        print("开始快速测试...")
        
        from app.services.professional_ppt_generator import professional_ppt_generator
        
        template_id = 'template_20250708_221451_adc64662'
        user_request = '人工智能发展趋势'
        output_filename = 'quick_test.pptx'
        
        print(f"模板: {template_id}")
        print(f"需求: {user_request}")
        
        # 使用测试模式（不调用LLM）
        result = professional_ppt_generator.generate_ppt_from_template(
            template_id, user_request, output_filename, test_mode=True
        )
        
        if result.get('success'):
            print("SUCCESS!")
            print(f"文件: {result['file_path']}")
            print(f"大小: {result['file_size']} bytes")
            
            # 显示验证结果
            validation = result.get('validation', {})
            print(f"页数匹配: {validation.get('slides_match', False)}")
            print(f"有原文字: {validation.get('has_original_text', False)}")
            print(f"有占位符: {validation.get('has_placeholder_text', False)}")
        else:
            print("FAILED!")
            print(f"错误: {result.get('error')}")
    
    except Exception as e:
        print(f"异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
