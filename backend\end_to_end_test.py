#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
端到端完整流程测试
测试从模板解析到PPT生成的完整流程
"""

import sys
import json
import time
import logging
from pathlib import Path
from pptx import Presentation

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加路径
sys.path.insert(0, str(Path(__file__).parent))

class EndToEndTester:
    """端到端测试器"""
    
    def __init__(self):
        self.test_results = {
            "step1_parse": False,
            "step2_placeholder": False,
            "step3_description": False,
            "step4_content_generation": False,
            "step5_fill_template": False,
            "validation": {},
            "errors": []
        }
    
    def run_complete_test(self):
        """运行完整测试"""
        print("🚀 开始端到端完整流程测试")
        print("=" * 60)
        
        try:
            # 测试参数
            template_id = 'template_20250708_221451_adc64662'
            user_request = '请生成一个关于人工智能在医疗领域应用的PPT，包括技术背景、应用场景、案例分析和发展前景'
            output_filename = 'end_to_end_test.pptx'
            
            print(f"📋 测试参数:")
            print(f"   模板ID: {template_id}")
            print(f"   用户需求: {user_request}")
            print(f"   输出文件: {output_filename}")
            print()
            
            # 步骤1: 测试模板解析
            self.test_step1_parse_template(template_id)
            
            # 步骤2: 测试占位符创建
            self.test_step2_create_placeholder(template_id)
            
            # 步骤3: 测试内容描述生成
            self.test_step3_content_description()
            
            # 步骤4: 测试内容生成
            self.test_step4_content_generation(user_request)
            
            # 步骤5: 测试完整流程
            self.test_step5_complete_generation(template_id, user_request, output_filename)
            
            # 最终验证
            self.final_validation(output_filename)
            
            # 输出测试报告
            self.print_test_report()
            
        except Exception as e:
            logger.error(f"测试过程中发生异常: {e}")
            self.test_results["errors"].append(f"测试异常: {e}")
            import traceback
            traceback.print_exc()
    
    def test_step1_parse_template(self, template_id):
        """测试步骤1: 模板解析"""
        print("🔍 步骤1: 测试模板解析...")
        
        try:
            from app.services.professional_ppt_generator import professional_ppt_generator
            
            template_path = f"templates/{template_id}.pptx"
            
            # 检查模板文件存在
            if not Path(template_path).exists():
                raise FileNotFoundError(f"模板文件不存在: {template_path}")
            
            # 解析模板
            structure_data = professional_ppt_generator._parse_ppt_content(template_path)
            
            # 验证解析结果
            total_slides = structure_data["template_info"]["total_slides"]
            total_text_elements = sum(len(s["text_elements"]) for s in structure_data["slides"])
            
            print(f"   ✅ 模板解析成功")
            print(f"   📊 总页数: {total_slides}")
            print(f"   📝 文字元素: {total_text_elements}个")
            
            # 检查前3页的解析质量
            for i, slide_data in enumerate(structure_data["slides"][:3]):
                text_count = len(slide_data["text_elements"])
                print(f"   📄 第{i+1}页: {text_count}个文字元素")
                
                for j, text_element in enumerate(slide_data["text_elements"][:2]):
                    print(f"      {text_element['placeholder_id']}: {text_element['text_type']}, {text_element['word_count']}字")
            
            self.test_results["step1_parse"] = True
            self.structure_data = structure_data
            
        except Exception as e:
            logger.error(f"步骤1失败: {e}")
            self.test_results["errors"].append(f"步骤1失败: {e}")
    
    def test_step2_create_placeholder(self, template_id):
        """测试步骤2: 占位符创建"""
        print("\n🔧 步骤2: 测试占位符创建...")
        
        try:
            if not self.test_results["step1_parse"]:
                raise Exception("步骤1未成功，无法进行步骤2")
            
            from app.services.professional_ppt_generator import professional_ppt_generator
            
            template_path = f"templates/{template_id}.pptx"
            placeholder_path = professional_ppt_generator._create_placeholder_template(
                template_path, self.structure_data, template_id
            )
            
            print(f"   ✅ 占位符模板创建成功")
            print(f"   📁 文件路径: {placeholder_path}")
            
            # 验证占位符模板
            prs = Presentation(placeholder_path)
            placeholder_count = 0
            
            for slide in prs.slides:
                for shape in slide.shapes:
                    if hasattr(shape, 'text') and '{{' in shape.text and '}}' in shape.text:
                        placeholder_count += 1
            
            print(f"   🏷️  占位符数量: {placeholder_count}")
            
            self.test_results["step2_placeholder"] = True
            self.placeholder_path = placeholder_path
            
        except Exception as e:
            logger.error(f"步骤2失败: {e}")
            self.test_results["errors"].append(f"步骤2失败: {e}")
    
    def test_step3_content_description(self):
        """测试步骤3: 内容描述生成"""
        print("\n📋 步骤3: 测试内容描述生成...")
        
        try:
            if not self.test_results["step1_parse"]:
                raise Exception("步骤1未成功，无法进行步骤3")
            
            from app.services.professional_ppt_generator import professional_ppt_generator
            
            content_requirements = professional_ppt_generator._generate_content_description(self.structure_data)
            
            print(f"   ✅ 内容描述生成成功")
            print(f"   📊 需要生成: {content_requirements['total_slides']}页")
            print(f"   📝 页面要求: {len(content_requirements['slides_requirements'])}个")
            
            # 显示前2页的要求
            for i, slide_req in enumerate(content_requirements["slides_requirements"][:2]):
                print(f"   📄 第{i+1}页要求:")
                for text_req in slide_req["text_requirements"][:3]:
                    word_range = text_req["word_range"]
                    print(f"      {text_req['placeholder_id']}: {text_req['text_type']}, {word_range['min']}-{word_range['max']}字")
            
            self.test_results["step3_description"] = True
            self.content_requirements = content_requirements
            
        except Exception as e:
            logger.error(f"步骤3失败: {e}")
            self.test_results["errors"].append(f"步骤3失败: {e}")
    
    def test_step4_content_generation(self, user_request):
        """测试步骤4: 内容生成"""
        print("\n🤖 步骤4: 测试内容生成...")
        
        try:
            if not self.test_results["step3_description"]:
                raise Exception("步骤3未成功，无法进行步骤4")
            
            from app.services.professional_ppt_generator import professional_ppt_generator
            
            # 使用后备内容生成（避免LLM调用问题）
            generated_content = professional_ppt_generator._generate_fallback_content(
                self.content_requirements, user_request
            )
            
            print(f"   ✅ 内容生成成功")
            print(f"   📊 生成页数: {len(generated_content['slides'])}")
            
            # 验证生成内容的结构
            for i, slide_content in enumerate(generated_content["slides"][:2]):
                content_dict = slide_content["content"]
                print(f"   📄 第{i+1}页内容: {len(content_dict)}个文字元素")
                
                for j, (placeholder_id, content) in enumerate(list(content_dict.items())[:2]):
                    content_preview = content[:30].replace('\n', ' ')
                    print(f"      {placeholder_id}: {content_preview}...")
            
            self.test_results["step4_content_generation"] = True
            self.generated_content = generated_content
            
        except Exception as e:
            logger.error(f"步骤4失败: {e}")
            self.test_results["errors"].append(f"步骤4失败: {e}")
    
    def test_step5_complete_generation(self, template_id, user_request, output_filename):
        """测试步骤5: 完整生成流程"""
        print("\n🎯 步骤5: 测试完整生成流程...")
        
        try:
            from app.services.professional_ppt_generator import professional_ppt_generator
            
            # 使用测试模式进行完整生成
            result = professional_ppt_generator.generate_ppt_from_template(
                template_id, user_request, output_filename, test_mode=True
            )
            
            if result.get('success'):
                print(f"   ✅ 完整流程生成成功")
                print(f"   📁 文件路径: {result['file_path']}")
                print(f"   📏 文件大小: {result['file_size']/1024/1024:.1f} MB")
                print(f"   📊 幻灯片数: {result['slides_count']}")
                
                self.test_results["step5_fill_template"] = True
                self.final_result = result
            else:
                raise Exception(f"生成失败: {result.get('error')}")
            
        except Exception as e:
            logger.error(f"步骤5失败: {e}")
            self.test_results["errors"].append(f"步骤5失败: {e}")
    
    def final_validation(self, output_filename):
        """最终验证"""
        print("\n🔍 最终验证...")
        
        try:
            if not self.test_results["step5_fill_template"]:
                raise Exception("步骤5未成功，无法进行最终验证")
            
            file_path = f"uploads/generated/{output_filename}"
            
            if not Path(file_path).exists():
                raise FileNotFoundError(f"生成的文件不存在: {file_path}")
            
            # 加载并验证PPT
            prs = Presentation(file_path)
            
            validation_result = {
                "file_exists": True,
                "total_slides": len(prs.slides),
                "file_size_mb": Path(file_path).stat().st_size / 1024 / 1024,
                "has_original_text": False,
                "has_placeholder_text": False,
                "text_samples": []
            }
            
            # 检查前3页的内容质量
            for i, slide in enumerate(prs.slides[:3]):
                text_shapes = [shape for shape in slide.shapes if hasattr(shape, 'text') and shape.text.strip()]
                
                for shape in text_shapes[:2]:
                    text_content = shape.text.strip()
                    validation_result["text_samples"].append({
                        "slide": i+1,
                        "text": text_content[:100]
                    })
                    
                    # 检查问题
                    if "{{" in text_content and "}}" in text_content:
                        validation_result["has_placeholder_text"] = True
                    
                    if any(keyword in text_content for keyword in ["主讲人", "AiPPT", "PowerPoint design"]):
                        validation_result["has_original_text"] = True
            
            self.test_results["validation"] = validation_result
            
            print(f"   ✅ 文件验证成功")
            print(f"   📊 页数: {validation_result['total_slides']}")
            print(f"   📏 大小: {validation_result['file_size_mb']:.1f} MB")
            print(f"   ⚠️  原文字残留: {validation_result['has_original_text']}")
            print(f"   ⚠️  占位符残留: {validation_result['has_placeholder_text']}")
            
        except Exception as e:
            logger.error(f"最终验证失败: {e}")
            self.test_results["errors"].append(f"最终验证失败: {e}")
    
    def print_test_report(self):
        """打印测试报告"""
        print("\n" + "=" * 60)
        print("📊 测试报告")
        print("=" * 60)
        
        steps = [
            ("步骤1: 模板解析", "step1_parse"),
            ("步骤2: 占位符创建", "step2_placeholder"),
            ("步骤3: 内容描述生成", "step3_description"),
            ("步骤4: 内容生成", "step4_content_generation"),
            ("步骤5: 完整流程", "step5_fill_template")
        ]
        
        for step_name, step_key in steps:
            status = "✅ 通过" if self.test_results[step_key] else "❌ 失败"
            print(f"{step_name}: {status}")
        
        # 验证结果
        validation = self.test_results.get("validation", {})
        if validation:
            print(f"\n🔍 验证结果:")
            print(f"   文件大小: {validation.get('file_size_mb', 0):.1f} MB")
            print(f"   原文字残留: {'❌ 有' if validation.get('has_original_text') else '✅ 无'}")
            print(f"   占位符残留: {'❌ 有' if validation.get('has_placeholder_text') else '✅ 无'}")
        
        # 错误列表
        if self.test_results["errors"]:
            print(f"\n❌ 发现的问题:")
            for i, error in enumerate(self.test_results["errors"], 1):
                print(f"   {i}. {error}")
        
        # 总体评估
        total_steps = len(steps)
        passed_steps = sum(1 for _, key in steps if self.test_results[key])
        success_rate = passed_steps / total_steps * 100
        
        print(f"\n🎯 总体评估:")
        print(f"   成功率: {success_rate:.1f}% ({passed_steps}/{total_steps})")
        
        if success_rate >= 80:
            print("   ✅ 测试基本通过")
        else:
            print("   ❌ 测试需要改进")

def main():
    """主函数"""
    tester = EndToEndTester()
    tester.run_complete_test()

if __name__ == "__main__":
    main()
