import sys
sys.path.insert(0, './backend')

try:
    from app.services.ppt_template_analyzer import ppt_template_analyzer
    from app.services.ppt_template_processor import ppt_template_processor
    from app.services.template_based_content_generator import template_based_content_generator
    from app.services.hybrid_ppt_engine import hybrid_ppt_engine
    
    template_id = 'template_20250708_221451_adc64662'
    user_request = '请生成一个关于人工智能在教育领域应用的PPT，包括背景介绍、技术方案、应用案例和未来展望等内容'
    
    print('🔄 开始专业模板化PPT生成测试...')
    print(f'模板ID: {template_id}')
    print(f'用户需求: {user_request}')
    print()
    
    # 步骤1: 分析模板
    print('📊 步骤1: 分析模板结构...')
    template_path = f'backend/templates/{template_id}.pptx'
    analysis_result = ppt_template_analyzer.analyze_ppt_template(template_path)
    
    print(f'✅ 模板分析完成:')
    print(f'   - 总页数: {analysis_result["total_slides"]}')
    print(f'   - 文字元素: {analysis_result["text_structure_summary"]["total_text_elements"]}个')
    print(f'   - 文字类型分布: {analysis_result["text_structure_summary"]["text_types_distribution"]}')
    print()
    
    # 步骤2: 获取生成要求
    print('📋 步骤2: 获取模板生成要求...')
    template_requirements = analysis_result['generation_requirements']
    print(f'✅ 生成要求获取完成:')
    print(f'   - 需要生成: {template_requirements["total_slides_needed"]}页')
    print(f'   - 每页要求: {len(template_requirements["slides_requirements"])}个页面规范')
    print()
    
    # 步骤3: 生成内容
    print('🤖 步骤3: 基于模板要求生成内容...')
    generated_content = template_based_content_generator.generate_content_for_template(
        user_request, template_requirements, analysis_result
    )
    
    print(f'✅ 内容生成完成:')
    print(f'   - 生成页数: {len(generated_content["slides"])}')
    print(f'   - PPT标题: {generated_content.get("title", "未设置")}')
    print()
    
    # 步骤4: 创建最终PPT
    print('📄 步骤4: 创建最终PPT...')
    output_filename = 'test_professional_template.pptx'
    final_result = ppt_template_processor.generate_ppt_from_content(
        template_id, generated_content, analysis_result, output_filename
    )
    
    if final_result.get('success'):
        print(f'✅ PPT生成成功!')
        print(f'   - 文件路径: {final_result["file_path"]}')
        print(f'   - 文件大小: {final_result["file_size"]/1024/1024:.1f} MB')
        print(f'   - 幻灯片数: {final_result["slides_count"]}')
        print(f'   - 引擎类型: {final_result["engine_type"]}')
    else:
        print(f'❌ PPT生成失败: {final_result.get("error", "未知错误")}')

except Exception as e:
    print(f'❌ 测试失败: {e}')
    import traceback
    traceback.print_exc()
