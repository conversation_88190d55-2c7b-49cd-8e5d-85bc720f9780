"""
统一配置文件 - 从根目录config.json读取所有配置
"""
import os
import json
from pathlib import Path
from dotenv import load_dotenv

load_dotenv()

# 读取根目录的配置文件
config_path = Path(__file__).parent.parent / "config.json"

# 默认配置
DEFAULT_CONFIG = {
    "app": {
        "name": "PPT生成助手",
        "version": "1.0.0",
        "debug": False
    },
    "ports": {
        "backend": 9527,
        "frontend": 9528,
        "other_services": 9529
    },
    "urls": {
        "backend": "http://localhost:9527",
        "frontend": "http://localhost:9528",
        "other_services": "http://localhost:9529"
    },
    "llm": {
        "api_key": "sk-2h1RwdRjqYc6FosI3aus",
        "model": "deepseek-v3-0324",
        "api_base": "http://*************/gateway/ai-service/v1",
        "timeout": 180,
        "max_tokens": 4000,
        "temperature": 0.7
    },
    "paths": {
        "base_dir": ".",
        "templates_dir": "templates",
        "template_analysis_dir": "template_analysis",
        "upload_dir": "uploads",
        "generated_dir": "generated",
        "static_dir": "static",
        "temp_processing_dir": "temp_processing"
    },
    "cors": {
        "allowed_origins": [
            "http://localhost:9528",
            "http://127.0.0.1:9528",
            "http://localhost:3000",
            "http://localhost:5173",
            "http://127.0.0.1:3000",
            "http://127.0.0.1:5173"
        ]
    }
}

# 尝试读取配置文件
try:
    if config_path.exists():
        with open(config_path, 'r', encoding='utf-8') as f:
            CONFIG = json.load(f)
        # 合并默认配置，确保所有必要的键都存在
        for key, value in DEFAULT_CONFIG.items():
            if key not in CONFIG:
                CONFIG[key] = value
    else:
        CONFIG = DEFAULT_CONFIG
except Exception as e:
    print(f"读取配置文件失败: {e}")
    CONFIG = DEFAULT_CONFIG

# 为了向后兼容，保留PORT_CONFIG
PORT_CONFIG = CONFIG

class Settings:
    # 应用配置
    APP_NAME: str = CONFIG["app"]["name"]
    APP_VERSION: str = CONFIG["app"]["version"]
    DEBUG: bool = CONFIG["app"].get("debug", False)

    # 文件配置
    MAX_FILE_SIZE: int = CONFIG.get("file", {}).get("max_file_size", 50 * 1024 * 1024)  # 50MB
    ALLOWED_EXTENSIONS = set(CONFIG.get("file", {}).get("allowed_extensions", ['.pptx', '.ppt']))

    # 路径配置 - 从统一配置读取
    BASE_DIR: str = CONFIG["paths"]["base_dir"]
    TEMPLATES_DIR: str = CONFIG["paths"]["templates_dir"]
    TEMPLATE_ANALYSIS_DIR: str = CONFIG["paths"]["template_analysis_dir"]
    UPLOAD_DIR: str = CONFIG["paths"]["upload_dir"]
    GENERATED_DIR: str = CONFIG["paths"]["generated_dir"]
    STATIC_DIR: str = CONFIG["paths"]["static_dir"]
    TEMP_PROCESSING_DIR: str = CONFIG["paths"]["temp_processing_dir"]

    # 兼容旧路径配置
    TEMPLATES_UPLOADED_DIR: str = "templates/uploaded"
    TEMPLATES_ANALYZED_DIR: str = "templates/analyzed"
    TEMPLATES_PREVIEWS_DIR: str = "templates/previews"
    TEMPLATES_SCHEMAS_DIR: str = "templates/schemas"
    TEMPLATES_METADATA_FILE: str = "templates/templates_metadata.json"
    UPLOADS_TEMPLATES_DIR: str = "uploads/templates"
    UPLOADS_STATIC_DIR: str = "uploads/static"
    UPLOADS_GENERATED_DIR: str = "uploads/generated"
    GENERATED_STATIC_DIR: str = "generated/static"
    SERVICES_DIR: str = "services"

    # 端口配置
    BACKEND_PORT: int = CONFIG["ports"]["backend"]
    FRONTEND_PORT: int = CONFIG["ports"]["frontend"]
    OTHER_SERVICES_PORT: int = CONFIG["ports"]["other_services"]

    # CORS配置
    ALLOWED_ORIGINS: list = CONFIG["cors"]["allowed_origins"]

    # PPT生成配置
    DEFAULT_SLIDE_WIDTH: int = CONFIG.get("ppt", {}).get("default_slide_width", 10)
    DEFAULT_SLIDE_HEIGHT: int = CONFIG.get("ppt", {}).get("default_slide_height", 7.5)
    MAX_SLIDES: int = CONFIG.get("ppt", {}).get("max_slides", 50)

    # AI配置
    MAX_TOKENS: int = CONFIG["llm"]["max_tokens"]
    TEMPERATURE: float = CONFIG["llm"]["temperature"]

    # 路径工具方法
    @classmethod
    def get_template_structure_file(cls, template_id: str) -> str:
        """获取模板结构化JSON文件路径"""
        return f"{cls.TEMPLATE_ANALYSIS_DIR}/{template_id}_structure.json"

    @classmethod
    def get_template_analysis_file(cls, template_id: str) -> str:
        """获取模板分析文件路径"""
        return f"{cls.TEMPLATE_ANALYSIS_DIR}/{template_id}_analysis.json"

    @classmethod
    def get_template_file(cls, template_id: str) -> str:
        """获取模板文件路径"""
        return f"{cls.TEMPLATES_DIR}/{template_id}.pptx"

    @classmethod
    def get_template_preview_file(cls, template_id: str) -> str:
        """获取模板预览文件路径"""
        return f"{cls.TEMPLATES_PREVIEWS_DIR}/{template_id}_preview.png"

    @classmethod
    def get_generated_file(cls, filename: str) -> str:
        """获取生成文件路径"""
        return f"{cls.UPLOADS_GENERATED_DIR}/{filename}"

    @classmethod
    def get_temp_processing_file(cls, filename: str) -> str:
        """获取临时处理文件路径"""
        return f"{cls.TEMP_PROCESSING_DIR}/{filename}"

settings = Settings()
