"""
配置文件
"""
import os
import json
from pathlib import Path
from dotenv import load_dotenv

load_dotenv()

# 读取根目录的配置文件
config_path = Path(__file__).parent.parent / "config.json"

if config_path.exists():
    with open(config_path, 'r', encoding='utf-8') as f:
        PORT_CONFIG = json.load(f)
else:
    # 默认端口配置
    PORT_CONFIG = {
        "ports": {
            "backend": 9527,
            "frontend": 9528,
            "other_services": 9529
        },
        "urls": {
            "backend": "http://localhost:9527",
            "frontend": "http://localhost:9528",
            "other_services": "http://localhost:9529"
        }
    }

class Settings:
    # 应用配置
    APP_NAME: str = "PPT生成助手"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = os.getenv("DEBUG", "False").lower() == "true"
    
    # 文件配置
    MAX_FILE_SIZE: int = 50 * 1024 * 1024  # 50MB
    ALLOWED_EXTENSIONS = {'.pptx', '.ppt'}

    # 路径配置 - 统一管理所有路径
    BASE_DIR: str = "."  # 项目根目录

    # 模板相关路径
    TEMPLATES_DIR: str = "templates"
    TEMPLATES_UPLOADED_DIR: str = "templates/uploaded"
    TEMPLATES_ANALYZED_DIR: str = "templates/analyzed"
    TEMPLATES_PREVIEWS_DIR: str = "templates/previews"
    TEMPLATES_SCHEMAS_DIR: str = "templates/schemas"
    TEMPLATES_METADATA_FILE: str = "templates/templates_metadata.json"

    # 模板分析路径（新方案）
    TEMPLATE_ANALYSIS_DIR: str = "template_analysis"

    # 上传路径
    UPLOAD_DIR: str = "uploads"
    UPLOADS_TEMPLATES_DIR: str = "uploads/templates"
    UPLOADS_STATIC_DIR: str = "uploads/static"
    UPLOADS_GENERATED_DIR: str = "uploads/generated"

    # 生成文件路径
    GENERATED_DIR: str = "generated"
    GENERATED_STATIC_DIR: str = "generated/static"

    # 静态文件路径
    STATIC_DIR: str = "static"

    # 临时处理路径
    TEMP_PROCESSING_DIR: str = "temp_processing"

    # 服务相关路径
    SERVICES_DIR: str = "services"
    
    # 端口配置
    BACKEND_PORT: int = PORT_CONFIG["ports"]["backend"]
    FRONTEND_PORT: int = PORT_CONFIG["ports"]["frontend"]
    OTHER_SERVICES_PORT: int = PORT_CONFIG["ports"]["other_services"]

    # CORS配置
    ALLOWED_ORIGINS: list = [
        PORT_CONFIG["urls"]["frontend"],
        f"http://127.0.0.1:{PORT_CONFIG['ports']['frontend']}",
        f"http://localhost:{PORT_CONFIG['ports']['frontend']}",
        "http://localhost:3000",  # 兼容旧配置
        "http://localhost:5173",  # 兼容旧配置
        "http://127.0.0.1:3000",  # 兼容旧配置
        "http://127.0.0.1:5173"   # 兼容旧配置
    ]
    
    # PPT生成配置
    DEFAULT_SLIDE_WIDTH: int = 10  # 英寸
    DEFAULT_SLIDE_HEIGHT: int = 7.5  # 英寸
    MAX_SLIDES: int = 50
    
    # AI配置
    MAX_TOKENS: int = 4000
    TEMPERATURE: float = 0.7

    # 路径工具方法
    @classmethod
    def get_template_structure_file(cls, template_id: str) -> str:
        """获取模板结构化JSON文件路径"""
        return f"{cls.TEMPLATE_ANALYSIS_DIR}/{template_id}_structure.json"

    @classmethod
    def get_template_analysis_file(cls, template_id: str) -> str:
        """获取模板分析文件路径"""
        return f"{cls.TEMPLATE_ANALYSIS_DIR}/{template_id}_analysis.json"

    @classmethod
    def get_template_file(cls, template_id: str) -> str:
        """获取模板文件路径"""
        return f"{cls.TEMPLATES_DIR}/{template_id}.pptx"

    @classmethod
    def get_template_preview_file(cls, template_id: str) -> str:
        """获取模板预览文件路径"""
        return f"{cls.TEMPLATES_PREVIEWS_DIR}/{template_id}_preview.png"

    @classmethod
    def get_generated_file(cls, filename: str) -> str:
        """获取生成文件路径"""
        return f"{cls.UPLOADS_GENERATED_DIR}/{filename}"

    @classmethod
    def get_temp_processing_file(cls, filename: str) -> str:
        """获取临时处理文件路径"""
        return f"{cls.TEMP_PROCESSING_DIR}/{filename}"

settings = Settings()
