"""
在线编辑器API路由
提供PPT内容在线编辑功能
"""

from fastapi import APIRouter, HTTPException
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
from typing import Optional
import logging
import time
import os
import hashlib

logger = logging.getLogger(__name__)
router = APIRouter()

class EditorPreviewRequest(BaseModel):
    """编辑器预览请求"""
    content: str
    template_id: str

class EditorSaveRequest(BaseModel):
    """编辑器保存请求"""
    content: str
    template_id: str

class EditorGenerateRequest(BaseModel):
    """编辑器生成PPT请求"""
    content: str
    template_id: str

# 内存存储编辑内容（生产环境应使用数据库）
editor_sessions = {}

@router.post("/preview")
async def preview_content(request: EditorPreviewRequest):
    """生成编辑内容的预览"""
    try:
        logger.info(f"编辑器预览请求: template_id={request.template_id}, content_length={len(request.content)}")
        
        # 生成预览HTML
        preview_html = generate_preview_html(request.content, request.template_id)
        
        # 生成唯一的预览ID
        content_hash = hashlib.md5(request.content.encode()).hexdigest()[:8]
        timestamp = int(time.time())
        preview_id = f"editor_{request.template_id}_{timestamp}_{content_hash}"
        
        # 存储预览内容
        editor_sessions[preview_id] = {
            'content': request.content,
            'template_id': request.template_id,
            'preview_html': preview_html,
            'created_at': timestamp
        }
        
        return {
            "success": True,
            "preview_url": f"/api/editor/preview/{preview_id}",
            "preview_id": preview_id
        }
        
    except Exception as e:
        logger.error(f"编辑器预览失败: {e}")
        raise HTTPException(status_code=500, detail=f"预览生成失败: {str(e)}")

@router.get("/preview/{preview_id}")
async def get_preview(preview_id: str):
    """获取预览内容"""
    try:
        if preview_id not in editor_sessions:
            # 返回默认预览页面
            default_html = generate_default_preview_html(preview_id)
            return HTMLResponse(content=default_html)
        
        session = editor_sessions[preview_id]
        return HTMLResponse(content=session['preview_html'])
        
    except Exception as e:
        logger.error(f"获取预览失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取预览失败: {str(e)}")

@router.post("/save")
async def save_content(request: EditorSaveRequest):
    """保存编辑内容"""
    try:
        logger.info(f"保存编辑内容: template_id={request.template_id}, content_length={len(request.content)}")
        
        # 生成保存ID
        timestamp = int(time.time())
        save_id = f"save_{request.template_id}_{timestamp}"
        
        # 存储保存的内容
        editor_sessions[save_id] = {
            'content': request.content,
            'template_id': request.template_id,
            'saved_at': timestamp,
            'type': 'saved'
        }
        
        return {
            "success": True,
            "message": "内容保存成功",
            "save_id": save_id,
            "saved_at": timestamp
        }
        
    except Exception as e:
        logger.error(f"保存内容失败: {e}")
        raise HTTPException(status_code=500, detail=f"保存失败: {str(e)}")

@router.post("/generate")
async def generate_ppt(request: EditorGenerateRequest):
    """生成PPT文件"""
    try:
        logger.info(f"生成PPT: template_id={request.template_id}, content_length={len(request.content)}")

        # 使用专业PPT生成器
        from ..services.professional_ppt_generator import ProfessionalPPTGenerator
        import time

        # 创建生成器实例
        generator = ProfessionalPPTGenerator()

        # 生成输出文件名
        timestamp = int(time.time())
        output_filename = f"editor_generated_{timestamp}.pptx"

        # 生成PPT文件
        result = generator.generate_ppt_from_template(
            template_id=request.template_id,
            user_request=request.content,
            output_filename=output_filename
        )

        if result.get("success"):
            return {
                "success": True,
                "message": "PPT生成成功",
                "download_url": result.get("download_url"),
                "file_path": result.get("file_path")
            }
        else:
            raise Exception(result.get("error", "PPT生成失败"))

    except Exception as e:
        logger.error(f"PPT生成失败: {e}")
        raise HTTPException(status_code=500, detail=f"PPT生成失败: {str(e)}")

@router.get("/sessions")
async def list_sessions():
    """列出所有编辑会话"""
    try:
        sessions = []
        for session_id, session_data in editor_sessions.items():
            sessions.append({
                "session_id": session_id,
                "template_id": session_data.get("template_id"),
                "created_at": session_data.get("created_at"),
                "type": session_data.get("type", "preview"),
                "content_length": len(session_data.get("content", ""))
            })
        
        return {
            "success": True,
            "sessions": sessions,
            "total": len(sessions)
        }
        
    except Exception as e:
        logger.error(f"获取会话列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取会话列表失败: {str(e)}")

def generate_preview_html(content: str, template_id: str) -> str:
    """生成预览HTML内容"""
    
    # 将Markdown内容转换为HTML幻灯片
    slides = content.split('---')
    slide_html_list = []
    
    for i, slide_content in enumerate(slides):
        if slide_content.strip():
            # 简单的Markdown到HTML转换
            html_content = markdown_to_html(slide_content.strip())
            slide_html_list.append(f"""
                <div class="slide" id="slide-{i+1}">
                    <div class="slide-content">
                        {html_content}
                    </div>
                </div>
            """)
    
    # 生成完整的HTML页面
    html_template = f"""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>PPT预览 - {template_id}</title>
        <style>
            body {{
                font-family: 'Microsoft YaHei', Arial, sans-serif;
                margin: 0;
                padding: 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
            }}
            
            .presentation {{
                max-width: 1000px;
                margin: 0 auto;
                background: white;
                border-radius: 15px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                overflow: hidden;
            }}
            
            .slide {{
                padding: 60px;
                min-height: 500px;
                border-bottom: 2px solid #f0f0f0;
                display: flex;
                align-items: center;
                justify-content: center;
            }}
            
            .slide:last-child {{
                border-bottom: none;
            }}
            
            .slide-content {{
                width: 100%;
                text-align: center;
            }}
            
            h1 {{
                color: #2c3e50;
                font-size: 2.5em;
                margin-bottom: 20px;
                font-weight: 700;
            }}
            
            h2 {{
                color: #34495e;
                font-size: 2em;
                margin-bottom: 30px;
                font-weight: 600;
            }}
            
            h3 {{
                color: #5a6c7d;
                font-size: 1.5em;
                margin-bottom: 20px;
                font-weight: 500;
            }}
            
            p {{
                color: #6c757d;
                font-size: 1.2em;
                line-height: 1.6;
                margin-bottom: 20px;
            }}
            
            ul, ol {{
                text-align: left;
                max-width: 600px;
                margin: 0 auto;
            }}
            
            li {{
                color: #6c757d;
                font-size: 1.1em;
                line-height: 1.8;
                margin-bottom: 10px;
            }}
            
            .slide:nth-child(even) {{
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            }}
            
            .slide-number {{
                position: absolute;
                bottom: 20px;
                right: 20px;
                background: rgba(0,0,0,0.1);
                padding: 5px 10px;
                border-radius: 15px;
                font-size: 0.9em;
                color: #666;
            }}
            
            code {{
                background: #f8f9fa;
                padding: 2px 6px;
                border-radius: 4px;
                font-family: 'Consolas', monospace;
                color: #e83e8c;
            }}
            
            pre {{
                background: #f8f9fa;
                padding: 20px;
                border-radius: 8px;
                overflow-x: auto;
                text-align: left;
            }}
        </style>
    </head>
    <body>
        <div class="presentation">
            {''.join(slide_html_list)}
        </div>
        
        <script>
            // 添加幻灯片编号
            document.querySelectorAll('.slide').forEach((slide, index) => {{
                const slideNumber = document.createElement('div');
                slideNumber.className = 'slide-number';
                slideNumber.textContent = `${{index + 1}} / ${{document.querySelectorAll('.slide').length}}`;
                slide.style.position = 'relative';
                slide.appendChild(slideNumber);
            }});
        </script>
    </body>
    </html>
    """
    
    return html_template

def markdown_to_html(markdown_content: str) -> str:
    """简单的Markdown到HTML转换"""
    import re
    
    html = markdown_content
    
    # 标题转换
    html = re.sub(r'^# (.*?)$', r'<h1>\1</h1>', html, flags=re.MULTILINE)
    html = re.sub(r'^## (.*?)$', r'<h2>\1</h2>', html, flags=re.MULTILINE)
    html = re.sub(r'^### (.*?)$', r'<h3>\1</h3>', html, flags=re.MULTILINE)
    
    # 列表转换
    lines = html.split('\n')
    in_list = False
    result_lines = []
    
    for line in lines:
        if line.strip().startswith('- '):
            if not in_list:
                result_lines.append('<ul>')
                in_list = True
            result_lines.append(f'<li>{line.strip()[2:]}</li>')
        elif line.strip().startswith('1. ') or re.match(r'^\d+\. ', line.strip()):
            if not in_list:
                result_lines.append('<ol>')
                in_list = True
            content = re.sub(r'^\d+\. ', '', line.strip())
            result_lines.append(f'<li>{content}</li>')
        else:
            if in_list:
                if result_lines and result_lines[-1] != '</ul>' and result_lines[-1] != '</ol>':
                    if any('- ' in prev_line for prev_line in lines[max(0, lines.index(line)-3):lines.index(line)]):
                        result_lines.append('</ul>')
                    else:
                        result_lines.append('</ol>')
                in_list = False
            if line.strip():
                result_lines.append(f'<p>{line.strip()}</p>')
            else:
                result_lines.append('<br>')
    
    if in_list:
        result_lines.append('</ul>')
    
    html = '\n'.join(result_lines)
    
    # 粗体和斜体
    html = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', html)
    html = re.sub(r'\*(.*?)\*', r'<em>\1</em>', html)
    
    # 代码
    html = re.sub(r'`(.*?)`', r'<code>\1</code>', html)
    
    return html

def generate_default_preview_html(preview_id: str) -> str:
    """生成默认预览页面"""
    return f"""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>预览不可用</title>
        <style>
            body {{
                font-family: 'Microsoft YaHei', Arial, sans-serif;
                margin: 0;
                padding: 40px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }}
            .container {{
                background: white;
                border-radius: 15px;
                padding: 40px;
                text-align: center;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            }}
            h1 {{ color: #2c3e50; margin-bottom: 20px; }}
            p {{ color: #6c757d; font-size: 1.1em; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>📝 编辑器预览</h1>
            <p>预览内容将在编辑后显示</p>
            <p>预览ID: {preview_id}</p>
        </div>
    </body>
    </html>
    """
