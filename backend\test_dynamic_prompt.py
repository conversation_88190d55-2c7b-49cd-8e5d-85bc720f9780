#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试动态提示词生成
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

from services.template_manager import template_manager

def test_dynamic_prompt():
    """测试动态提示词生成"""
    try:
        print('🔧 测试动态提示词生成')
        print('=' * 60)
        
        template_id = 'template_20250708_221451_adc64662'
        
        print(f'📋 模板ID: {template_id}')
        print()
        
        # 测试旧系统提示词
        print('📖 测试旧系统提示词（md2pptx）:')
        print('-' * 40)
        
        old_prompt = template_manager.generate_ai_prompt(template_id, use_new_system=False)
        
        if old_prompt.get("success"):
            print('✅ 旧提示词生成成功')
            prompt_text = old_prompt["prompt"]
            print(f'提示词长度: {len(prompt_text)}字符')
            print(f'包含md2pptx: {"md2pptx" in prompt_text}')
            print(f'前200字符: {prompt_text[:200]}...')
        else:
            print(f'❌ 旧提示词生成失败: {old_prompt.get("error")}')
        
        print()
        
        # 测试新系统提示词
        print('🚀 测试新系统提示词（动态）:')
        print('-' * 40)
        
        new_prompt = template_manager.generate_ai_prompt(template_id, use_new_system=True)
        
        if new_prompt.get("success"):
            print('✅ 新提示词生成成功')
            prompt_text = new_prompt["prompt"]
            print(f'提示词长度: {len(prompt_text)}字符')
            print(f'包含md2pptx: {"md2pptx" in prompt_text}')
            print(f'包含占位符: {"PLACEHOLDER" in prompt_text}')
            print(f'包含JSON: {"JSON" in prompt_text}')
            print(f'是否动态: {new_prompt.get("is_dynamic", False)}')
            
            template_info = new_prompt.get("template_info", {})
            print(f'总页数: {template_info.get("total_pages", 0)}')
            print(f'总占位符: {template_info.get("total_placeholders", 0)}')
            
            print(f'\n前500字符: {prompt_text[:500]}...')
            
            return True
        else:
            print(f'❌ 新提示词生成失败: {new_prompt.get("error")}')
            return False
            
    except Exception as e:
        print(f'❌ 测试异常: {e}')
        import traceback
        traceback.print_exc()
        return False

def test_structure_file():
    """检查结构化文件是否存在"""
    try:
        print('\n📁 检查结构化文件:')
        print('-' * 40)
        
        template_id = 'template_20250708_221451_adc64662'
        structure_file = Path("template_analysis") / f"{template_id}_structure.json"
        
        print(f'结构化文件路径: {structure_file}')
        
        if structure_file.exists():
            print('✅ 结构化文件存在')
            
            import json
            with open(structure_file, 'r', encoding='utf-8') as f:
                structure_data = json.load(f)
            
            total_pages = len(structure_data)
            total_placeholders = 0
            for page_data in structure_data.values():
                total_placeholders += len(page_data)
            
            print(f'📄 总页数: {total_pages}')
            print(f'🏷️  总占位符: {total_placeholders}')
            
            # 显示前2页结构
            print('\n📖 前2页结构:')
            for i, (page_key, page_data) in enumerate(list(structure_data.items())[:2]):
                print(f'{page_key}: {len(page_data)}个元素')
                for element_key, element_data in list(page_data.items())[:3]:
                    print(f'  {element_key}: {element_data.get("type")}, {element_data.get("word_limit")}字, {element_data.get("PlaceholderId")}')
                if len(page_data) > 3:
                    print(f'  ... 还有{len(page_data) - 3}个元素')
                print()
            
            return True
        else:
            print('❌ 结构化文件不存在')
            print('请先运行"重新解析模板"功能生成结构化数据')
            return False
            
    except Exception as e:
        print(f'❌ 检查异常: {e}')
        return False

if __name__ == "__main__":
    print('🚀 开始测试动态提示词生成')
    
    # 检查结构化文件
    structure_exists = test_structure_file()
    
    # 测试动态提示词
    if structure_exists:
        success = test_dynamic_prompt()
        
        print(f'\n🎯 测试结果: {"✅ 成功" if success else "❌ 失败"}')
        
        if success:
            print('\n🎉 动态提示词生成功能正常!')
            print('现在聊天服务会使用新的动态提示词，包含：')
            print('1. 模板的实际页面结构')
            print('2. 每个占位符的详细信息')
            print('3. JSON格式的生成要求')
            print('4. 不再有md2pptx语法要求')
        else:
            print('\n❌ 动态提示词生成需要调试')
    else:
        print('\n⚠️  请先生成结构化数据，然后再测试动态提示词')
        print('在浏览器中点击"重新解析模板"按钮')
