#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from pathlib import Path

# 添加路径
sys.path.insert(0, str(Path(__file__).parent))

def main():
    print("🚀 开始测试新模板系统...")
    
    try:
        # 测试导入
        print("📦 测试导入...")
        from app.services.template_based_ppt_generator import TemplatePPTGenerator
        print("✅ 导入成功")
        
        # 创建实例
        generator = TemplatePPTGenerator()
        print("✅ 实例创建成功")
        
        # 测试模板路径查找
        template_id = 'template_20250708_221451_adc64662'
        template_path = generator._get_template_path(template_id)
        
        if template_path:
            print(f"✅ 找到模板: {template_path}")
            
            # 测试步骤1
            print("📖 测试步骤1: 模板解析...")
            result = generator.step1_parse_template_ppt(template_path)
            
            if result["success"]:
                print(f"✅ 解析成功: {result['total_pages']}页, {result['total_placeholders']}个占位符")
                return True
            else:
                print(f"❌ 解析失败: {result['error']}")
                return False
        else:
            print(f"❌ 模板文件不存在: {template_id}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    print(f"🎯 结果: {'成功' if success else '失败'}")
