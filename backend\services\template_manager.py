"""
PPT模板管理服务
负责模板上传、分析、存储和md2pptx兼容的数据结构映射
"""

import os
import json
import shutil
import hashlib
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import logging

from pptx import Presentation
from pptx.enum.shapes import PP_PLACEHOLDER

logger = logging.getLogger(__name__)


class TemplateManager:
    """PPT模板管理器"""
    
    def __init__(self, templates_dir: str = "templates"):
        self.templates_dir = Path(templates_dir)
        self.templates_dir.mkdir(exist_ok=True)
        
        # 模板元数据存储
        self.metadata_file = self.templates_dir / "templates_metadata.json"
        self.metadata = self._load_metadata()
    
    def _load_metadata(self) -> Dict[str, Any]:
        """加载模板元数据"""
        if self.metadata_file.exists():
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"加载模板元数据失败: {e}")
                return {}
        return {}
    
    def _save_metadata(self):
        """保存模板元数据"""
        try:
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存模板元数据失败: {e}")
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """计算文件哈希值"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def upload_template(
        self,
        file_path: str,
        template_name: str,
        description: str = "",
        category: str = "general",
        tags: str = ""
    ) -> Dict[str, Any]:
        """
        上传PPT模板
        
        Args:
            file_path: 模板文件路径
            template_name: 模板名称
            description: 模板描述
            category: 模板分类
            
        Returns:
            上传结果
        """
        try:
            # 验证文件存在
            if not os.path.exists(file_path):
                return {"success": False, "error": "模板文件不存在"}
            
            # 验证文件格式
            if not file_path.lower().endswith(('.pptx', '.pptm')):
                return {"success": False, "error": "不支持的文件格式，请使用.pptx或.pptm文件"}
            
            # 计算文件哈希
            file_hash = self._calculate_file_hash(file_path)
            
            # 检查是否已存在相同文件
            for template_id, template_info in self.metadata.items():
                if template_info.get("file_hash") == file_hash:
                    return {
                        "success": False, 
                        "error": f"相同的模板文件已存在: {template_info['name']}"
                    }
            
            # 生成模板ID
            template_id = f"template_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{file_hash[:8]}"
            
            # 复制文件到模板目录
            file_extension = Path(file_path).suffix
            template_file_path = self.templates_dir / f"{template_id}{file_extension}"
            shutil.copy2(file_path, template_file_path)
            
            # 分析模板结构
            analysis_result = self.analyze_template(str(template_file_path))
            
            if not analysis_result["success"]:
                # 如果分析失败，删除已复制的文件
                if template_file_path.exists():
                    template_file_path.unlink()
                return analysis_result
            
            # 解析标签
            tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()] if tags else []

            # 保存模板元数据
            template_metadata = {
                "id": template_id,
                "name": template_name,
                "description": description,
                "category": category,
                "tags": tag_list,
                "file_path": str(template_file_path),
                "file_hash": file_hash,
                "upload_time": datetime.now().isoformat(),
                "file_size": os.path.getsize(template_file_path),
                "analysis": analysis_result["analysis"]
            }
            
            self.metadata[template_id] = template_metadata
            self._save_metadata()
            
            return {
                "success": True,
                "template_id": template_id,
                "metadata": template_metadata
            }
            
        except Exception as e:
            logger.error(f"上传模板失败: {e}")
            return {"success": False, "error": f"上传过程中发生错误: {str(e)}"}
    
    def analyze_template(self, template_path: str) -> Dict[str, Any]:
        """
        分析PPT模板结构，生成md2pptx兼容的数据结构映射

        Args:
            template_path: 模板文件路径

        Returns:
            分析结果
        """
        try:
            logger.info(f"开始分析模板: {template_path}")

            # 添加超时保护（Windows兼容）
            import threading
            import time

            prs = None
            error_occurred = False

            def load_presentation():
                nonlocal prs, error_occurred
                try:
                    prs = Presentation(template_path)
                except Exception as e:
                    logger.error(f"加载PPT文件失败: {e}")
                    error_occurred = True

            # 在单独线程中加载，避免卡死
            load_thread = threading.Thread(target=load_presentation)
            load_thread.daemon = True
            load_thread.start()
            load_thread.join(timeout=30)  # 30秒超时

            if load_thread.is_alive() or error_occurred or prs is None:
                if load_thread.is_alive():
                    logger.error("模板分析超时，使用默认分析结果")
                else:
                    logger.error("模板加载失败，使用默认分析结果")
                return self._create_default_analysis()

            analysis = {
                "slide_layouts": [],
                "theme_colors": {},
                "fonts": {},
                "slide_size": {
                    "width": getattr(prs, 'slide_width', 9144000),
                    "height": getattr(prs, 'slide_height', 6858000)
                },
                "md2pptx_mapping": {},
                "recommended_metadata": {}
            }
            
            # 分析幻灯片布局
            logger.info(f"开始分析模板布局，总数: {len(prs.slide_layouts)}")

            # 获取母版幻灯片
            slide_master = prs.slide_master

            for i, layout in enumerate(slide_master.slide_layouts):
                try:
                    layout_info = {
                        "index": i,
                        "name": layout.name or f"布局_{i+1}",
                        "placeholders": []
                    }

                    logger.debug(f"分析布局 {i}: {layout.name}")

                    # 分析占位符
                    for j, placeholder in enumerate(layout.placeholders):
                        try:
                            placeholder_info = {
                                "idx": placeholder.placeholder_format.idx,
                                "type": str(placeholder.placeholder_format.type),
                                "name": placeholder.name if hasattr(placeholder, 'name') else f"占位符_{j+1}",
                                "left": placeholder.left,
                                "top": placeholder.top,
                                "width": placeholder.width,
                                "height": placeholder.height
                            }
                            layout_info["placeholders"].append(placeholder_info)
                        except Exception as e:
                            logger.warning(f"分析占位符 {j} 失败: {e}")

                    analysis["slide_layouts"].append(layout_info)
                    logger.debug(f"布局 {i} 分析完成，{len(layout_info['placeholders'])} 个占位符")

                except Exception as e:
                    logger.error(f"分析布局 {i} 失败: {e}")

            logger.info(f"布局分析完成，共 {len(analysis['slide_layouts'])} 个布局")

            # 分析主题颜色
            try:
                theme_colors = self._extract_theme_colors(prs)
                analysis["theme_colors"] = theme_colors
                logger.debug(f"主题颜色分析完成，{len(theme_colors)} 个颜色")
            except Exception as e:
                logger.warning(f"主题颜色分析失败: {e}")

            # 分析字体信息
            try:
                fonts = self._extract_fonts(prs)
                analysis["fonts"] = fonts
                logger.debug(f"字体分析完成，{len(fonts)} 个字体")
            except Exception as e:
                logger.warning(f"字体分析失败: {e}")

            # 生成md2pptx映射配置
            md2pptx_mapping = self._generate_md2pptx_mapping(analysis)
            analysis["md2pptx_mapping"] = md2pptx_mapping

            # 生成推荐的md2pptx元数据配置
            recommended_metadata = self._generate_recommended_metadata(analysis)
            analysis["recommended_metadata"] = recommended_metadata

            # 生成预览图片
            try:
                preview_path = None
                slide_previews = []

                # 优先尝试LibreOffice
                try:
                    preview_path = self._generate_preview_image_with_libreoffice(template_path)
                    if preview_path:
                        logger.info("✅ LibreOffice预览生成成功")
                except Exception as e:
                    logger.debug(f"LibreOffice预览生成失败: {e}")

                # 生成所有幻灯片预览
                try:
                    slide_previews = self._generate_all_slides_preview(prs, template_path)
                    if slide_previews:
                        analysis["slide_previews"] = slide_previews
                        logger.info(f"生成了 {len(slide_previews)} 张幻灯片预览")
                except Exception as e:
                    logger.warning(f"生成幻灯片预览失败: {e}")

                # 如果LibreOffice失败，使用python-pptx方法生成封面
                if not preview_path:
                    logger.info("使用python-pptx方法生成封面预览")
                    preview_path = self._generate_preview_image(prs, template_path)

                if preview_path:
                    analysis["preview_image"] = preview_path
                    logger.info(f"封面预览图片生成成功: {preview_path}")

                if not preview_path and not slide_previews:
                    logger.warning("所有预览生成方法都失败了")

            except Exception as e:
                logger.error(f"预览图片生成异常: {e}")
                import traceback
                traceback.print_exc()

            # 验证分析结果的完整性
            if self._is_analysis_incomplete(analysis):
                logger.warning("分析结果不完整，使用默认分析结果")
                default_result = self._create_default_analysis()

                # 保留生成的预览图片和幻灯片预览
                if analysis.get("preview_image"):
                    default_result["analysis"]["preview_image"] = analysis["preview_image"]
                    logger.info("保留了生成的预览图片")

                if analysis.get("slide_previews"):
                    default_result["analysis"]["slide_previews"] = analysis["slide_previews"]
                    logger.info(f"保留了 {len(analysis['slide_previews'])} 张幻灯片预览")

                return default_result

            return {
                "success": True,
                "analysis": analysis
            }
            
        except Exception as e:
            logger.error(f"分析模板失败: {e}")
            return {
                "success": False,
                "error": f"模板分析失败: {str(e)}"
            }
    
    def _generate_md2pptx_mapping(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成md2pptx兼容的映射配置
        
        Args:
            analysis: 模板分析结果
            
        Returns:
            md2pptx映射配置
        """
        mapping = {
            "TitleSlideLayout": None,
            "SectionSlideLayout": None,
            "TitleOnlyLayout": None,
            "BlankLayout": None,
            "ContentSlideLayout": None
        }
        
        # 根据布局名称和占位符类型推断布局用途
        for layout in analysis["slide_layouts"]:
            layout_name = layout["name"].lower()
            placeholders = layout["placeholders"]
            
            # 标题幻灯片布局
            if "title" in layout_name and ("subtitle" in layout_name or len(placeholders) >= 2):
                mapping["TitleSlideLayout"] = layout["index"]
            
            # 章节幻灯片布局
            elif "section" in layout_name or "divider" in layout_name:
                mapping["SectionSlideLayout"] = layout["index"]
            
            # 仅标题布局
            elif "title only" in layout_name or (len(placeholders) == 1 and "title" in str(placeholders[0]["type"])):
                mapping["TitleOnlyLayout"] = layout["index"]
            
            # 空白布局
            elif "blank" in layout_name or len(placeholders) == 0:
                mapping["BlankLayout"] = layout["index"]
            
            # 内容布局
            elif "content" in layout_name or len(placeholders) >= 2:
                if mapping["ContentSlideLayout"] is None:  # 使用第一个找到的内容布局
                    mapping["ContentSlideLayout"] = layout["index"]
        
        # 如果没有找到特定布局，使用默认值
        if mapping["TitleSlideLayout"] is None and len(analysis["slide_layouts"]) > 0:
            mapping["TitleSlideLayout"] = 0
        
        if mapping["ContentSlideLayout"] is None and len(analysis["slide_layouts"]) > 1:
            mapping["ContentSlideLayout"] = 1
        
        return mapping
    
    def _generate_recommended_metadata(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成推荐的md2pptx元数据配置
        
        Args:
            analysis: 模板分析结果
            
        Returns:
            推荐的元数据配置
        """
        # 基于幻灯片尺寸推荐字体大小
        slide_width = analysis["slide_size"]["width"]
        slide_height = analysis["slide_size"]["height"]
        
        # 标准16:9比例的推荐配置
        if slide_width / slide_height > 1.7:  # 宽屏格式
            recommended = {
                "pageTitleSize": 28,
                "pageSubtitleSize": 20,
                "sectionTitleSize": 32,
                "baseTextSize": 18,
                "baseTextDecrement": 2
            }
        else:  # 4:3格式
            recommended = {
                "pageTitleSize": 24,
                "pageSubtitleSize": 18,
                "sectionTitleSize": 28,
                "baseTextSize": 16,
                "baseTextDecrement": 2
            }
        
        # 添加其他推荐配置
        recommended.update({
            "numbers": "no",
            "compactTables": 16,
            "marginBase": 0.5
        })
        
        return recommended
    
    def get_template_list(self) -> List[Dict[str, Any]]:
        """获取所有模板列表"""
        templates = []
        for template_id, template_info in self.metadata.items():
            # 只返回基本信息，不包含详细分析数据
            tags = template_info.get("tags", [])
            logger.debug(f"Template {template_id} tags: {tags}")
            basic_info = {
                "id": template_id,
                "name": template_info["name"],
                "description": template_info["description"],
                "category": template_info["category"],
                "tags": tags,
                "upload_time": template_info["upload_time"],
                "file_size": template_info["file_size"],
                "status": "ready"  # 添加status字段，所有现有模板都标记为ready
            }
            templates.append(basic_info)

        logger.debug(f"Returning {len(templates)} templates")
        return sorted(templates, key=lambda x: x["upload_time"], reverse=True)

    def get_template_details(self, template_id: str) -> Optional[Dict[str, Any]]:
        """获取模板详细信息"""
        return self.metadata.get(template_id)
    
    def delete_template(self, template_id: str) -> Dict[str, Any]:
        """删除模板"""
        try:
            if template_id not in self.metadata:
                return {"success": False, "error": "模板不存在"}
            
            template_info = self.metadata[template_id]
            file_path = Path(template_info["file_path"])
            
            # 删除文件
            if file_path.exists():
                file_path.unlink()
            
            # 删除元数据
            del self.metadata[template_id]
            self._save_metadata()
            
            return {"success": True, "message": "模板删除成功"}
            
        except Exception as e:
            logger.error(f"删除模板失败: {e}")
            return {"success": False, "error": f"删除失败: {str(e)}"}
    
    def generate_ai_prompt(self, template_id: str, use_new_system: bool = False) -> Dict[str, Any]:
        """
        基于模板分析结果生成AI提示词

        Args:
            template_id: 模板ID
            use_new_system: 是否使用新的基于成品PPT模板的系统

        Returns:
            AI提示词生成结果
        """
        try:
            template_info = self.get_template_details(template_id)
            if not template_info:
                return {"success": False, "error": "模板不存在"}

            # 如果使用新系统，生成动态提示词
            if use_new_system:
                return self._generate_dynamic_prompt(template_id, template_info)

            # 否则使用旧的md2pptx提示词（保持兼容性）
            analysis = template_info["analysis"]
            layouts = analysis["slide_layouts"]

            # 构建提示词
            prompt_parts = [
                "请基于以下PPT模板结构生成内容，严格遵循md2pptx语法规范：",
                "",
                "## 模板信息",
                f"- 模板名称: {template_info['name']}",
                f"- 可用布局数量: {len(layouts)}",
                "",
                "## md2pptx语法要求",
                "- 使用 # 创建标题幻灯片",
                "- 使用 ## 创建章节幻灯片",
                "- 使用 ### 创建内容幻灯片",
                "- 支持列表、表格、代码块、funnel图表等",
                "",
                "## 推荐配置",
            ]
            
            # 添加推荐的元数据配置
            for key, value in analysis["recommended_metadata"].items():
                prompt_parts.append(f"- {key}: {value}")
            
            prompt_parts.extend([
                "",
                "请生成符合此模板的演示文稿内容，确保：",
                "1. 内容结构清晰，层次分明",
                "2. 充分利用模板的布局特性",
                "3. 使用适当的md2pptx扩展语法（如funnel、表格等）",
                "4. 保持专业的演示风格"
            ])
            
            return {
                "success": True,
                "prompt": "\n".join(prompt_parts),
                "template_info": {
                    "name": template_info["name"],
                    "layouts_count": len(layouts),
                    "recommended_metadata": analysis["recommended_metadata"]
                }
            }
            
        except Exception as e:
            logger.error(f"生成AI提示词失败: {e}")
            return {"success": False, "error": f"生成提示词失败: {str(e)}"}

    def _generate_dynamic_prompt(self, template_id: str, template_info: Dict) -> Dict[str, Any]:
        """
        生成新系统的动态提示词（基于结构化JSON数据）

        Args:
            template_id: 模板ID
            template_info: 模板信息

        Returns:
            动态提示词生成结果
        """
        try:
            # 检查是否有结构化JSON文件（使用统一配置）
            from config import settings
            structure_file = Path(settings.get_template_structure_file(template_id))

            if not structure_file.exists():
                # 如果没有结构化文件，尝试生成
                logger.info(f"结构化文件不存在，尝试生成: {structure_file}")

                # 这里可以调用新的模板解析器生成结构化数据
                # 暂时返回错误，提示需要先解析模板
                return {
                    "success": False,
                    "error": "模板结构化数据不存在，请先重新解析模板"
                }

            # 读取结构化JSON数据
            import json
            with open(structure_file, 'r', encoding='utf-8') as f:
                structure_data = json.load(f)

            # 统计信息
            total_pages = len(structure_data)
            total_placeholders = 0
            for page_data in structure_data.values():
                total_placeholders += len(page_data)

            # 构建动态提示词
            prompt_parts = [
                "请基于以下PPT模板的实际结构生成内容，严格按照模板的页面布局和占位符要求：",
                "",
                "## 模板基本信息",
                f"- 模板名称: {template_info['name']}",
                f"- 总页数: {total_pages}页",
                f"- 总占位符: {total_placeholders}个",
                "",
                "## 重要要求",
                "1. 必须严格按照模板的实际页面结构生成内容",
                "2. 每个占位符都必须填充合适的内容",
                "3. 内容长度必须符合占位符的字数限制",
                "4. 保持专业的商务演示风格",
                "",
                "## 模板页面结构",
            ]

            # 添加前3页的详细结构示例
            page_count = 0
            for page_key, page_data in structure_data.items():
                if page_count >= 3:  # 只显示前3页作为示例
                    break

                prompt_parts.append(f"### {page_key}")
                for element_key, element_data in page_data.items():
                    element_type = element_data.get("type", "text")
                    word_limit = element_data.get("word_limit", 10)
                    placeholder_id = element_data.get("PlaceholderId", "")

                    prompt_parts.append(f"- {element_key}: {element_type}类型, 限制{word_limit}字, 占位符{placeholder_id}")

                prompt_parts.append("")
                page_count += 1

            if total_pages > 3:
                prompt_parts.append(f"... 还有{total_pages - 3}页，结构类似")
                prompt_parts.append("")

            prompt_parts.extend([
                "## 生成要求",
                "请生成JSON格式的内容，格式如下：",
                "{",
                '  "page_1": {',
                '    "element_name": {',
                '      "type": "title/subtitle/text/list",',
                '      "content": "实际内容文字",',
                '      "word_limit": 数字,',
                '      "PlaceholderId": "PLACEHOLDER_XXX"',
                "    }",
                "  }",
                "}",
                "",
                f"请为所有{total_pages}页生成完整内容，确保每个占位符都有合适的内容。"
            ])

            prompt = "\n".join(prompt_parts)

            return {
                "success": True,
                "prompt": prompt,
                "template_info": {
                    "name": template_info['name'],
                    "total_pages": total_pages,
                    "total_placeholders": total_placeholders,
                    "structure_file": str(structure_file)
                },
                "is_dynamic": True
            }

        except Exception as e:
            logger.error(f"生成动态提示词失败: {e}")
            return {"success": False, "error": f"动态提示词生成失败: {str(e)}"}

    def _extract_theme_colors(self, prs) -> Dict[str, str]:
        """提取主题颜色"""
        colors = {}
        try:
            # 尝试从不同位置获取主题颜色
            slide_master = prs.slide_master

            # 方法1: 尝试从slide_master获取theme
            if hasattr(slide_master, 'theme'):
                theme = slide_master.theme
                if hasattr(theme, 'color_scheme'):
                    color_scheme = theme.color_scheme
                    color_names = [
                        "accent1", "accent2", "accent3", "accent4", "accent5", "accent6",
                        "dark1", "dark2", "light1", "light2", "hyperlink", "followedHyperlink"
                    ]

                    for color_name in color_names:
                        try:
                            if hasattr(color_scheme, color_name):
                                color = getattr(color_scheme, color_name)
                                if hasattr(color, 'rgb'):
                                    colors[color_name] = str(color.rgb)
                        except Exception as e:
                            logger.debug(f"提取颜色 {color_name} 失败: {e}")

            # 方法2: 如果没有主题，尝试从形状中提取常用颜色
            if not colors:
                logger.debug("尝试从形状中提取颜色")
                for layout in slide_master.slide_layouts:
                    for shape in layout.shapes:
                        try:
                            if hasattr(shape, 'fill') and hasattr(shape.fill, 'fore_color'):
                                if hasattr(shape.fill.fore_color, 'rgb'):
                                    color_key = f"shape_color_{len(colors)}"
                                    colors[color_key] = str(shape.fill.fore_color.rgb)
                                    if len(colors) >= 6:  # 限制提取的颜色数量
                                        break
                        except Exception as e:
                            logger.debug(f"从形状提取颜色失败: {e}")
                    if len(colors) >= 6:
                        break

        except Exception as e:
            logger.warning(f"提取主题颜色失败: {e}")

        return colors

    def _extract_fonts(self, prs) -> Dict[str, str]:
        """提取字体信息"""
        fonts = {}
        try:
            slide_master = prs.slide_master

            # 方法1: 尝试从主题获取字体
            if hasattr(slide_master, 'theme'):
                theme = slide_master.theme
                if hasattr(theme, 'font_scheme'):
                    font_scheme = theme.font_scheme

                    if hasattr(font_scheme, 'major_font'):
                        fonts["major_font"] = font_scheme.major_font.latin_typeface or "未知"

                    if hasattr(font_scheme, 'minor_font'):
                        fonts["minor_font"] = font_scheme.minor_font.latin_typeface or "未知"

            # 方法2: 如果没有主题字体，从文本框中提取字体
            if not fonts:
                logger.debug("尝试从文本框中提取字体")
                font_set = set()

                for layout in slide_master.slide_layouts:
                    for shape in layout.shapes:
                        try:
                            if hasattr(shape, 'text_frame'):
                                for paragraph in shape.text_frame.paragraphs:
                                    for run in paragraph.runs:
                                        if hasattr(run.font, 'name') and run.font.name:
                                            font_set.add(run.font.name)
                                            if len(font_set) >= 5:  # 限制字体数量
                                                break
                                    if len(font_set) >= 5:
                                        break
                                if len(font_set) >= 5:
                                    break
                        except Exception as e:
                            logger.debug(f"从文本框提取字体失败: {e}")
                    if len(font_set) >= 5:
                        break

                # 将字体集合转换为字典
                for i, font_name in enumerate(list(font_set)[:5]):
                    fonts[f"font_{i+1}"] = font_name

        except Exception as e:
            logger.warning(f"提取字体信息失败: {e}")

        return fonts

    def _generate_preview_image_with_libreoffice(self, template_path: str) -> Optional[str]:
        """使用LibreOffice生成预览图片"""
        try:
            from .libreoffice_converter import libreoffice_converter

            if not libreoffice_converter.is_available():
                logger.info("LibreOffice不可用，跳过")
                return None

            # 获取模板ID
            template_id = Path(template_path).stem

            # 创建预览目录
            preview_dir = self.templates_dir / "previews"

            # 使用LibreOffice生成预览
            preview_path = libreoffice_converter.generate_preview(
                ppt_path=template_path,
                preview_dir=str(preview_dir),
                template_id=template_id
            )

            if preview_path:
                logger.info(f"LibreOffice预览生成成功: {preview_path}")
                return preview_path
            else:
                logger.warning("LibreOffice预览生成失败")
                return None

        except Exception as e:
            logger.error(f"LibreOffice预览生成异常: {e}")
            return None

    def _generate_all_slides_preview(self, prs, template_path: str) -> List[str]:
        """生成所有幻灯片的预览图片"""
        try:
            from PIL import Image, ImageDraw, ImageFont
            import io

            # 获取模板ID
            template_id = Path(template_path).stem

            # 创建预览目录
            preview_dir = self.templates_dir / "previews" / template_id
            preview_dir.mkdir(parents=True, exist_ok=True)

            preview_paths = []

            logger.info(f"开始生成 {len(prs.slides)} 张幻灯片的预览")

            for slide_index, slide in enumerate(prs.slides):
                try:
                    # 提取当前幻灯片的内容
                    text_content = []
                    images = []

                    for shape in slide.shapes:
                        try:
                            # 提取文本
                            if hasattr(shape, 'text_frame') and shape.text_frame.text.strip():
                                text = shape.text_frame.text.strip()
                                text_content.append(text)

                            # 提取图片
                            if hasattr(shape, 'image'):
                                try:
                                    image = shape.image
                                    images.append({
                                        'format': image.ext,
                                        'size': len(image.blob),
                                        'data': image.blob
                                    })
                                except Exception as e:
                                    logger.debug(f"幻灯片{slide_index}图片提取失败: {e}")
                        except Exception as e:
                            logger.debug(f"幻灯片{slide_index}形状处理失败: {e}")

                    # 生成预览图片 (16:9比例，800x450)
                    img = Image.new('RGB', (800, 450), color='white')

                    # 如果有图片，使用最大的图片作为背景（不加遮罩）
                    if images:
                        try:
                            largest_image = max(images, key=lambda x: x['size'])
                            bg_image = Image.open(io.BytesIO(largest_image['data']))

                            # 保持比例调整背景图片大小
                            bg_image.thumbnail((800, 450), Image.Resampling.LANCZOS)

                            # 居中放置背景图片
                            bg_width, bg_height = bg_image.size
                            x = (800 - bg_width) // 2
                            y = (450 - bg_height) // 2

                            img.paste(bg_image, (x, y))

                        except Exception as e:
                            logger.debug(f"幻灯片{slide_index}背景处理失败: {e}")
                            # 使用渐变背景
                            for y in range(450):
                                color_value = int(240 - (y * 40 / 450))
                                for x in range(800):
                                    img.putpixel((x, y), (color_value, color_value, 255))

                    # 绘制边框
                    draw = ImageDraw.Draw(img)
                    draw.rectangle([0, 0, 799, 449], outline='#cccccc', width=1)

                    # 添加页码
                    try:
                        font = ImageFont.truetype("C:/Windows/Fonts/msyh.ttc", 12)
                    except:
                        font = ImageFont.load_default()

                    page_text = f"{slide_index + 1}/{len(prs.slides)}"
                    draw.text((10, 10), page_text, fill='#666666', font=font)

                    # 保存预览图片
                    preview_filename = f"slide_{slide_index + 1}.png"
                    preview_path = preview_dir / preview_filename
                    img.save(str(preview_path), 'PNG', quality=90)

                    # 添加到预览路径列表
                    relative_path = f"previews/{template_id}/{preview_filename}"
                    preview_paths.append(relative_path)

                    logger.debug(f"幻灯片{slide_index + 1}预览生成成功")

                except Exception as e:
                    logger.warning(f"生成幻灯片{slide_index + 1}预览失败: {e}")
                    continue

            logger.info(f"成功生成 {len(preview_paths)} 张幻灯片预览")
            return preview_paths

        except Exception as e:
            logger.error(f"生成所有幻灯片预览失败: {e}")
            return []

    def _generate_preview_image(self, prs, template_path: str) -> str:
        """生成模板预览图片"""
        try:
            from PIL import Image, ImageDraw, ImageFont
            import io
            import base64

            # 创建预览图片目录
            preview_dir = self.templates_dir / "previews"
            preview_dir.mkdir(exist_ok=True)

            # 获取模板ID
            template_id = Path(template_path).stem
            preview_filename = f"{template_id}_preview.png"
            preview_path = preview_dir / preview_filename

            # 强制重新生成预览图片（用于测试）
            if preview_path.exists():
                logger.info(f"删除现有预览图片: {preview_path}")
                preview_path.unlink()

            # 方法1: 尝试从PPT第一张幻灯片生成预览
            try:
                # 添加超时保护，避免复杂PPT文件卡死
                import signal
                import threading

                def timeout_handler():
                    raise TimeoutError("PPT处理超时")

                # 使用线程超时处理
                slides_count = 0
                try:
                    slides_count = len(prs.slides)
                    logger.info(f"PPT文件有 {slides_count} 张幻灯片")
                except Exception as e:
                    logger.warning(f"获取幻灯片数量失败: {e}")
                    raise TimeoutError("PPT文件处理失败")

                if slides_count > 0:
                    # 由于python-pptx不直接支持图片导出，我们创建一个简单的预览图
                    try:
                        slide = prs.slides[0]
                    except Exception as e:
                        logger.warning(f"获取第一张幻灯片失败: {e}")
                        raise TimeoutError("幻灯片访问失败")

                    # 先提取幻灯片内容
                    text_content = []
                    images = []
                    colors = []

                    logger.info(f"第一张幻灯片有 {len(slide.shapes)} 个形状")

                    for i, shape in enumerate(slide.shapes):
                        try:
                            # 提取文本
                            if hasattr(shape, 'text_frame') and shape.text_frame.text.strip():
                                text = shape.text_frame.text.strip()
                                text_content.append(text)
                                logger.debug(f"形状{i}文本: {text[:50]}...")

                            # 提取图片
                            if hasattr(shape, 'image'):
                                try:
                                    image = shape.image
                                    images.append({
                                        'format': image.ext,
                                        'size': len(image.blob),
                                        'data': image.blob
                                    })
                                    logger.debug(f"形状{i}图片: {image.ext}, {len(image.blob)} bytes")
                                except Exception as e:
                                    logger.debug(f"提取图片失败: {e}")

                            # 提取颜色
                            if hasattr(shape, 'fill'):
                                try:
                                    fill = shape.fill
                                    if hasattr(fill, 'fore_color') and hasattr(fill.fore_color, 'rgb'):
                                        colors.append(str(fill.fore_color.rgb))
                                except Exception as e:
                                    logger.debug(f"提取颜色失败: {e}")

                        except Exception as e:
                            logger.debug(f"处理形状{i}失败: {e}")

                    logger.info(f"提取到: {len(text_content)}个文本, {len(images)}张图片, {len(colors)}种颜色")

                    # 创建预览图片 (400x300)
                    img = Image.new('RGB', (400, 300), color='white')

                    # 如果有图片，尝试使用第一张图片作为背景
                    if images:
                        try:
                            # 使用最大的图片作为背景
                            largest_image = max(images, key=lambda x: x['size'])
                            bg_image = Image.open(io.BytesIO(largest_image['data']))

                            # 调整背景图片大小
                            bg_image = bg_image.resize((400, 300), Image.Resampling.LANCZOS)

                            # 创建半透明遮罩以便文字可读
                            overlay = Image.new('RGBA', (400, 300), (255, 255, 255, 128))
                            bg_image = bg_image.convert('RGBA')
                            img = Image.alpha_composite(bg_image, overlay).convert('RGB')

                            logger.info("✅ 使用PPT中的图片作为预览背景")

                        except Exception as e:
                            logger.warning(f"使用PPT图片作为背景失败: {e}")
                            # 如果失败，使用渐变背景
                            for y in range(300):
                                color_value = int(255 - (y * 50 / 300))
                                for x in range(400):
                                    img.putpixel((x, y), (color_value, color_value, 255))

                    draw = ImageDraw.Draw(img)

                    # 绘制边框
                    draw.rectangle([0, 0, 399, 299], outline='#1976d2', width=2)

                    # 绘制文本预览
                    y_offset = 20
                    try:
                        # 尝试使用Windows系统字体
                        font_paths = [
                            "C:/Windows/Fonts/msyh.ttc",  # 微软雅黑
                            "C:/Windows/Fonts/arial.ttf",  # Arial
                            "C:/Windows/Fonts/calibri.ttf",  # Calibri
                        ]

                        font = None
                        title_font = None

                        for font_path in font_paths:
                            try:
                                if os.path.exists(font_path):
                                    font = ImageFont.truetype(font_path, 14)
                                    title_font = ImageFont.truetype(font_path, 18)
                                    logger.debug(f"使用字体: {font_path}")
                                    break
                            except Exception as e:
                                logger.debug(f"加载字体失败 {font_path}: {e}")
                                continue

                        if not font:
                            # 如果没有找到字体，使用默认字体
                            font = ImageFont.load_default()
                            title_font = ImageFont.load_default()
                            logger.debug("使用默认字体")

                    except Exception as e:
                        logger.warning(f"字体加载失败: {e}")
                        font = ImageFont.load_default()
                        title_font = ImageFont.load_default()

                    # 绘制标题
                    if text_content:
                        title = text_content[0][:30] + "..." if len(text_content[0]) > 30 else text_content[0]
                        draw.text((20, y_offset), title, fill='#333333', font=title_font)
                        y_offset += 40

                    # 绘制其他内容
                    for i, text in enumerate(text_content[1:4]):  # 最多显示3行
                        if y_offset > 250:
                            break
                        content = text[:50] + "..." if len(text) > 50 else text
                        draw.text((20, y_offset), content, fill='#666666', font=font)
                        y_offset += 25

                    # 如果没有文本内容，显示默认信息
                    if not text_content:
                        draw.text((20, 20), "PPT模板预览", fill='#333333', font=title_font)
                        draw.text((20, 60), "点击查看详细结构信息", fill='#666666', font=font)

                    # 保存预览图片
                    img.save(preview_path, 'PNG', quality=85)
                    logger.info(f"预览图片生成成功: {preview_path}")

                    return f"previews/{preview_filename}"

            except (TimeoutError, Exception) as e:
                logger.warning(f"从PPT生成预览失败: {e}")
                # 如果PPT处理失败，直接跳到默认预览生成

            # 方法2: 生成默认预览图片
            img = Image.new('RGB', (400, 300), color='#f5f5f5')
            draw = ImageDraw.Draw(img)

            # 绘制边框
            draw.rectangle([0, 0, 399, 299], outline='#1976d2', width=3)

            # 绘制默认内容
            try:
                # 使用相同的字体加载逻辑
                font_paths = [
                    "C:/Windows/Fonts/msyh.ttc",  # 微软雅黑
                    "C:/Windows/Fonts/arial.ttf",  # Arial
                    "C:/Windows/Fonts/calibri.ttf",  # Calibri
                ]

                font = None
                title_font = None

                for font_path in font_paths:
                    try:
                        if os.path.exists(font_path):
                            font = ImageFont.truetype(font_path, 16)
                            title_font = ImageFont.truetype(font_path, 24)
                            break
                    except:
                        continue

                if not font:
                    title_font = ImageFont.load_default()
                    font = ImageFont.load_default()

            except Exception as e:
                logger.warning(f"默认预览字体加载失败: {e}")
                title_font = ImageFont.load_default()
                font = ImageFont.load_default()

            draw.text((50, 100), "PPT 模板", fill='#1976d2', font=title_font)
            draw.text((50, 140), "• 5种标准布局", fill='#333333', font=font)
            draw.text((50, 165), "• 12种主题颜色", fill='#333333', font=font)
            draw.text((50, 190), "• 5种字体配置", fill='#333333', font=font)
            draw.text((50, 220), "点击预览查看详情", fill='#666666', font=font)

            # 保存预览图片
            img.save(preview_path, 'PNG', quality=85)
            logger.info(f"默认预览图片生成成功: {preview_path}")

            return f"previews/{preview_filename}"

        except Exception as e:
            logger.error(f"生成预览图片失败: {e}")
            return None

    def reanalyze_template(self, template_id: str) -> Dict[str, Any]:
        """重新分析模板"""
        try:
            if template_id not in self.metadata:
                return {"success": False, "error": "模板不存在"}

            template_info = self.metadata[template_id]
            file_path = template_info["file_path"]

            if not os.path.exists(file_path):
                return {"success": False, "error": "模板文件不存在"}

            logger.info(f"开始重新分析模板: {template_id}")

            # 重新分析模板
            analysis_result = self.analyze_template(file_path)

            if analysis_result["success"]:
                # 更新元数据
                template_info["analysis"] = analysis_result["analysis"]
                self.metadata[template_id] = template_info
                self._save_metadata()

                logger.info(f"模板重新分析完成: {template_id}")
                return {
                    "success": True,
                    "template_id": template_id,
                    "analysis": analysis_result["analysis"]
                }
            else:
                return analysis_result

        except Exception as e:
            logger.error(f"重新分析模板失败: {e}")
            return {"success": False, "error": f"重新分析失败: {str(e)}"}

    def delete_template(self, template_id: str) -> Dict[str, Any]:
        """删除模板及相关数据"""
        try:
            if template_id not in self.metadata:
                return {"success": False, "error": "模板不存在"}

            template_info = self.metadata[template_id]

            logger.info(f"开始删除模板: {template_id}")

            # 删除模板文件
            file_path = template_info.get("file_path")
            if file_path and os.path.exists(file_path):
                try:
                    os.remove(file_path)
                    logger.info(f"已删除模板文件: {file_path}")
                except Exception as e:
                    logger.warning(f"删除模板文件失败: {e}")

            # 删除分析结果文件（如果存在）
            analysis_file = self.templates_dir / "analyzed" / f"{template_id}.json"
            if analysis_file.exists():
                try:
                    analysis_file.unlink()
                    logger.info(f"已删除分析文件: {analysis_file}")
                except Exception as e:
                    logger.warning(f"删除分析文件失败: {e}")

            # 删除schema文件（如果存在）
            schema_file = self.templates_dir / "schemas" / f"{template_id}_markdown_schema.json"
            if schema_file.exists():
                try:
                    schema_file.unlink()
                    logger.info(f"已删除schema文件: {schema_file}")
                except Exception as e:
                    logger.warning(f"删除schema文件失败: {e}")

            # 从内存中删除
            del self.metadata[template_id]

            # 保存更新后的元数据
            self._save_metadata()

            logger.info(f"模板删除完成: {template_id}")

            return {
                "success": True,
                "template_id": template_id,
                "message": "模板删除成功"
            }

        except Exception as e:
            logger.error(f"删除模板失败: {e}")
            return {"success": False, "error": f"删除失败: {str(e)}"}

    def _is_analysis_incomplete(self, analysis: Dict[str, Any]) -> bool:
        """检查分析结果是否不完整"""
        try:
            # 检查必要的字段
            slide_layouts = analysis.get("slide_layouts", [])
            theme_colors = analysis.get("theme_colors", {})
            fonts = analysis.get("fonts", {})

            # 如果布局少于2个，或者没有主题颜色和字体，认为不完整
            if len(slide_layouts) < 2:
                logger.debug(f"布局数量不足: {len(slide_layouts)}")
                return True

            if len(theme_colors) == 0 and len(fonts) == 0:
                logger.debug("缺少主题颜色和字体信息")
                return True

            # 检查布局是否有有效的占位符
            valid_layouts = 0
            for layout in slide_layouts:
                if layout.get("placeholders") and len(layout["placeholders"]) > 0:
                    valid_layouts += 1

            if valid_layouts == 0:
                logger.debug("没有有效的布局占位符")
                return True

            return False

        except Exception as e:
            logger.warning(f"检查分析结果完整性失败: {e}")
            return True

    def _create_default_analysis(self) -> Dict[str, Any]:
        """创建默认的分析结果"""
        return {
            "success": True,
            "analysis": {
                "slide_layouts": [
                    {
                        "index": 0,
                        "name": "封面页",
                        "placeholders": [
                            {
                                "idx": 0,
                                "type": "TITLE",
                                "name": "主标题",
                                "left": 914400,
                                "top": 1828800,
                                "width": 7315200,
                                "height": 1143000
                            },
                            {
                                "idx": 1,
                                "type": "SUBTITLE",
                                "name": "副标题",
                                "left": 914400,
                                "top": 3086100,
                                "width": 7315200,
                                "height": 1143000
                            }
                        ]
                    },
                    {
                        "index": 1,
                        "name": "标题和内容",
                        "placeholders": [
                            {
                                "idx": 0,
                                "type": "TITLE",
                                "name": "页面标题",
                                "left": 914400,
                                "top": 457200,
                                "width": 7315200,
                                "height": 1143000
                            },
                            {
                                "idx": 1,
                                "type": "BODY",
                                "name": "正文内容",
                                "left": 914400,
                                "top": 1828800,
                                "width": 7315200,
                                "height": 4114800
                            }
                        ]
                    },
                    {
                        "index": 2,
                        "name": "章节标题",
                        "placeholders": [
                            {
                                "idx": 0,
                                "type": "TITLE",
                                "name": "章节标题",
                                "left": 914400,
                                "top": 2743200,
                                "width": 7315200,
                                "height": 1371600
                            }
                        ]
                    },
                    {
                        "index": 3,
                        "name": "两栏内容",
                        "placeholders": [
                            {
                                "idx": 0,
                                "type": "TITLE",
                                "name": "标题",
                                "left": 914400,
                                "top": 457200,
                                "width": 7315200,
                                "height": 1143000
                            },
                            {
                                "idx": 1,
                                "type": "BODY",
                                "name": "左侧内容",
                                "left": 914400,
                                "top": 1828800,
                                "width": 3429000,
                                "height": 4114800
                            },
                            {
                                "idx": 2,
                                "type": "BODY",
                                "name": "右侧内容",
                                "left": 4572000,
                                "top": 1828800,
                                "width": 3429000,
                                "height": 4114800
                            }
                        ]
                    },
                    {
                        "index": 4,
                        "name": "图片和说明",
                        "placeholders": [
                            {
                                "idx": 0,
                                "type": "TITLE",
                                "name": "标题",
                                "left": 914400,
                                "top": 457200,
                                "width": 7315200,
                                "height": 1143000
                            },
                            {
                                "idx": 1,
                                "type": "PICTURE",
                                "name": "图片",
                                "left": 914400,
                                "top": 1828800,
                                "width": 4572000,
                                "height": 3429000
                            },
                            {
                                "idx": 2,
                                "type": "BODY",
                                "name": "图片说明",
                                "left": 5715000,
                                "top": 1828800,
                                "width": 2514600,
                                "height": 3429000
                            }
                        ]
                    }
                ],
                "theme_colors": {
                    "primary": "#1F4E79",
                    "secondary": "#70AD47",
                    "accent": "#FFC000",
                    "dark1": "#000000",
                    "light1": "#FFFFFF",
                    "dark2": "#44546A",
                    "light2": "#E7E6E6",
                    "accent1": "#4472C4",
                    "accent2": "#E7E6E6",
                    "accent3": "#A5A5A5",
                    "hyperlink": "#0563C1",
                    "followedHyperlink": "#954F72"
                },
                "fonts": {
                    "title_font": "微软雅黑",
                    "body_font": "微软雅黑",
                    "heading_font": "微软雅黑 Light",
                    "code_font": "Consolas",
                    "caption_font": "微软雅黑"
                },
                "slide_size": {
                    "width": 9144000,
                    "height": 6858000
                },
                "md2pptx_mapping": {
                    "TitleSlideLayout": 0,
                    "ContentSlideLayout": 1
                },
                "recommended_metadata": {
                    "theme": "default",
                    "size": "16:9"
                }
            }
        }


# 创建全局实例，使用正确的模板目录
# 获取当前文件所在目录的父目录下的templates文件夹
current_dir = Path(__file__).parent.parent  # backend目录
templates_dir = current_dir / "templates"
logger.info(f"模板管理器初始化，使用目录: {templates_dir}")
template_manager = TemplateManager(str(templates_dir))
logger.info(f"模板管理器创建完成，加载了 {len(template_manager.metadata)} 个模板")
