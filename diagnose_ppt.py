import sys
sys.path.insert(0, './backend')

from pptx import Presentation

# 检查生成的PPT文件
try:
    ppt_path = 'backend/uploads/generated/test_professional_template.pptx'
    prs = Presentation(ppt_path)
except Exception as e:
    print(f'无法打开PPT文件: {e}')
    exit(1)

print('🔍 诊断生成的PPT文件问题...')
print(f'总页数: {len(prs.slides)}')
print()

# 检查前3页的文字内容
for i, slide in enumerate(prs.slides[:3]):
    print(f'=== 第{i+1}页 ===')
    text_shapes = [shape for shape in slide.shapes if hasattr(shape, 'text') and shape.text.strip()]
    print(f'文字形状数量: {len(text_shapes)}')
    
    for j, shape in enumerate(text_shapes):
        text_preview = shape.text[:100].replace('\n', ' ') if shape.text else '(空)'
        print(f'  文字{j+1}: {text_preview}')
    print()
