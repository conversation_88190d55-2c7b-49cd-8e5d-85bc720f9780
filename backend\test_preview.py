#!/usr/bin/env python3
"""
测试预览图片生成功能
"""

import sys
import os
from pathlib import Path

# 添加当前目录到路径
sys.path.insert(0, '.')

def test_preview_generation():
    """测试预览图片生成"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        print("✅ PIL库导入成功")
        
        # 测试创建简单图片
        img = Image.new('RGB', (400, 300), color='white')
        draw = ImageDraw.Draw(img)
        
        # 测试字体加载
        font_paths = [
            "C:/Windows/Fonts/msyh.ttc",  # 微软雅黑
            "C:/Windows/Fonts/arial.ttf",  # Arial
            "C:/Windows/Fonts/calibri.ttf",  # Calibri
        ]
        
        font_found = False
        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    font = ImageFont.truetype(font_path, 16)
                    print(f"✅ 字体加载成功: {font_path}")
                    font_found = True
                    break
                except Exception as e:
                    print(f"❌ 字体加载失败 {font_path}: {e}")
        
        if not font_found:
            font = ImageFont.load_default()
            print("✅ 使用默认字体")
        
        # 绘制测试内容
        draw.rectangle([0, 0, 399, 299], outline='#1976d2', width=3)
        draw.text((50, 100), "测试预览图片", fill='#1976d2', font=font)
        
        # 保存测试图片
        test_path = "test_generated_preview.png"
        img.save(test_path, 'PNG')
        print(f"✅ 测试图片生成成功: {test_path}")
        
        # 检查文件大小
        if os.path.exists(test_path):
            size = os.path.getsize(test_path)
            print(f"✅ 文件大小: {size} bytes")
        
        return True
        
    except Exception as e:
        print(f"❌ 预览图片生成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_template_manager():
    """测试模板管理器的预览生成"""
    try:
        from services.template_manager import template_manager
        
        # 获取现有模板
        templates = template_manager.get_templates()
        print(f"✅ 找到 {len(templates)} 个模板")
        
        if templates:
            template = templates[0]
            template_id = template['id']
            print(f"测试模板: {template_id}")
            
            # 获取模板详情
            details = template_manager.get_template_details(template_id)
            if details:
                file_path = details.get('file_path')
                print(f"模板文件: {file_path}")
                
                if file_path and os.path.exists(file_path):
                    print("✅ 模板文件存在")
                    
                    # 尝试生成预览
                    try:
                        from pptx import Presentation
                        prs = Presentation(file_path)
                        print(f"✅ PPT文件加载成功，{len(prs.slides)} 张幻灯片")
                        
                        # 调用预览生成方法
                        preview_path = template_manager._generate_preview_image(prs, file_path)
                        if preview_path:
                            print(f"✅ 预览图片生成成功: {preview_path}")
                            
                            # 检查文件是否真的存在
                            full_path = template_manager.templates_dir / preview_path
                            if full_path.exists():
                                size = full_path.stat().st_size
                                print(f"✅ 预览文件存在，大小: {size} bytes")
                            else:
                                print(f"❌ 预览文件不存在: {full_path}")
                        else:
                            print("❌ 预览图片生成失败")
                            
                    except Exception as e:
                        print(f"❌ PPT处理失败: {e}")
                        import traceback
                        traceback.print_exc()
                else:
                    print(f"❌ 模板文件不存在: {file_path}")
            else:
                print("❌ 无法获取模板详情")
        else:
            print("❌ 没有找到模板")
            
    except Exception as e:
        print(f"❌ 模板管理器测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    print("=== 预览图片生成测试 ===")
    
    print("\n1. 测试基础图片生成功能:")
    test_preview_generation()
    
    print("\n2. 测试模板管理器预览生成:")
    test_template_manager()
    
    print("\n测试完成!")
