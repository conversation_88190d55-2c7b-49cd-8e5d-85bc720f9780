#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import json
import asyncio
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent))

async def test_sse_route():
    """测试SSE路由是否正确调用专业PPT生成器"""
    try:
        from app.services.sse_generator import sse_generator
        
        print('🔄 测试SSE路由修复效果...')
        print()
        
        # 模拟JSON大纲内容
        json_outline = {
            "title": "基于人工智能的结构化数据文本编辑方法及系统",
            "subtitle": "革命性的数据编辑解决方案",
            "slides": [
                {
                    "title": "技术背景",
                    "content": ["当前结构化数据编辑的局限性", "只能使用Excel等表格工具编辑"]
                },
                {
                    "title": "核心方案",
                    "content": ["S1: 序列化为普通文本", "S2: 文本工具编辑", "S3: 反序列化"]
                }
            ]
        }
        
        template_id = 'template_20250708_221451_adc64662'
        content = json.dumps(json_outline, ensure_ascii=False)
        user_input = '基于人工智能的结构化数据文本编辑方法'
        
        print(f'模板ID: {template_id}')
        print(f'内容类型: JSON大纲')
        print(f'内容长度: {len(content)}字符')
        print(f'用户输入: {user_input}')
        print()
        
        # 测试流式生成
        print('🚀 开始流式生成测试...')
        
        message_count = 0
        professional_generator_used = False
        
        async for message in sse_generator.generate_md2pptx_stream(
            template_id, content, user_input, apply_patches=True
        ):
            message_count += 1
            
            try:
                data = json.loads(message)
                msg_type = data.get('type', '')
                message_text = data.get('message', '')
                step = data.get('step', '')
                
                print(f'消息{message_count}: {msg_type} - {message_text}')
                
                # 检查是否使用了专业PPT生成器
                if '专业模板引擎' in message_text or '5步流程' in message_text:
                    professional_generator_used = True
                    print('  ✅ 检测到专业PPT生成器被调用！')
                
                # 检查是否还有md2pptx相关信息
                if 'md2pptx' in message_text.lower():
                    print('  ⚠️  仍然提及md2pptx')
                
                if msg_type == 'complete':
                    print('  🎯 生成完成')
                    break
                elif msg_type == 'error':
                    print(f'  ❌ 生成错误: {message_text}')
                    break
                    
            except json.JSONDecodeError:
                print(f'消息{message_count}: 非JSON格式 - {message[:100]}')
            
            # 限制测试消息数量
            if message_count > 20:
                print('  ⏹️  达到测试消息限制')
                break
        
        print()
        print('📊 测试结果:')
        print(f'总消息数: {message_count}')
        print(f'专业生成器被调用: {"✅ 是" if professional_generator_used else "❌ 否"}')
        
        if professional_generator_used:
            print('✅ 修复成功！SSE路由现在正确调用专业PPT生成器')
            return True
        else:
            print('❌ 修复失败！仍然没有调用专业PPT生成器')
            return False
            
    except Exception as e:
        print(f'❌ 测试异常: {e}')
        import traceback
        traceback.print_exc()
        return False

async def main():
    success = await test_sse_route()
    print(f'\n🎯 最终结果: {"✅ 修复成功" if success else "❌ 需要继续修复"}')

if __name__ == "__main__":
    asyncio.run(main())
