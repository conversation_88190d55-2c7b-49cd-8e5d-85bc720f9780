"""
PPT模板解析器
将成品PPT解析为模板，提取文字内容并生成占位符映射
"""

import logging
import json
import re
from pathlib import Path
from typing import Dict, List, Any, Tuple
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.shapes import MSO_SHAPE_TYPE
from pptx.enum.text import MSO_ANCHOR, PP_ALIGN
import hashlib

logger = logging.getLogger(__name__)


class PPTTemplateAnalyzer:
    """PPT模板解析器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent.parent.parent
        self.templates_dir = self.project_root / "backend" / "templates"
        import sys
        import os
        # 添加根目录到Python路径
        root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
        if root_dir not in sys.path:
            sys.path.insert(0, root_dir)

        from config_loader import settings
        self.analysis_cache_dir = self.project_root / "backend" / settings.TEMPLATE_ANALYSIS_DIR
        self.analysis_cache_dir.mkdir(exist_ok=True)
    
    def analyze_ppt_template(self, template_path: str) -> Dict[str, Any]:
        """
        深度解析PPT模板，提取文字结构和样式信息
        
        Args:
            template_path: PPT文件路径
            
        Returns:
            模板分析结果
        """
        try:
            logger.info(f"开始解析PPT模板: {template_path}")
            
            # 加载PPT文件
            prs = Presentation(template_path)
            
            # 生成分析结果
            analysis_result = {
                "template_path": template_path,
                "template_id": Path(template_path).stem,
                "total_slides": len(prs.slides),
                "slide_size": {
                    "width": prs.slide_width,
                    "height": prs.slide_height
                },
                "slides_analysis": [],
                "text_structure_summary": {},
                "generation_requirements": {}
            }
            
            # 逐页分析
            for slide_idx, slide in enumerate(prs.slides):
                slide_analysis = self._analyze_slide(slide, slide_idx)
                analysis_result["slides_analysis"].append(slide_analysis)
            
            # 生成文字结构摘要
            analysis_result["text_structure_summary"] = self._generate_text_structure_summary(
                analysis_result["slides_analysis"]
            )
            
            # 生成大模型生成要求
            analysis_result["generation_requirements"] = self._generate_llm_requirements(
                analysis_result["slides_analysis"]
            )
            
            # 保存分析结果
            self._save_analysis_result(analysis_result)
            
            logger.info(f"PPT模板解析完成: {len(prs.slides)}页，{len(analysis_result['text_structure_summary'])}个文字区域")
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"PPT模板解析失败: {e}")
            raise
    
    def _analyze_slide(self, slide, slide_idx: int) -> Dict[str, Any]:
        """分析单张幻灯片"""
        slide_analysis = {
            "slide_index": slide_idx,
            "slide_title": f"幻灯片{slide_idx + 1}",
            "text_elements": [],
            "non_text_elements": [],
            "layout_info": {
                "layout_name": slide.slide_layout.name if slide.slide_layout.name else f"布局{slide_idx}",
                "placeholders_count": len(slide.slide_layout.placeholders)
            }
        }
        
        # 分析所有形状
        for shape_idx, shape in enumerate(slide.shapes):
            if hasattr(shape, 'text') and shape.text.strip():
                # 文字元素
                text_element = self._analyze_text_shape(shape, slide_idx, shape_idx)
                slide_analysis["text_elements"].append(text_element)
            else:
                # 非文字元素（图片、图形等）
                non_text_element = self._analyze_non_text_shape(shape, shape_idx)
                slide_analysis["non_text_elements"].append(non_text_element)
        
        return slide_analysis
    
    def _analyze_text_shape(self, shape, slide_idx: int, shape_idx: int) -> Dict[str, Any]:
        """分析文字形状"""
        text_content = shape.text.strip()
        
        # 生成占位符ID
        placeholder_id = f"slide_{slide_idx}_text_{shape_idx}"
        
        # 分析文字类型
        text_type = self._classify_text_type(text_content, shape)
        
        # 分析字体样式
        font_info = self._extract_font_info(shape)
        
        # 计算文字统计信息
        text_stats = {
            "char_count": len(text_content),
            "word_count": len(text_content.split()),
            "line_count": text_content.count('\n') + 1,
            "max_line_length": max(len(line) for line in text_content.split('\n')) if text_content else 0
        }
        
        return {
            "placeholder_id": placeholder_id,
            "original_text": text_content,
            "text_type": text_type,
            "text_stats": text_stats,
            "position": {
                "left": shape.left,
                "top": shape.top,
                "width": shape.width,
                "height": shape.height
            },
            "font_info": font_info,
            "shape_properties": {
                "shape_type": str(shape.shape_type),
                "has_text_frame": hasattr(shape, 'text_frame'),
                "auto_size": shape.text_frame.auto_size if hasattr(shape, 'text_frame') else None
            }
        }
    
    def _analyze_non_text_shape(self, shape, shape_idx: int) -> Dict[str, Any]:
        """分析非文字形状"""
        return {
            "shape_index": shape_idx,
            "shape_type": str(shape.shape_type),
            "position": {
                "left": shape.left,
                "top": shape.top,
                "width": shape.width,
                "height": shape.height
            },
            "is_image": shape.shape_type == MSO_SHAPE_TYPE.PICTURE,
            "is_chart": shape.shape_type == MSO_SHAPE_TYPE.CHART,
            "is_table": shape.shape_type == MSO_SHAPE_TYPE.TABLE
        }
    
    def _classify_text_type(self, text_content: str, shape) -> str:
        """分类文字类型"""
        # 基于文字内容和位置特征判断类型
        text_length = len(text_content)
        
        # 基于长度判断
        if text_length <= 20:
            return "title"  # 标题
        elif text_length <= 50:
            return "subtitle"  # 副标题
        elif '\n' in text_content or '•' in text_content or '-' in text_content:
            return "bullet_list"  # 列表
        elif text_length > 100:
            return "paragraph"  # 段落
        else:
            return "content"  # 一般内容
    
    def _extract_font_info(self, shape) -> Dict[str, Any]:
        """提取字体信息"""
        font_info = {
            "font_name": None,
            "font_size": None,
            "font_bold": False,
            "font_italic": False,
            "font_color": None,
            "alignment": None
        }
        
        try:
            if hasattr(shape, 'text_frame') and shape.text_frame.paragraphs:
                para = shape.text_frame.paragraphs[0]
                if para.runs:
                    run = para.runs[0]
                    font = run.font
                    
                    font_info.update({
                        "font_name": font.name,
                        "font_size": font.size.pt if font.size else None,
                        "font_bold": font.bold,
                        "font_italic": font.italic,
                        "alignment": str(para.alignment) if para.alignment else None
                    })
        except Exception as e:
            logger.warning(f"提取字体信息失败: {e}")
        
        return font_info
    
    def _generate_text_structure_summary(self, slides_analysis: List[Dict]) -> Dict[str, Any]:
        """生成文字结构摘要"""
        summary = {
            "total_text_elements": 0,
            "text_types_distribution": {},
            "average_text_length": {},
            "slides_structure": []
        }
        
        all_text_elements = []
        
        for slide_analysis in slides_analysis:
            slide_structure = {
                "slide_index": slide_analysis["slide_index"],
                "text_elements_count": len(slide_analysis["text_elements"]),
                "text_types": []
            }
            
            for text_element in slide_analysis["text_elements"]:
                all_text_elements.append(text_element)
                slide_structure["text_types"].append({
                    "placeholder_id": text_element["placeholder_id"],
                    "text_type": text_element["text_type"],
                    "char_count": text_element["text_stats"]["char_count"]
                })
            
            summary["slides_structure"].append(slide_structure)
        
        # 统计信息
        summary["total_text_elements"] = len(all_text_elements)
        
        # 按类型统计
        type_stats = {}
        for element in all_text_elements:
            text_type = element["text_type"]
            if text_type not in type_stats:
                type_stats[text_type] = {"count": 0, "total_chars": 0}
            type_stats[text_type]["count"] += 1
            type_stats[text_type]["total_chars"] += element["text_stats"]["char_count"]
        
        for text_type, stats in type_stats.items():
            summary["text_types_distribution"][text_type] = stats["count"]
            summary["average_text_length"][text_type] = stats["total_chars"] // stats["count"]
        
        return summary
    
    def _generate_llm_requirements(self, slides_analysis: List[Dict]) -> Dict[str, Any]:
        """生成大模型生成要求"""
        requirements = {
            "total_slides_needed": len(slides_analysis),
            "slides_requirements": [],
            "content_constraints": {},
            "generation_prompt": ""
        }
        
        # 为每页生成要求
        for slide_analysis in slides_analysis:
            slide_req = {
                "slide_index": slide_analysis["slide_index"],
                "required_text_elements": []
            }
            
            for text_element in slide_analysis["text_elements"]:
                element_req = {
                    "placeholder_id": text_element["placeholder_id"],
                    "text_type": text_element["text_type"],
                    "recommended_char_count": text_element["text_stats"]["char_count"],
                    "char_range": {
                        "min": max(1, text_element["text_stats"]["char_count"] - 20),
                        "max": text_element["text_stats"]["char_count"] + 20
                    },
                    "original_example": text_element["original_text"][:50] + "..." if len(text_element["original_text"]) > 50 else text_element["original_text"]
                }
                slide_req["required_text_elements"].append(element_req)
            
            requirements["slides_requirements"].append(slide_req)
        
        # 生成约束条件
        requirements["content_constraints"] = {
            "total_slides": len(slides_analysis),
            "text_length_constraints": self._generate_length_constraints(slides_analysis)
        }
        
        # 生成提示词
        requirements["generation_prompt"] = self._generate_prompt_template(requirements)
        
        return requirements
    
    def _generate_length_constraints(self, slides_analysis: List[Dict]) -> Dict[str, Any]:
        """生成长度约束"""
        constraints = {}
        
        for slide_analysis in slides_analysis:
            for text_element in slide_analysis["text_elements"]:
                text_type = text_element["text_type"]
                char_count = text_element["text_stats"]["char_count"]
                
                if text_type not in constraints:
                    constraints[text_type] = {"min": char_count, "max": char_count, "examples": []}
                
                constraints[text_type]["min"] = min(constraints[text_type]["min"], char_count)
                constraints[text_type]["max"] = max(constraints[text_type]["max"], char_count)
                constraints[text_type]["examples"].append(char_count)
        
        return constraints
    
    def _generate_prompt_template(self, requirements: Dict[str, Any]) -> str:
        """生成提示词模板"""
        prompt_parts = [
            "请根据以下PPT模板结构要求生成内容：",
            f"",
            f"总页数：{requirements['total_slides_needed']}页",
            f"",
            f"每页内容要求："
        ]
        
        for slide_req in requirements["slides_requirements"]:
            prompt_parts.append(f"第{slide_req['slide_index'] + 1}页：")
            for element_req in slide_req["required_text_elements"]:
                prompt_parts.append(
                    f"  - {element_req['text_type']}：{element_req['char_range']['min']}-{element_req['char_range']['max']}字"
                )
            prompt_parts.append("")
        
        return "\n".join(prompt_parts)
    
    def _save_analysis_result(self, analysis_result: Dict[str, Any]):
        """保存分析结果"""
        template_id = analysis_result["template_id"]
        output_path = self.analysis_cache_dir / f"{template_id}_analysis.json"
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(analysis_result, f, ensure_ascii=False, indent=2, default=str)
        
        logger.info(f"模板分析结果已保存: {output_path}")


# 创建全局实例
ppt_template_analyzer = PPTTemplateAnalyzer()
