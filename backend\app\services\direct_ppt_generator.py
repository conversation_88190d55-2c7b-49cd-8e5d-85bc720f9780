"""
直接PPT生成器
基于用户的PPT文件直接生成新内容，保留所有设计元素
"""

import logging
from pathlib import Path
from typing import Dict, Any, List
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
import json
import shutil

logger = logging.getLogger(__name__)


class DirectPPTGenerator:
    """直接PPT生成器 - 保留原始设计"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent.parent.parent
        self.templates_dir = self.project_root / "backend" / "templates"
        self.output_dir = self.project_root / "backend" / "uploads" / "generated"
        
    def generate_from_outline(self, outline_json: str, template_id: str, output_filename: str) -> Dict[str, Any]:
        """
        基于JSON大纲和模板直接生成PPT
        
        Args:
            outline_json: JSON格式的大纲
            template_id: 模板ID
            output_filename: 输出文件名
            
        Returns:
            生成结果
        """
        try:
            # 解析大纲
            if isinstance(outline_json, str):
                outline = json.loads(outline_json)
            else:
                outline = outline_json
            
            # 加载模板文件
            template_path = self.templates_dir / f"{template_id}.pptx"
            if not template_path.exists():
                raise FileNotFoundError(f"模板文件不存在: {template_path}")
            
            logger.info(f"使用模板文件: {template_path}")
            
            # 创建新的演示文稿，基于模板
            prs = Presentation(str(template_path))
            logger.info(f"模板加载成功，原有 {len(prs.slides)} 张幻灯片")
            
            # 清空现有幻灯片，但保留设计
            # 我们将基于第一张幻灯片的设计创建新内容
            if len(prs.slides) > 0:
                # 保存第一张幻灯片作为设计模板
                design_slide = prs.slides[0]
                
                # 删除所有幻灯片
                slide_ids = [slide.slide_id for slide in prs.slides]
                for slide_id in slide_ids:
                    # 注意：python-pptx不支持直接删除幻灯片
                    # 我们采用替换内容的方式
                    pass
            
            # 基于大纲创建新幻灯片
            slides_data = outline.get('slides', [])
            logger.info(f"准备生成 {len(slides_data)} 张幻灯片，原有 {len(prs.slides)} 张")

            # 策略：使用现有幻灯片，按需要的数量循环使用
            target_slide_count = len(slides_data)
            original_slide_count = len(prs.slides)

            if target_slide_count <= original_slide_count:
                # 如果需要的幻灯片数量不超过原有数量，直接使用前N张
                logger.info(f"使用前 {target_slide_count} 张幻灯片")
                slides_to_use = list(range(target_slide_count))
            else:
                # 如果需要更多幻灯片，循环使用原有幻灯片
                logger.info(f"循环使用原有幻灯片来满足 {target_slide_count} 张的需求")
                slides_to_use = []
                for i in range(target_slide_count):
                    slide_index = i % original_slide_count
                    slides_to_use.append(slide_index)

            # 更新每张幻灯片的内容
            for i, slide_data in enumerate(slides_data):
                slide_index = slides_to_use[i]
                slide = prs.slides[slide_index]
                self._update_slide_content(slide, slide_data, i == 0)
                logger.info(f"更新幻灯片 {i+1}/{len(slides_data)} (使用原幻灯片 {slide_index+1})")

            # 如果原有幻灯片多于需要的，我们保留所有（python-pptx不支持删除）
            # 但只有前面的幻灯片被更新了内容
            
            # 保存文件
            output_path = self.output_dir / output_filename
            output_path.parent.mkdir(parents=True, exist_ok=True)
            prs.save(str(output_path))
            
            logger.info(f"PPT生成成功: {output_path}")
            
            return {
                'success': True,
                'file_path': str(output_path),
                'output_path': str(output_path),
                'slides_count': len(slides_data),
                'template_used': template_id,
                'file_size': output_path.stat().st_size if output_path.exists() else 0
            }
            
        except Exception as e:
            logger.error(f"直接PPT生成失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _duplicate_slide(self, prs: Presentation, slide_index: int):
        """
        复制指定的幻灯片（简化版本）
        """
        try:
            # 由于python-pptx限制，我们使用添加新幻灯片的方式
            source_slide = prs.slides[slide_index]
            new_slide = prs.slides.add_slide(source_slide.slide_layout)
            
            # 复制形状（简化版本）
            for shape in source_slide.shapes:
                if hasattr(shape, 'text'):
                    # 对于文本形状，创建相似的文本框
                    try:
                        textbox = new_slide.shapes.add_textbox(
                            shape.left, shape.top, shape.width, shape.height
                        )
                        textbox.text = ""  # 清空文本，等待填充新内容
                    except:
                        pass  # 忽略复制错误
            
            logger.info(f"幻灯片复制完成")
            
        except Exception as e:
            logger.warning(f"幻灯片复制失败: {e}")
    
    def _update_slide_content(self, slide, slide_data: Dict[str, Any], is_title_slide: bool = False):
        """
        智能更新幻灯片内容 - 清空原有文字，保留设计
        """
        try:
            title = slide_data.get('title', '')
            content = slide_data.get('content', [])

            logger.info(f"开始更新幻灯片: {title}")

            # 获取所有文本形状
            text_shapes = []
            for shape in slide.shapes:
                if hasattr(shape, 'text'):
                    text_shapes.append(shape)

            logger.info(f"找到 {len(text_shapes)} 个文本形状")

            # 策略1: 清空所有现有文本
            for i, shape in enumerate(text_shapes):
                try:
                    original_text = shape.text[:50] if shape.text else "(空)"
                    shape.text = ""  # 清空文本
                    logger.info(f"清空文本形状 {i}: \"{original_text}\"")
                except Exception as e:
                    logger.warning(f"清空文本形状 {i} 失败: {e}")

            # 策略2: 智能填充新内容
            if text_shapes:
                # 按大小排序，最大的作为标题
                sorted_shapes = sorted(text_shapes, key=lambda s: s.width * s.height, reverse=True)

                # 填充标题
                if title and len(sorted_shapes) > 0:
                    title_shape = sorted_shapes[0]
                    title_shape.text = title
                    logger.info(f"设置标题: {title}")

                    # 保持原有字体样式，只改变内容
                    try:
                        if title_shape.text_frame.paragraphs:
                            para = title_shape.text_frame.paragraphs[0]
                            if para.font.size is None:
                                para.font.size = Pt(24 if is_title_slide else 20)
                            para.font.bold = True
                    except:
                        pass

                # 填充内容
                if content and len(sorted_shapes) > 1:
                    # 使用第二大的文本框作为内容区域
                    content_shape = sorted_shapes[1]

                    if isinstance(content, list):
                        content_text = '\\n'.join([f'• {item}' for item in content])
                    else:
                        content_text = str(content)

                    content_shape.text = content_text
                    logger.info(f"设置内容: {len(content)} 项")

                    # 保持原有字体样式
                    try:
                        if content_shape.text_frame.paragraphs:
                            for para in content_shape.text_frame.paragraphs:
                                if para.font.size is None:
                                    para.font.size = Pt(16)
                    except:
                        pass

                # 如果还有更多内容项，填充到其他文本框
                if isinstance(content, list) and len(content) > 1 and len(sorted_shapes) > 2:
                    for i, item in enumerate(content[1:], 2):  # 从第3个文本框开始
                        if i < len(sorted_shapes):
                            try:
                                sorted_shapes[i].text = f"• {item}"
                                logger.info(f"设置额外内容 {i}: {item[:30]}")
                            except:
                                pass

            logger.info(f"幻灯片内容更新完成: {title}")

        except Exception as e:
            logger.error(f"更新幻灯片内容失败: {e}")
            import traceback
            traceback.print_exc()


# 创建全局实例
direct_ppt_generator = DirectPPTGenerator()
