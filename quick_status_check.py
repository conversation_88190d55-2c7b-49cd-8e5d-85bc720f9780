#!/usr/bin/env python3
"""
PPT Agent 快速状态检查脚本
用于新会话窗口快速了解项目当前状态
"""

import requests
import socket
import json
import os
from pathlib import Path

def check_port(port):
    """检查端口是否被占用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(1)
            return s.connect_ex(('127.0.0.1', port)) == 0
    except:
        return False

def check_backend_health():
    """检查后端健康状态"""
    try:
        response = requests.get('http://localhost:9527/health', timeout=5)
        return response.status_code == 200, response.json() if response.status_code == 200 else None
    except:
        return False, None

def check_frontend_access():
    """检查前端访问"""
    try:
        response = requests.get('http://127.0.0.1:9528', timeout=5)
        return response.status_code == 200
    except:
        return False

def check_template_api():
    """检查模板API"""
    try:
        response = requests.get('http://localhost:9527/api/templates/', timeout=5)
        if response.status_code == 200:
            templates = response.json()
            return True, len(templates), templates[0] if templates else None
        return False, 0, None
    except:
        return False, 0, None

def check_chat_api():
    """检查对话API"""
    try:
        response = requests.post(
            'http://localhost:9527/api/chat/template-chat',
            json={"user_input": "测试", "conversation_history": []},
            timeout=10
        )
        return response.status_code == 200
    except:
        return False

def check_config_files():
    """检查配置文件"""
    config_status = {}
    
    # 检查主配置文件
    config_path = Path('config.json')
    if config_path.exists():
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            config_status['config.json'] = {
                'exists': True,
                'backend_port': config.get('ports', {}).get('backend'),
                'frontend_port': config.get('ports', {}).get('frontend')
            }
        except:
            config_status['config.json'] = {'exists': True, 'error': 'Parse error'}
    else:
        config_status['config.json'] = {'exists': False}
    
    # 检查前端配置
    vite_config = Path('frontend/vite.config.js')
    config_status['vite.config.js'] = {'exists': vite_config.exists()}
    
    # 检查后端主文件
    main_py = Path('backend/app/main.py')
    config_status['backend/app/main.py'] = {'exists': main_py.exists()}
    
    return config_status

def main():
    """主检查函数"""
    print("🔍 PPT Agent 项目状态检查")
    print("=" * 50)
    
    # 1. 端口状态检查
    print("\n📡 端口状态:")
    backend_port_used = check_port(9527)
    frontend_port_used = check_port(9528)
    
    print(f"  后端端口 9527: {'🟢 使用中' if backend_port_used else '🔴 空闲'}")
    print(f"  前端端口 9528: {'🟢 使用中' if frontend_port_used else '🔴 空闲'}")
    
    # 2. 服务健康检查
    print("\n🏥 服务健康状态:")
    backend_healthy, health_data = check_backend_health()
    print(f"  后端服务: {'✅ 健康' if backend_healthy else '❌ 异常'}")
    if health_data:
        print(f"    服务信息: {health_data}")
    
    frontend_accessible = check_frontend_access()
    print(f"  前端服务: {'✅ 可访问' if frontend_accessible else '❌ 不可访问'}")
    
    # 3. API功能检查
    print("\n🔌 API功能状态:")
    
    # 模板API
    template_ok, template_count, first_template = check_template_api()
    print(f"  模板API: {'✅ 正常' if template_ok else '❌ 异常'}")
    if template_ok:
        print(f"    模板数量: {template_count}")
        if first_template:
            status = first_template.get('status', 'unknown')
            print(f"    首个模板状态: {status}")
    
    # 对话API
    chat_ok = check_chat_api()
    print(f"  对话API: {'✅ 正常' if chat_ok else '❌ 异常'}")
    
    # 4. 配置文件检查
    print("\n📄 配置文件状态:")
    config_status = check_config_files()
    
    for file_name, status in config_status.items():
        exists_icon = "✅" if status['exists'] else "❌"
        print(f"  {file_name}: {exists_icon}")
        if status.get('backend_port'):
            print(f"    后端端口: {status['backend_port']}")
        if status.get('frontend_port'):
            print(f"    前端端口: {status['frontend_port']}")
        if status.get('error'):
            print(f"    错误: {status['error']}")
    
    # 5. 总体状态评估
    print("\n📊 总体状态评估:")
    
    all_good = (
        backend_port_used and frontend_port_used and 
        backend_healthy and frontend_accessible and 
        template_ok and chat_ok
    )
    
    if all_good:
        print("🎉 系统状态良好，所有功能正常运行！")
        print("\n🌐 访问地址:")
        print("  前端: http://127.0.0.1:9528")
        print("  后端: http://localhost:9527")
        print("  API文档: http://localhost:9527/docs")
    else:
        print("⚠️ 系统存在问题，需要检查:")
        
        if not backend_port_used:
            print("  - 后端服务未启动")
        if not frontend_port_used:
            print("  - 前端服务未启动")
        if not backend_healthy:
            print("  - 后端服务健康检查失败")
        if not frontend_accessible:
            print("  - 前端服务不可访问")
        if not template_ok:
            print("  - 模板API异常")
        if not chat_ok:
            print("  - 对话API异常")
    
    # 6. 快速启动建议
    print("\n🚀 快速启动建议:")
    if not backend_port_used:
        print("  启动后端: cd backend && python run_server.py")
    if not frontend_port_used:
        print("  启动前端: cd frontend && npm run dev")
    
    print("\n📚 更多信息请查看: 项目详细说明文档.md")

if __name__ == "__main__":
    main()
