"""
SSE流式生成服务
实现实时PPT内容生成和进度推送
"""

import asyncio
import json
import logging
import os
import time
from typing import Dict, List, Optional, AsyncGenerator
from pathlib import Path

# 导入专业PPT生成器和其他必要组件
from .professional_ppt_generator import ProfessionalPPTGenerator
from .syntax_validator import syntax_validator
from .outline_converter import outline_converter

logger = logging.getLogger(__name__)

class SSEGenerator:
    """SSE流式生成器"""
    
    def __init__(self):
        self.is_generating = False
        self.current_session = None
        self.last_preview_content = None
        self.professional_generator = ProfessionalPPTGenerator()
        self.syntax_validator = syntax_validator

    async def generate_enhanced_stream(self, user_request: str, template_id: str, template_schema: Optional[Dict] = None) -> AsyncGenerator[str, None]:
        """
        使用增强的md2pptx内容生成器进行流式生成

        Args:
            user_request: 用户请求
            template_id: 模板ID
            template_schema: 模板结构信息
        """
        try:
            self.is_generating = True
            session_id = f"enhanced_{int(time.time())}"
            self.current_session = session_id

            # 1. 生成增强内容
            yield f"data: {json.dumps({'type': 'status', 'message': '正在生成md2pptx优化内容...', 'progress': 10})}\n\n"

            generation_result = self.content_generator.generate_enhanced_content(user_request, template_schema)

            if not generation_result['success']:
                error_msg = generation_result.get('error', '未知错误')
                yield f"data: {json.dumps({'type': 'error', 'message': f'内容生成失败: {error_msg}', 'session_id': session_id})}\n\n"
                return

            raw_content = generation_result['content']
            validation_result = generation_result['validation']

            # 2. 语法验证和自动修复
            yield f"data: {json.dumps({'type': 'status', 'message': '正在进行语法验证和优化...', 'progress': 25})}\n\n"

            syntax_result = self.syntax_validator.validate_syntax(raw_content, auto_fix=True)

            if syntax_result.fixed_content:
                validated_content = syntax_result.fixed_content
                yield f"data: {json.dumps({'type': 'info', 'message': f'自动修复了 {len([issue for issue in syntax_result.issues if issue.auto_fixable])} 个语法问题'})}\n\n"
            else:
                validated_content = raw_content

            # 3. 内容增强
            yield f"data: {json.dumps({'type': 'status', 'message': '正在进行内容增强优化...', 'progress': 40})}\n\n"

            enhancement_result = self.content_enhancer.enhance_content(validated_content, template_schema)
            final_content = enhancement_result.enhanced_content

            if enhancement_result.improvements_made:
                improvements_msg = f"应用了 {len(enhancement_result.improvements_made)} 项增强: {', '.join(enhancement_result.improvements_made[:3])}"
                if len(enhancement_result.improvements_made) > 3:
                    improvements_msg += "等"
                yield f"data: {json.dumps({'type': 'info', 'message': improvements_msg})}\n\n"

            # 4. 转换为md2pptx格式
            yield f"data: {json.dumps({'type': 'status', 'message': '正在转换为md2pptx格式...', 'progress': 55})}\n\n"

            converted_content = self._convert_to_md2pptx(final_content)

            # 5. 分页处理
            yield f"data: {json.dumps({'type': 'status', 'message': '正在分析页面结构...', 'progress': 70})}\n\n"

            pages = await self._split_content_to_pages(converted_content)

            # 6. 生成预览
            yield f"data: {json.dumps({'type': 'status', 'message': '正在生成预览...', 'progress': 85})}\n\n"

            preview_html = await self._generate_preview_html(pages, template_id)
            preview_url = f"/static/slideshow_preview.html?session={session_id}"

            # 保存预览内容
            self.last_preview_content = preview_html

            # 7. 生成下载文件
            yield f"data: {json.dumps({'type': 'status', 'message': '正在生成下载文件...', 'progress': 95})}\n\n"

            # 转换页面格式
            dict_pages = []
            for i, page in enumerate(pages, 1):
                lines = page.split('\n')
                title = f"第{i}页"
                content = page

                for line in lines:
                    if line.startswith('# '):
                        title = line[2:].strip()
                        content = '\n'.join(lines[1:])
                        break
                    elif line.startswith('## '):
                        title = line[3:].strip()
                        content = '\n'.join(lines[1:])
                        break

                dict_pages.append({
                    'title': title,
                    'content': content.strip(),
                    'page_number': i
                })

            download_result = await self._generate_download_files(dict_pages, template_id)

            # 确保download_result是字典
            if not isinstance(download_result, dict):
                download_result = {
                    'download_urls': {},
                    'file_sizes': {},
                    'hybrid_stats': {}
                }

            # 8. 返回完成结果
            result = {
                'type': 'complete',
                'session_id': session_id,
                'preview_url': preview_url,
                'download_urls': download_result.get('download_urls', {}),
                'file_sizes': download_result.get('file_sizes', {}),
                'pages_count': len(pages),
                'generation_stats': {
                    'content_generation': generation_result.get('stats', {}),
                    'syntax_validation': {
                        'compliance_score': syntax_result.compliance_score,
                        'issues_found': len(syntax_result.issues),
                        'auto_fixes_applied': len([issue for issue in syntax_result.issues if issue.auto_fixable])
                    },
                    'content_enhancement': {
                        'original_score': enhancement_result.original_score,
                        'enhanced_score': enhancement_result.enhancement_score,
                        'improvements_made': len(enhancement_result.improvements_made)
                    },
                    'hybrid_engine': download_result.get('hybrid_stats', {})
                },
                'quality_metrics': {
                    'validation_score': validation_result.compliance_score,
                    'syntax_score': syntax_result.compliance_score,
                    'enhancement_score': enhancement_result.enhancement_score,
                    'overall_score': (validation_result.compliance_score + syntax_result.compliance_score + enhancement_result.enhancement_score) / 3
                },
                'progress': 100
            }

            yield f"data: {json.dumps(result)}\n\n"

        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            logger.error(f"增强流式生成失败: {e}\n{error_details}")
            yield f"data: {json.dumps({'type': 'error', 'message': f'生成过程出错: {str(e)}', 'session_id': session_id})}\n\n"
        finally:
            self.is_generating = False

    def _convert_to_md2pptx(self, content: str) -> str:
        """转换内容为md2pptx格式"""
        # 确保内容符合md2pptx格式要求
        lines = content.split('\n')
        converted_lines = []

        # 检查是否有元数据
        has_metadata = False
        for line in lines[:10]:  # 检查前10行
            if ':' in line and not line.startswith('#'):
                has_metadata = True
                break

        # 如果没有元数据，添加基本元数据
        if not has_metadata:
            converted_lines.extend([
                'title: AI生成的演示文稿',
                'author: PPT助手',
                'date: ' + time.strftime('%Y-%m-%d'),
                '',
            ])

        # 处理内容行
        for line in lines:
            # 确保幻灯片分隔符格式正确
            if line.strip() in ['---', '===']:
                converted_lines.append('---')
            else:
                converted_lines.append(line)

        return '\n'.join(converted_lines)

    async def generate_stream(self, content: str, template_id: str, user_input: str = "") -> AsyncGenerator[str, None]:
        """
        SSE流式生成PPT内容
        
        Args:
            content: 要生成的内容
            template_id: 模板ID
            user_input: 用户输入（用于上下文）
            
        Yields:
            str: SSE格式的数据流
        """
        session_id = f"session_{int(time.time())}"
        self.current_session = session_id
        self.is_generating = True
        
        try:
            # 发送开始事件
            yield self._format_sse_data({
                "type": "start",
                "session_id": session_id,
                "message": "开始生成PPT内容...",
                "progress": 0,
                "timestamp": time.time()
            })
            
            await asyncio.sleep(0.1)  # 短暂延迟确保客户端接收
            
            # 分析内容，拆分为页面
            yield self._format_sse_data({
                "type": "progress",
                "session_id": session_id,
                "message": "分析内容结构...",
                "progress": 10,
                "timestamp": time.time()
            })
            
            pages = await self._split_content_to_pages(content)
            total_pages = len(pages)
            
            yield self._format_sse_data({
                "type": "progress",
                "session_id": session_id,
                "message": f"检测到 {total_pages} 页内容，开始逐页生成...",
                "progress": 20,
                "total_pages": total_pages,
                "timestamp": time.time()
            })
            
            # 逐页生成内容
            generated_pages = []
            for i, page_content in enumerate(pages):
                if not self.is_generating:
                    break
                    
                current_progress = 20 + (i + 1) * 60 // total_pages
                
                yield self._format_sse_data({
                    "type": "page_start",
                    "session_id": session_id,
                    "message": f"正在生成第 {i + 1} 页...",
                    "progress": current_progress,
                    "current_page": i + 1,
                    "total_pages": total_pages,
                    "timestamp": time.time()
                })
                
                # 模拟页面生成过程
                page_result = await self._generate_page_content(page_content, template_id, i + 1)
                generated_pages.append(page_result)
                
                yield self._format_sse_data({
                    "type": "page_complete",
                    "session_id": session_id,
                    "message": f"第 {i + 1} 页生成完成",
                    "progress": current_progress,
                    "current_page": i + 1,
                    "page_data": page_result,
                    "timestamp": time.time()
                })
                
                # 短暂延迟模拟真实生成时间
                await asyncio.sleep(0.5)
            
            # 生成预览
            yield self._format_sse_data({
                "type": "progress",
                "session_id": session_id,
                "message": "生成预览文件...",
                "progress": 85,
                "timestamp": time.time()
            })
            
            preview_result = await self._generate_preview(generated_pages, template_id)

            # 保存预览内容
            self.last_preview_content = preview_result.get("preview_content")
            
            # 生成下载文件
            yield self._format_sse_data({
                "type": "progress",
                "session_id": session_id,
                "message": "生成下载文件...",
                "progress": 95,
                "timestamp": time.time()
            })
            
            download_result = await self._generate_download_files(generated_pages, template_id)
            
            # 完成
            yield self._format_sse_data({
                "type": "complete",
                "session_id": session_id,
                "message": "PPT生成完成！",
                "progress": 100,
                "result": {
                    "total_pages": total_pages,
                    "generated_pages": len(generated_pages),
                    "preview_url": f"/api/sse/preview/{session_id}",
                    "download_urls": download_result.get("download_urls", {}),
                    "generation_time": time.time() - float(session_id.split('_')[1])
                },
                "timestamp": time.time()
            })
            
        except Exception as e:
            logger.error(f"SSE生成过程出错: {e}")
            yield self._format_sse_data({
                "type": "error",
                "session_id": session_id,
                "message": f"生成过程出错: {str(e)}",
                "error": str(e),
                "timestamp": time.time()
            })
        finally:
            self.is_generating = False
            self.current_session = None
    
    def _format_sse_data(self, data: Dict) -> str:
        """格式化SSE数据"""
        json_data = json.dumps(data, ensure_ascii=False)
        return f"data: {json_data}\n\n"
    
    async def _split_content_to_pages(self, content: str) -> List[str]:
        """将内容拆分为页面"""
        # 简单的页面拆分逻辑，基于标题层级
        pages = []
        lines = content.split('\n')
        current_page = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 检测新页面开始（# 或 ## 标题）
            if line.startswith('# ') or line.startswith('## '):
                if current_page:
                    pages.append('\n'.join(current_page))
                    current_page = []
                current_page.append(line)
            else:
                current_page.append(line)
        
        # 添加最后一页
        if current_page:
            pages.append('\n'.join(current_page))
        
        # 如果没有检测到页面，将整个内容作为一页
        if not pages:
            pages = [content]
            
        return pages
    
    async def _generate_page_content(self, page_content: str, template_id: str, page_number: int) -> Dict:
        """生成单页内容"""
        # 模拟页面内容生成
        await asyncio.sleep(0.3)  # 模拟处理时间
        
        return {
            "page_number": page_number,
            "title": self._extract_page_title(page_content),
            "content": page_content,
            "md2pptx_content": self._convert_to_md2pptx(page_content),
            "layout_type": self._detect_layout_type(page_content),
            "elements": self._extract_page_elements(page_content)
        }
    
    def _extract_page_title(self, content: str) -> str:
        """提取页面标题"""
        lines = content.split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith('# '):
                return line[2:].strip()
            elif line.startswith('## '):
                return line[3:].strip()
        return "无标题"
    
    def _convert_to_md2pptx(self, content: str) -> str:
        """转换为md2pptx格式 - 使用MD2PPTX主引擎和适配器"""
        try:
            # 验证md2pptx语法
            validation = md2pptx_engine.validate_md2pptx_syntax(content)

            if validation["is_valid"]:
                logger.info("内容已符合md2pptx语法标准")
                return content
            else:
                # 尝试修复语法问题
                logger.info(f"修复md2pptx语法问题: {validation['issues']}")

                # 基本语法修复
                fixed_content = content

                # 确保标题格式正确
                lines = fixed_content.split('\n')
                fixed_lines = []

                for line in lines:
                    # 修复标题格式
                    if line.strip().startswith('#'):
                        # 确保标题后有空格
                        if not line.startswith('# ') and not line.startswith('## ') and not line.startswith('### '):
                            line = line.replace('#', '# ', 1)

                    # 修复列表格式
                    elif line.strip().startswith('-') and not line.startswith('- '):
                        line = line.replace('-', '- ', 1)

                    fixed_lines.append(line)

                fixed_content = '\n'.join(fixed_lines)

                # 再次验证
                validation = md2pptx_engine.validate_md2pptx_syntax(fixed_content)
                if validation["is_valid"]:
                    logger.info("md2pptx语法修复成功")
                    return fixed_content
                else:
                    logger.warning("md2pptx语法修复失败，返回原内容")
                    return content

        except Exception as e:
            logger.error(f"md2pptx转换异常: {e}")
            return content
    
    def _detect_layout_type(self, content: str) -> str:
        """检测布局类型"""
        if '|' in content and '---' in content:
            return "table"
        elif '```funnel' in content:
            return "funnel"
        elif '```chart' in content:
            return "chart"
        elif content.count('\n-') > 2:
            return "bullet_list"
        else:
            return "text"
    
    def _extract_page_elements(self, content: str) -> List[Dict]:
        """提取页面元素"""
        elements = []
        lines = content.split('\n')
        
        for line in lines:
            line = line.strip()
            if line.startswith('# '):
                elements.append({"type": "title", "content": line[2:]})
            elif line.startswith('## '):
                elements.append({"type": "subtitle", "content": line[3:]})
            elif line.startswith('- '):
                elements.append({"type": "bullet", "content": line[2:]})
            elif line.startswith('```'):
                elements.append({"type": "code_block", "content": line})
            elif '|' in line:
                elements.append({"type": "table_row", "content": line})
            elif line:
                elements.append({"type": "text", "content": line})
        
        return elements
    
    async def _generate_preview(self, pages: List[Dict], template_id: str) -> Dict:
        """生成预览内容"""
        await asyncio.sleep(0.5)  # 模拟预览生成时间

        # 直接生成预览HTML内容，不保存文件
        html_content = self._generate_preview_html(pages, template_id)

        return {
            "preview_content": html_content,
            "preview_type": "html",
            "pages_count": len(pages),
            "template_id": template_id
        }

    def _generate_preview_html(self, pages: List[Dict], template_id: str) -> str:
        """生成预览HTML内容 - 使用MD2PPTX到Moffee适配器"""
        html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PPT预览 - {template_id}</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }}
        .pages {{
            padding: 40px;
        }}
        .page {{
            margin-bottom: 40px;
            padding: 30px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            background: #fafbfc;
            transition: all 0.3s ease;
        }}
        .page:hover {{
            border-color: #667eea;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
        }}
        .page-number {{
            background: #667eea;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 20px;
        }}
        .page-title {{
            font-size: 1.8em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        }}
        .page-content {{
            line-height: 1.6;
            color: #34495e;
            font-size: 1.1em;
        }}
        .footer {{
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #6c757d;
            border-top: 1px solid #e9ecef;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 PPT预览</h1>
            <p>模板ID: {template_id} | 总页数: {len(pages)}</p>
        </div>
        <div class="pages">
"""

        for i, page in enumerate(pages, 1):
            title = page.get('title', f'第{i}页')
            content = page.get('content', '页面内容')

            # 使用MD2PPTX到Moffee适配器处理内容
            try:
                # 构建单页md2pptx格式内容
                page_md2pptx = f"## {title}\n\n{content}"

                # 转换为Moffee兼容格式
                moffee_content = self.md2pptx_adapter.convert_md2pptx_to_moffee(page_md2pptx)

                # 应用视觉一致性
                template_style = {'font_size': 16, 'color_theme': template_id or 'default'}
                styled_content = self.md2pptx_adapter.maintain_visual_consistency(moffee_content, template_style)

                processed_content = styled_content

            except Exception as e:
                logger.warning(f"适配器处理失败，使用原始内容: {e}")
                processed_content = content

            html_content += f"""
            <div class="page">
                <div class="page-number">第 {i} 页</div>
                <div class="page-title">{title}</div>
                <div class="page-content">{processed_content}</div>
            </div>
"""

        html_content += f"""
        </div>
        <div class="footer">
            <p>🚀 由PPT Agent生成 | 生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
    </div>
</body>
</html>"""

        return html_content
    
    async def _generate_download_files(self, pages: List[Dict], template_id: str) -> Dict:
        """生成下载文件 - 使用混合PPT引擎"""
        try:
            # 将页面内容合并为完整的Markdown
            markdown_content = self._combine_pages_to_markdown(pages)

            # 使用混合PPT引擎生成PPT
            result = self.hybrid_engine.generate_ppt(
                content=markdown_content,
                template_id=template_id,
                metadata={
                    "author": "PPT生成系统",
                    "title": "AI生成演示文稿",
                    "date": time.strftime("%Y-%m-%d"),
                    "pages_count": len(pages)
                },
                output_filename=f"hybrid_ppt_{template_id}_{int(time.time())}.pptx"
            )

            if result.get("success", False):
                logger.info(f"混合引擎文件生成成功: {result.get('file_path', 'unknown')}")

                # 获取文件大小
                file_size = 0
                if result.get('file_path') and os.path.exists(result['file_path']):
                    file_size = os.path.getsize(result['file_path'])

                return {
                    "download_urls": {
                        "pptx": result.get("download_url", f"/api/download/{os.path.basename(result.get('file_path', 'unknown.pptx'))}"),
                        "pdf": f"/api/download/presentation_{template_id}_{int(time.time())}.pdf",
                        "html": f"/api/download/presentation_{template_id}_{int(time.time())}.html"
                    },
                    "file_sizes": {
                        "pptx": f"{file_size / 1024 / 1024:.1f}MB" if file_size > 0 else "未知",
                        "pdf": "1.8MB",
                        "html": "0.3MB"
                    },
                    "template_id": template_id,
                    "engine": result.get("engine", "hybrid"),
                    "patches_applied": result.get("patches_applied", []),
                    "hybrid_stats": self.hybrid_engine.get_engine_stats()
                }
            else:
                logger.error(f"MD2PPTX文件生成失败: {result['error']}")
                # 降级到模拟生成
                return await self._generate_fallback_files(template_id)

        except Exception as e:
            logger.error(f"MD2PPTX文件生成异常: {e}")
            # 降级到模拟生成
            return await self._generate_fallback_files(template_id)

    def _combine_pages_to_markdown(self, pages: List[Dict]) -> str:
        """将页面内容合并为完整的Markdown"""
        markdown_lines = []

        for i, page in enumerate(pages):
            if i == 0:
                # 第一页作为标题页
                markdown_lines.append(f"# {page.get('title', '演示文稿')}")
                if page.get('content'):
                    markdown_lines.append(page['content'])
            else:
                # 其他页面作为内容页
                markdown_lines.append(f"\n## {page.get('title', f'幻灯片 {i+1}')}")
                if page.get('content'):
                    markdown_lines.append(page['content'])

        return '\n\n'.join(markdown_lines)

    async def _generate_fallback_files(self, template_id: str) -> Dict:
        """生成下载文件的备用方案"""
        await asyncio.sleep(0.8)  # 模拟文件生成时间

        # 生成文件名
        timestamp = int(time.time())

        return {
            "download_urls": {
                "pptx": f"/api/download/presentation_fallback_{template_id}_{timestamp}.pptx",
                "pdf": f"/api/download/presentation_fallback_{template_id}_{timestamp}.pdf",
                "html": f"/api/download/presentation_fallback_{template_id}_{timestamp}.html"
            },
            "file_sizes": {
                "pptx": "2.5MB",
                "pdf": "1.8MB",
                "html": "0.3MB"
            },
            "engine": "fallback"
        }
    
    async def generate_md2pptx_stream(self, template_id: str, content: str, user_input: str = "", apply_patches: bool = True) -> AsyncGenerator[str, None]:
        """
        使用md2pptx主引擎进行流式生成

        Args:
            template_id: 模板ID
            content: 要生成的内容
            user_input: 用户输入上下文
            apply_patches: 是否应用渐进式补丁
        """
        try:
            self.is_generating = True
            session_id = f"md2pptx_{int(time.time())}"
            self.current_session = session_id

            # 发送开始事件
            yield json.dumps({
                "type": "start",
                "session_id": session_id,
                "message": "开始md2pptx流式生成...",
                "progress": 0
            })

            await asyncio.sleep(0.1)

            # 步骤0: 格式转换（如果需要）
            yield json.dumps({
                "type": "progress",
                "session_id": session_id,
                "message": "正在转换内容格式...",
                "progress": 5,
                "step": "format_conversion"
            })

            # 检查内容是否为JSON格式
            processed_content = content
            enhanced_content = content  # 初始化enhanced_content
            is_json_outline = False

            try:
                # 尝试解析为JSON
                json.loads(content)
                is_json_outline = True
                logger.info("检测到JSON格式大纲，将使用专业PPT生成器")
                # 暂时不转换，保留原始JSON用于专业生成器
                processed_content = content
                enhanced_content = content  # JSON内容直接作为enhanced_content
            except (json.JSONDecodeError, TypeError):
                # 不是JSON格式，直接使用原内容
                logger.info("内容已是Markdown格式，将使用传统流程")
                is_json_outline = False
                enhanced_content = content  # Markdown内容作为enhanced_content

            await asyncio.sleep(0.1)

            # 根据内容类型决定处理步骤
            if not is_json_outline:
                # 只有Markdown内容才需要增强和验证

                # 步骤1: 内容增强
                yield json.dumps({
                    "type": "progress",
                    "session_id": session_id,
                    "message": "正在增强内容...",
                    "progress": 10,
                    "step": "content_enhancement"
                })

                enhancement_result = self.content_enhancer.enhance_content(processed_content, user_input)
                enhanced_content = enhancement_result.enhanced_content
                await asyncio.sleep(0.5)

                # 步骤2: 语法验证
                yield json.dumps({
                    "type": "progress",
                    "session_id": session_id,
                    "message": "正在验证md2pptx语法...",
                    "progress": 20,
                    "step": "syntax_validation"
                })

                validation_result = self.syntax_validator.validate_syntax(enhanced_content)
                if not validation_result.is_valid:
                    issues_summary = [issue.message for issue in validation_result.issues[:3]]
                    logger.warning(f"语法验证警告: {issues_summary}")
                await asyncio.sleep(0.3)
            else:
                # JSON大纲直接跳到PPT生成
                yield json.dumps({
                    "type": "progress",
                    "session_id": session_id,
                    "message": "正在解析模板结构...",
                    "progress": 15,
                    "step": "template_analysis"
                })
                await asyncio.sleep(0.3)

            # 步骤3: 生成PPT
            if is_json_outline:
                yield json.dumps({
                    "type": "progress",
                    "session_id": session_id,
                    "message": "正在使用专业模板引擎生成PPT（5步流程）...",
                    "progress": 30,
                    "step": "professional_ppt_generation"
                })
            else:
                yield json.dumps({
                    "type": "progress",
                    "session_id": session_id,
                    "message": "正在使用md2pptx引擎生成PPT...",
                    "progress": 30,
                    "step": "md2pptx_generation"
                })

            # 根据内容格式选择生成引擎
            if is_json_outline:
                logger.info("使用新的基于成品PPT模板的生成器（5步流程）")

                # 使用专业PPT生成器
                try:
                    output_filename = f"ppt_{session_id}.pptx"
                    test_mode = "测试" in user_input or "test" in user_input.lower()

                    # 使用专业PPT生成器的5步流程
                    generation_result = self.professional_generator.generate_ppt_from_template(
                        template_id=template_id,
                        user_request=user_input,
                        output_filename=output_filename,
                        test_mode=test_mode
                    )

                    logger.info("专业PPT生成器调用完成")

                except Exception as e:
                    logger.error(f"专业PPT生成器失败: {e}")
                    generation_result = {
                        "success": False,
                        "error": str(e)
                    }

            else:
                logger.info("使用专业PPT生成器处理Markdown内容")

                # 对于Markdown内容，也使用专业PPT生成器
                try:
                    output_filename = f"ppt_{session_id}.pptx"

                    # 将Markdown内容和用户输入合并
                    combined_request = f"{user_input}\n\n{processed_content}"

                    generation_result = self.professional_generator.generate_ppt_from_template(
                        template_id=template_id,
                        user_request=combined_request,
                        output_filename=output_filename
                    )

                except Exception as e:
                    logger.error(f"专业PPT生成器处理Markdown失败: {e}")
                    generation_result = {
                        "success": False,
                        "error": str(e)
                    }

            await asyncio.sleep(1.0)

            # 步骤4: 生成预览
            yield json.dumps({
                "type": "progress",
                "session_id": session_id,
                "message": "正在生成在线预览...",
                "progress": 70,
                "step": "preview_generation"
            })

            # 使用适配器生成Moffee预览（确保变量存在）
            preview_content = enhanced_content if 'enhanced_content' in locals() else processed_content
            preview_html = self.md2pptx_adapter.convert_md2pptx_to_moffee(preview_content)
            preview_path = f"/static/preview_{session_id}.html"

            # 保存预览文件
            static_dir = Path("static")
            static_dir.mkdir(exist_ok=True)
            with open(static_dir / f"preview_{session_id}.html", "w", encoding="utf-8") as f:
                f.write(preview_html)

            await asyncio.sleep(0.5)

            # 步骤5: 完成
            yield json.dumps({
                "type": "progress",
                "session_id": session_id,
                "message": "生成完成！",
                "progress": 100,
                "step": "complete"
            })

            # 发送完成事件
            result = {
                "type": "complete",
                "session_id": session_id,
                "message": "md2pptx PPT生成完成！",
                "progress": 100,
                "result": {
                    "preview_url": preview_path,
                    "download_url": generation_result.get("download_url", f"/api/download/presentation_{session_id}.pptx"),
                    "file_info": {
                        "name": f"presentation_{session_id}.pptx",
                        "size": generation_result.get("file_size", "未知"),
                        "format": "PPTX"
                    },
                    "engine": "md2pptx_hybrid",
                    "patches_applied": apply_patches,
                    "generation_time": time.time()
                }
            }

            yield json.dumps(result)

        except Exception as e:
            logger.error(f"md2pptx流式生成失败: {e}")
            yield json.dumps({
                "type": "error",
                "session_id": session_id,
                "error": str(e),
                "message": "md2pptx生成过程中出现错误"
            })
        finally:
            self.is_generating = False

    def stop_generation(self):
        """停止当前生成过程"""
        self.is_generating = False
        logger.info("SSE生成过程已停止")

# 全局SSE生成器实例
sse_generator = SSEGenerator()
