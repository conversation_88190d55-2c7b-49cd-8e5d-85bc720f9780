# -*- coding: utf-8 -*-
"""
SSE流式生成服务 - 基于专业PPT生成器
提供实时的PPT生成进度反馈
"""

import asyncio
import json
import logging
import time
import traceback
from pathlib import Path
from typing import AsyncGenerator, Dict, List, Optional

# 导入专业PPT生成器和其他必要组件
from .professional_ppt_generator import ProfessionalPPTGenerator
from .syntax_validator import syntax_validator
from .outline_converter import outline_converter
from .moffee_service import MoffeeService

logger = logging.getLogger(__name__)

class SSEGenerator:
    """SSE流式生成器 - 基于专业PPT生成器"""
    
    def __init__(self):
        self.is_generating = False
        self.current_session = None
        self.last_preview_content = None
        self.professional_generator = ProfessionalPPTGenerator()
        self.syntax_validator = syntax_validator
        self.moffee_service = MoffeeService()

    async def generate_enhanced_stream(self, user_request: str, template_id: str, template_schema: Optional[Dict] = None) -> AsyncGenerator[str, None]:
        """
        使用专业PPT生成器进行流式生成

        Args:
            user_request: 用户请求
            template_id: 模板ID
            template_schema: 模板结构信息
        """
        try:
            self.is_generating = True
            session_id = f"enhanced_{int(time.time())}"
            self.current_session = session_id

            # 1. 开始生成
            yield f"data: {json.dumps({'type': 'status', 'message': '正在启动专业PPT生成器...', 'progress': 10})}\n\n"
            await asyncio.sleep(0.1)

            # 2. 分析模板
            yield f"data: {json.dumps({'type': 'status', 'message': '正在分析PPT模板...', 'progress': 20})}\n\n"
            await asyncio.sleep(0.5)

            # 3. 生成内容
            yield f"data: {json.dumps({'type': 'status', 'message': '正在生成PPT内容...', 'progress': 40})}\n\n"

            output_filename = f"enhanced_{session_id}.pptx"
            generation_result = self.professional_generator.generate_ppt_from_template(
                template_id=template_id,
                user_request=user_request,
                output_filename=output_filename
            )

            if not generation_result.get('success'):
                error_msg = generation_result.get('error', '未知错误')
                yield f"data: {json.dumps({'type': 'error', 'message': f'PPT生成失败: {error_msg}', 'session_id': session_id})}\n\n"
                return

            # 4. 生成预览
            yield f"data: {json.dumps({'type': 'status', 'message': '正在生成预览...', 'progress': 70})}\n\n"

            # 使用Moffee服务生成预览
            preview_content = f"# {user_request}\n\n生成的PPT内容预览"
            try:
                preview_result = self.moffee_service.generate_preview(preview_content)
                preview_path = f"/static/preview_{session_id}.html"
            except Exception as e:
                logger.warning(f"预览生成失败，使用默认预览: {e}")
                preview_path = "/static/default_preview.html"

            # 5. 完成
            yield f"data: {json.dumps({'type': 'status', 'message': '生成完成！', 'progress': 100})}\n\n"

            # 发送最终结果
            final_result = {
                'type': 'complete',
                'session_id': session_id,
                'preview_url': preview_path,
                'download_url': generation_result.get('download_url', f'/api/download/{output_filename}'),
                'file_info': {
                    'name': output_filename,
                    'size': generation_result.get('file_size', '未知'),
                    'format': 'PPTX'
                },
                'generation_stats': {
                    'template_id': template_id,
                    'engine': 'professional_ppt_generator',
                    'generation_time': time.time()
                }
            }

            yield f"data: {json.dumps(final_result)}\n\n"

        except Exception as e:
            error_details = traceback.format_exc()
            logger.error(f"增强流式生成失败: {e}\n{error_details}")
            yield f"data: {json.dumps({'type': 'error', 'message': f'生成过程出错: {str(e)}', 'session_id': session_id})}\n\n"
        finally:
            self.is_generating = False

    async def generate_professional_stream(self, template_id: str, content: str, user_input: str = "", apply_patches: bool = True) -> AsyncGenerator[str, None]:
        """
        使用专业PPT生成器进行流式生成

        Args:
            template_id: 模板ID
            content: 要生成的内容
            user_input: 用户输入上下文
            apply_patches: 是否应用补丁（保留兼容性）
        """
        try:
            self.is_generating = True
            session_id = f"professional_{int(time.time())}"
            self.current_session = session_id

            # 发送开始事件
            yield json.dumps({
                "type": "start",
                "session_id": session_id,
                "message": "开始专业PPT流式生成...",
                "progress": 0
            })

            await asyncio.sleep(0.1)

            # 步骤1: 内容预处理
            yield json.dumps({
                "type": "progress",
                "session_id": session_id,
                "message": "正在预处理内容...",
                "progress": 10,
                "step": "content_preprocessing"
            })

            # 合并用户输入和内容
            combined_request = f"{user_input}\n\n{content}" if user_input else content

            # 检测内容格式
            is_json_outline = False
            try:
                json.loads(content)
                is_json_outline = True
                logger.info("检测到JSON格式大纲")
            except:
                logger.info("检测到文本格式内容")

            # 步骤2: 语法验证
            yield json.dumps({
                "type": "progress",
                "session_id": session_id,
                "message": "正在验证内容格式...",
                "progress": 20,
                "step": "syntax_validation"
            })

            # 简单的语法验证
            validation_result = self.syntax_validator.validate_syntax(content)
            if not validation_result.is_valid:
                yield json.dumps({
                    "type": "warning",
                    "session_id": session_id,
                    "message": f"内容格式警告: {len(validation_result.issues)} 个问题",
                    "details": [str(issue) for issue in validation_result.issues[:3]]
                })

            # 步骤3: PPT生成
            yield json.dumps({
                "type": "progress",
                "session_id": session_id,
                "message": "正在使用专业PPT生成器...",
                "progress": 40,
                "step": "professional_generation"
            })

            # 使用专业PPT生成器
            output_filename = f"professional_{session_id}.pptx"
            generation_result = self.professional_generator.generate_ppt_from_template(
                template_id=template_id,
                user_request=combined_request,
                output_filename=output_filename
            )

            if not generation_result.get("success"):
                yield json.dumps({
                    "type": "error",
                    "session_id": session_id,
                    "error": generation_result.get("error", "PPT生成失败"),
                    "message": "专业PPT生成器执行失败"
                })
                return

            # 步骤4: 生成预览
            yield json.dumps({
                "type": "progress",
                "session_id": session_id,
                "message": "正在生成预览...",
                "progress": 80,
                "step": "preview_generation"
            })

            # 生成预览
            preview_content = f"# PPT预览\n\n{content[:500]}..."
            try:
                preview_result = self.moffee_service.generate_preview(preview_content)
                preview_path = f"/static/preview_{session_id}.html"
            except Exception as e:
                logger.warning(f"预览生成失败: {e}")
                preview_path = "/static/default_preview.html"

            # 发送完成事件
            result = {
                "type": "complete",
                "session_id": session_id,
                "message": "专业PPT生成完成！",
                "progress": 100,
                "result": {
                    "preview_url": preview_path,
                    "download_url": generation_result.get("download_url", f"/api/download/{output_filename}"),
                    "file_info": {
                        "name": output_filename,
                        "size": generation_result.get("file_size", "未知"),
                        "format": "PPTX"
                    },
                    "engine": "professional_ppt_generator",
                    "generation_time": time.time()
                }
            }

            yield json.dumps(result)

        except Exception as e:
            logger.error(f"专业PPT流式生成失败: {e}")
            yield json.dumps({
                "type": "error",
                "session_id": session_id,
                "error": str(e),
                "message": "专业PPT生成过程中出现错误"
            })
        finally:
            self.is_generating = False

    def stop_generation(self):
        """停止生成"""
        self.is_generating = False
        logger.info("SSE生成已停止")

    def get_generation_status(self):
        """获取生成状态"""
        return {
            "is_generating": self.is_generating,
            "current_session": self.current_session
        }

    # 兼容性方法 - 为了保持与原有代码的兼容性
    async def generate_md2pptx_stream(self, template_id: str, content: str, user_input: str = "", apply_patches: bool = True) -> AsyncGenerator[str, None]:
        """
        兼容性方法：重定向到专业PPT生成器
        """
        logger.info("调用兼容性方法 generate_md2pptx_stream，重定向到专业PPT生成器")
        async for chunk in self.generate_professional_stream(template_id, content, user_input, apply_patches):
            yield chunk

# 创建全局实例
sse_generator = SSEGenerator()
