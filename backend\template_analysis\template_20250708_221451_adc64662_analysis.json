{"template_path": "backend/templates/template_20250708_221451_adc64662.pptx", "template_id": "template_20250708_221451_adc64662", "total_slides": 18, "slide_size": {"width": 12192000, "height": 6858000}, "slides_analysis": [{"slide_index": 0, "slide_title": "幻灯片1", "text_elements": [{"placeholder_id": "slide_0_text_2", "original_text": "主讲人：", "text_type": "title", "text_stats": {"char_count": 4, "word_count": 1, "line_count": 1, "max_line_length": 4}, "position": {"left": 1353391, "top": 5514590, "width": 920911, "height": 317779}, "font_info": {"font_name": "Source <PERSON>", "font_size": 18.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "CENTER (2)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_0_text_3", "original_text": "AiPPT", "text_type": "title", "text_stats": {"char_count": 5, "word_count": 1, "line_count": 1, "max_line_length": 5}, "position": {"left": 2246841, "top": 5488813, "width": 897908, "height": 369332}, "font_info": {"font_name": "OPPOSans H", "font_size": 18.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_0_text_6", "original_text": "时间：", "text_type": "title", "text_stats": {"char_count": 3, "word_count": 1, "line_count": 1, "max_line_length": 3}, "position": {"left": 4014385, "top": 5554537, "width": 920911, "height": 237884}, "font_info": {"font_name": "Source <PERSON>", "font_size": 18.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "CENTER (2)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_0_text_7", "original_text": "2025.7", "text_type": "title", "text_stats": {"char_count": 6, "word_count": 1, "line_count": 1, "max_line_length": 6}, "position": {"left": 4692336, "top": 5488813, "width": 985247, "height": 369332}, "font_info": {"font_name": "OPPOSans H", "font_size": 9.79, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_0_text_15", "original_text": "PowerPoint design", "text_type": "title", "text_stats": {"char_count": 17, "word_count": 2, "line_count": 1, "max_line_length": 17}, "position": {"left": 889117, "top": 4569554, "width": 3000495, "height": 461665}, "font_info": {"font_name": "Source <PERSON>", "font_size": 20.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_0_text_22", "original_text": "202X", "text_type": "title", "text_stats": {"char_count": 4, "word_count": 1, "line_count": 1, "max_line_length": 4}, "position": {"left": 601804, "top": 1378424, "width": 3741482, "height": 1428188}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 96.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_0_text_23", "original_text": "基于数据组件的文书编辑工具", "text_type": "title", "text_stats": {"char_count": 13, "word_count": 1, "line_count": 1, "max_line_length": 13}, "position": {"left": 607303, "top": 2611521, "width": 6153108, "height": 1791367}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 46.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}], "non_text_elements": [{"shape_index": 0, "shape_type": "PICTURE (13)", "position": {"left": -1, "top": 0, "width": 12226974, "height": 6874042}, "is_image": true, "is_chart": false, "is_table": false}, {"shape_index": 1, "shape_type": "TEXT_BOX (17)", "position": {"left": -1, "top": 1, "width": 12226973, "height": 6874042}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 4, "shape_type": "TEXT_BOX (17)", "position": {"left": 724530, "top": 5431959, "width": 483041, "height": 483041}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 5, "shape_type": "FREEFORM (5)", "position": {"left": 865787, "top": 5557699, "width": 213768, "height": 231561}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 8, "shape_type": "TEXT_BOX (17)", "position": {"left": 3443940, "top": 5431959, "width": 483041, "height": 483041}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 9, "shape_type": "FREEFORM (5)", "position": {"left": 3580836, "top": 5566311, "width": 227407, "height": 214336}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 10, "shape_type": "FREEFORM (5)", "position": {"left": 731388, "top": 661141, "width": 2214690, "height": 275018}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 11, "shape_type": "FREEFORM (5)", "position": {"left": -1, "top": 6335594, "width": 934028, "height": 306789}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 12, "shape_type": "FREEFORM (5)", "position": {"left": 3996102, "top": 1968227, "width": 930740, "height": 314396}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 13, "shape_type": "FREEFORM (5)", "position": {"left": 0, "top": -2, "width": 653691, "height": 1144814}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 14, "shape_type": "TEXT_BOX (17)", "position": {"left": 744088, "top": 4546391, "width": 5259703, "height": 507990}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 16, "shape_type": "TEXT_BOX (17)", "position": {"left": 6323369, "top": 3493049, "width": 3739281, "height": 3364950}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 17, "shape_type": "TEXT_BOX (17)", "position": {"left": 6792291, "top": 4365188, "width": 1272168, "height": 1144814}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 18, "shape_type": "PICTURE (13)", "position": {"left": 6582847, "top": -2, "width": 5627441, "height": 6523824}, "is_image": true, "is_chart": false, "is_table": false}, {"shape_index": 19, "shape_type": "TEXT_BOX (17)", "position": {"left": 5528074, "top": 0, "width": 2937827, "height": 2643359}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 20, "shape_type": "FREEFORM (5)", "position": {"left": 10392879, "top": 3645935, "width": 1834094, "height": 3212063}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 21, "shape_type": "LINE (9)", "position": {"left": 3411940, "top": 4800386, "width": 2088108, "height": 0}, "is_image": false, "is_chart": false, "is_table": false}], "layout_info": {"layout_name": "布局0", "placeholders_count": 0}}, {"slide_index": 1, "slide_title": "幻灯片2", "text_elements": [{"placeholder_id": "slide_1_text_6", "original_text": "目录", "text_type": "title", "text_stats": {"char_count": 2, "word_count": 1, "line_count": 1, "max_line_length": 2}, "position": {"left": 2591968, "top": 3254567, "width": 1538883, "height": 923330}, "font_info": {"font_name": "OPPOSans H", "font_size": 60.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_1_text_7", "original_text": "工具概述", "text_type": "title", "text_stats": {"char_count": 4, "word_count": 1, "line_count": 1, "max_line_length": 4}, "position": {"left": 7048402, "top": 914400, "width": 5143598, "height": 953368}, "font_info": {"font_name": "OPPOSans H", "font_size": 18.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_1_text_8", "original_text": "01", "text_type": "title", "text_stats": {"char_count": 2, "word_count": 1, "line_count": 1, "max_line_length": 2}, "position": {"left": 6132513, "top": 1097118, "width": 802592, "height": 587933}, "font_info": {"font_name": "OPPOSans H", "font_size": 28.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "CENTER (2)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_1_text_17", "original_text": "功能特点", "text_type": "title", "text_stats": {"char_count": 4, "word_count": 1, "line_count": 1, "max_line_length": 4}, "position": {"left": 7048402, "top": 1960321, "width": 5143598, "height": 953368}, "font_info": {"font_name": "OPPOSans H", "font_size": 18.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_1_text_18", "original_text": "02", "text_type": "title", "text_stats": {"char_count": 2, "word_count": 1, "line_count": 1, "max_line_length": 2}, "position": {"left": 6132513, "top": 2143039, "width": 802592, "height": 587933}, "font_info": {"font_name": "OPPOSans H", "font_size": 28.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "CENTER (2)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_1_text_19", "original_text": "应用场景", "text_type": "title", "text_stats": {"char_count": 4, "word_count": 1, "line_count": 1, "max_line_length": 4}, "position": {"left": 7048402, "top": 3006242, "width": 5143598, "height": 953368}, "font_info": {"font_name": "OPPOSans H", "font_size": 18.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_1_text_20", "original_text": "03", "text_type": "title", "text_stats": {"char_count": 2, "word_count": 1, "line_count": 1, "max_line_length": 2}, "position": {"left": 6132513, "top": 3188960, "width": 802592, "height": 587933}, "font_info": {"font_name": "OPPOSans H", "font_size": 28.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "CENTER (2)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_1_text_21", "original_text": "效率提升与优势", "text_type": "title", "text_stats": {"char_count": 7, "word_count": 1, "line_count": 1, "max_line_length": 7}, "position": {"left": 7048402, "top": 4052163, "width": 5143598, "height": 953368}, "font_info": {"font_name": "OPPOSans H", "font_size": 18.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_1_text_22", "original_text": "04", "text_type": "title", "text_stats": {"char_count": 2, "word_count": 1, "line_count": 1, "max_line_length": 2}, "position": {"left": 6132513, "top": 4234881, "width": 802592, "height": 587933}, "font_info": {"font_name": "OPPOSans H", "font_size": 28.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "CENTER (2)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_1_text_23", "original_text": "未来展望", "text_type": "title", "text_stats": {"char_count": 4, "word_count": 1, "line_count": 1, "max_line_length": 4}, "position": {"left": 7048402, "top": 5098084, "width": 5143598, "height": 953368}, "font_info": {"font_name": "OPPOSans H", "font_size": 18.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_1_text_24", "original_text": "05", "text_type": "title", "text_stats": {"char_count": 2, "word_count": 1, "line_count": 1, "max_line_length": 2}, "position": {"left": 6132513, "top": 5280802, "width": 802592, "height": 587933}, "font_info": {"font_name": "OPPOSans H", "font_size": 28.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "CENTER (2)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}], "non_text_elements": [{"shape_index": 0, "shape_type": "TEXT_BOX (17)", "position": {"left": -18288, "top": -32227, "width": 12210288, "height": 6890227}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 1, "shape_type": "TEXT_BOX (17)", "position": {"left": 0, "top": 0, "width": 12192000, "height": 6858000}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 2, "shape_type": "FREEFORM (5)", "position": {"left": 1408038, "top": 1188105, "width": 3880634, "height": 5040590}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 3, "shape_type": "FREEFORM (5)", "position": {"left": 1682698, "top": 1516874, "width": 3331315, "height": 4383051}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 4, "shape_type": "FREEFORM (5)", "position": {"left": 1894934, "top": 1764593, "width": 2906841, "height": 3887614}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 5, "shape_type": "FREEFORM (5)", "position": {"left": 2172331, "top": 2105061, "width": 2352049, "height": 3206677}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 9, "shape_type": "TEXT_BOX (17)", "position": {"left": 4415776, "top": 1969121, "width": 342900, "height": 342900}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 10, "shape_type": "TEXT_BOX (17)", "position": {"left": 4511026, "top": 2064371, "width": 152400, "height": 152400}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 11, "shape_type": "TEXT_BOX (17)", "position": {"left": 4988744, "top": 3041393, "width": 342900, "height": 342900}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 12, "shape_type": "TEXT_BOX (17)", "position": {"left": 5083994, "top": 3136643, "width": 152400, "height": 152400}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 13, "shape_type": "TEXT_BOX (17)", "position": {"left": 4415776, "top": 5076878, "width": 342900, "height": 342900}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 14, "shape_type": "TEXT_BOX (17)", "position": {"left": 4511026, "top": 5172128, "width": 152400, "height": 152400}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 15, "shape_type": "TEXT_BOX (17)", "position": {"left": 4988744, "top": 4013288, "width": 342900, "height": 342900}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 16, "shape_type": "TEXT_BOX (17)", "position": {"left": 5083994, "top": 4108538, "width": 152400, "height": 152400}, "is_image": false, "is_chart": false, "is_table": false}], "layout_info": {"layout_name": "布局1", "placeholders_count": 0}}, {"slide_index": 2, "slide_title": "幻灯片3", "text_elements": [{"placeholder_id": "slide_2_text_11", "original_text": "工具概述", "text_type": "title", "text_stats": {"char_count": 4, "word_count": 1, "line_count": 1, "max_line_length": 4}, "position": {"left": 607251, "top": 3401402, "width": 5924390, "height": 2330658}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 36.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_2_text_12", "original_text": "Part", "text_type": "title", "text_stats": {"char_count": 4, "word_count": 1, "line_count": 1, "max_line_length": 4}, "position": {"left": 664546, "top": 2248451, "width": 2703140, "height": 1075448}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 72.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_2_text_13", "original_text": "01", "text_type": "title", "text_stats": {"char_count": 2, "word_count": 1, "line_count": 1, "max_line_length": 2}, "position": {"left": 2910082, "top": 945727, "width": 2003111, "height": 2378172}, "font_info": {"font_name": "OPPOSans H", "font_size": 72.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}], "non_text_elements": [{"shape_index": 0, "shape_type": "PICTURE (13)", "position": {"left": -1, "top": 0, "width": 12226974, "height": 6874042}, "is_image": true, "is_chart": false, "is_table": false}, {"shape_index": 1, "shape_type": "TEXT_BOX (17)", "position": {"left": -1, "top": 1, "width": 12226973, "height": 6874042}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 2, "shape_type": "FREEFORM (5)", "position": {"left": 744088, "top": 661141, "width": 2214690, "height": 275018}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 3, "shape_type": "FREEFORM (5)", "position": {"left": -1, "top": 6335594, "width": 934028, "height": 306789}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 4, "shape_type": "FREEFORM (5)", "position": {"left": 4323648, "top": 2634019, "width": 963038, "height": 325306}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 5, "shape_type": "FREEFORM (5)", "position": {"left": 0, "top": -2, "width": 653691, "height": 1144814}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 6, "shape_type": "TEXT_BOX (17)", "position": {"left": 6323369, "top": 3493049, "width": 3739281, "height": 3364950}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 7, "shape_type": "TEXT_BOX (17)", "position": {"left": 6792291, "top": 4365188, "width": 1272168, "height": 1144814}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 8, "shape_type": "PICTURE (13)", "position": {"left": 6582847, "top": -2, "width": 5627441, "height": 6523824}, "is_image": true, "is_chart": false, "is_table": false}, {"shape_index": 9, "shape_type": "TEXT_BOX (17)", "position": {"left": 5528074, "top": 0, "width": 2937827, "height": 2643359}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 10, "shape_type": "FREEFORM (5)", "position": {"left": 10392879, "top": 3645935, "width": 1834094, "height": 3212063}, "is_image": false, "is_chart": false, "is_table": false}], "layout_info": {"layout_name": "布局2", "placeholders_count": 0}}, {"slide_index": 3, "slide_title": "幻灯片4", "text_elements": [{"placeholder_id": "slide_3_text_4", "original_text": "主要目标", "text_type": "title", "text_stats": {"char_count": 4, "word_count": 1, "line_count": 1, "max_line_length": 4}, "position": {"left": 6556871, "top": 2676035, "width": 2720649, "height": 844339}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 16.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "CENTER (2)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_3_text_6", "original_text": "本工具将行业业务数据转换为可编辑的数据组件，结合传统在线文本编辑器功能，形成在线文档编辑工具，旨在解决传统文书编制中难以直接调用现有信息系统数据的问题。", "text_type": "content", "text_stats": {"char_count": 76, "word_count": 1, "line_count": 1, "max_line_length": 76}, "position": {"left": 2896652, "top": 3534444, "width": 2717238, "height": 2705709}, "font_info": {"font_name": "Source <PERSON>", "font_size": 14.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "CENTER (2)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_3_text_7", "original_text": "核心定义", "text_type": "title", "text_stats": {"char_count": 4, "word_count": 1, "line_count": 1, "max_line_length": 4}, "position": {"left": 2894945, "top": 2676035, "width": 2720649, "height": 844339}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 16.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "CENTER (2)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_3_text_8", "original_text": "实现行业业务数据与文书数据的双向转换，减少对既有文书工作习惯的干扰，借助系统内专业领域数据辅助文书制作，提升文书编制效率。", "text_type": "content", "text_stats": {"char_count": 61, "word_count": 1, "line_count": 1, "max_line_length": 61}, "position": {"left": 6553762, "top": 3617184, "width": 2717238, "height": 2705709}, "font_info": {"font_name": "Source <PERSON>", "font_size": 14.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "CENTER (2)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_3_text_18", "original_text": "工具定义与目标", "text_type": "title", "text_stats": {"char_count": 7, "word_count": 1, "line_count": 1, "max_line_length": 7}, "position": {"left": 824877, "top": 330175, "width": 8405995, "height": 432000}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 32.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}], "non_text_elements": [{"shape_index": 0, "shape_type": "TEXT_BOX (17)", "position": {"left": -18288, "top": -32227, "width": 12210288, "height": 6890227}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 1, "shape_type": "FREEFORM (5)", "position": {"left": 10437117, "top": 1956869, "width": 1754883, "height": 3515311}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 2, "shape_type": "FREEFORM (5)", "position": {"left": 0, "top": 2148298, "width": 1736470, "height": 3513452}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 3, "shape_type": "TEXT_BOX (17)", "position": {"left": 6304386, "top": 1469647, "width": 3225619, "height": 5022592}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 5, "shape_type": "TEXT_BOX (17)", "position": {"left": 2671585, "top": 1466510, "width": 3225619, "height": 5022592}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 9, "shape_type": "TEXT_BOX (17)", "position": {"left": 3833432, "top": 1615322, "width": 881163, "height": 881163}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 10, "shape_type": "TEXT_BOX (17)", "position": {"left": 3833432, "top": 1615322, "width": 881163, "height": 881163}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 11, "shape_type": "TEXT_BOX (17)", "position": {"left": 3890900, "top": 1672789, "width": 766229, "height": 766229}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 12, "shape_type": "FREEFORM (5)", "position": {"left": 4082457, "top": 1845750, "width": 383114, "height": 420307}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 13, "shape_type": "TEXT_BOX (17)", "position": {"left": 7478810, "top": 1615322, "width": 881163, "height": 881163}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 14, "shape_type": "TEXT_BOX (17)", "position": {"left": 7478810, "top": 1615322, "width": 881163, "height": 881163}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 15, "shape_type": "TEXT_BOX (17)", "position": {"left": 7536278, "top": 1672789, "width": 766229, "height": 766229}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 16, "shape_type": "FREEFORM (5)", "position": {"left": 7727835, "top": 1845750, "width": 383114, "height": 420307}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 17, "shape_type": "TEXT_BOX (17)", "position": {"left": 361150, "top": 186175, "width": 9232259, "height": 720000}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 19, "shape_type": "TEXT_BOX (17)", "position": {"left": 193595, "top": 330275, "width": 444500, "height": 431800}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 20, "shape_type": "FREEFORM (5)", "position": {"left": 313291, "top": 449516, "width": 205107, "height": 193318}, "is_image": false, "is_chart": false, "is_table": false}], "layout_info": {"layout_name": "布局3", "placeholders_count": 0}}, {"slide_index": 4, "slide_title": "幻灯片5", "text_elements": [{"placeholder_id": "slide_4_text_11", "original_text": "功能特点", "text_type": "title", "text_stats": {"char_count": 4, "word_count": 1, "line_count": 1, "max_line_length": 4}, "position": {"left": 607251, "top": 3401402, "width": 5924390, "height": 2330658}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 36.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_4_text_12", "original_text": "Part", "text_type": "title", "text_stats": {"char_count": 4, "word_count": 1, "line_count": 1, "max_line_length": 4}, "position": {"left": 664546, "top": 2248451, "width": 2703140, "height": 1075448}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 72.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_4_text_13", "original_text": "02", "text_type": "title", "text_stats": {"char_count": 2, "word_count": 1, "line_count": 1, "max_line_length": 2}, "position": {"left": 2910082, "top": 945727, "width": 2003111, "height": 2378172}, "font_info": {"font_name": "OPPOSans H", "font_size": 72.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}], "non_text_elements": [{"shape_index": 0, "shape_type": "PICTURE (13)", "position": {"left": -1, "top": 0, "width": 12226974, "height": 6874042}, "is_image": true, "is_chart": false, "is_table": false}, {"shape_index": 1, "shape_type": "TEXT_BOX (17)", "position": {"left": -1, "top": 1, "width": 12226973, "height": 6874042}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 2, "shape_type": "FREEFORM (5)", "position": {"left": 744088, "top": 661141, "width": 2214690, "height": 275018}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 3, "shape_type": "FREEFORM (5)", "position": {"left": -1, "top": 6335594, "width": 934028, "height": 306789}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 4, "shape_type": "FREEFORM (5)", "position": {"left": 4323648, "top": 2634019, "width": 963038, "height": 325306}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 5, "shape_type": "FREEFORM (5)", "position": {"left": 0, "top": -2, "width": 653691, "height": 1144814}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 6, "shape_type": "TEXT_BOX (17)", "position": {"left": 6323369, "top": 3493049, "width": 3739281, "height": 3364950}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 7, "shape_type": "TEXT_BOX (17)", "position": {"left": 6792291, "top": 4365188, "width": 1272168, "height": 1144814}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 8, "shape_type": "PICTURE (13)", "position": {"left": 6582847, "top": -2, "width": 5627441, "height": 6523824}, "is_image": true, "is_chart": false, "is_table": false}, {"shape_index": 9, "shape_type": "TEXT_BOX (17)", "position": {"left": 5528074, "top": 0, "width": 2937827, "height": 2643359}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 10, "shape_type": "FREEFORM (5)", "position": {"left": 10392879, "top": 3645935, "width": 1834094, "height": 3212063}, "is_image": false, "is_chart": false, "is_table": false}], "layout_info": {"layout_name": "布局4", "placeholders_count": 0}}, {"slide_index": 5, "slide_title": "幻灯片6", "text_elements": [{"placeholder_id": "slide_5_text_3", "original_text": "通过特定算法和规则，将行业业务数据精准转换为可编辑的数据组件，确保数据的完整性和准确性，为文书编辑提供可靠的数据基础。", "text_type": "content", "text_stats": {"char_count": 59, "word_count": 1, "line_count": 1, "max_line_length": 59}, "position": {"left": 1476415, "top": 4908070, "width": 4400402, "height": 1301890}, "font_info": {"font_name": "Source <PERSON>", "font_size": 14.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "CENTER (2)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_5_text_4", "original_text": "转换机制", "text_type": "title", "text_stats": {"char_count": 4, "word_count": 1, "line_count": 1, "max_line_length": 4}, "position": {"left": 1476415, "top": 4063694, "width": 4400402, "height": 745920}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 16.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "CENTER (2)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_5_text_6", "original_text": "实现数据的灵活复用，避免重复录入，提高工作效率，同时保证数据的一致性和实时性，减少因数据更新不及时导致的错误。", "text_type": "content", "text_stats": {"char_count": 55, "word_count": 1, "line_count": 1, "max_line_length": 55}, "position": {"left": 6315183, "top": 4001412, "width": 4400402, "height": 1301890}, "font_info": {"font_name": "Source <PERSON>", "font_size": 14.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "CENTER (2)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_5_text_7", "original_text": "转换优势", "text_type": "title", "text_stats": {"char_count": 4, "word_count": 1, "line_count": 1, "max_line_length": 4}, "position": {"left": 6315183, "top": 3157037, "width": 4400402, "height": 745920}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 16.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "CENTER (2)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_5_text_11", "original_text": "数据组件转换", "text_type": "title", "text_stats": {"char_count": 6, "word_count": 1, "line_count": 1, "max_line_length": 6}, "position": {"left": 824877, "top": 330175, "width": 8405995, "height": 432000}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 32.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}], "non_text_elements": [{"shape_index": 0, "shape_type": "TEXT_BOX (17)", "position": {"left": -18288, "top": -32227, "width": 12210288, "height": 6890227}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 1, "shape_type": "FREEFORM (5)", "position": {"left": -181707, "top": 2675731, "width": 12397291, "height": 1264806}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 2, "shape_type": "TEXT_BOX (17)", "position": {"left": 2885753, "top": 1913110, "width": 1581727, "height": 1581727}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 5, "shape_type": "FREEFORM (5)", "position": {"left": 3456850, "top": 2452992, "width": 439533, "height": 501962}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 8, "shape_type": "TEXT_BOX (17)", "position": {"left": 7724521, "top": 1209653, "width": 1581727, "height": 1581727}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 9, "shape_type": "FREEFORM (5)", "position": {"left": 8211743, "top": 1696830, "width": 607283, "height": 607373}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 10, "shape_type": "TEXT_BOX (17)", "position": {"left": 361150, "top": 186175, "width": 9232259, "height": 720000}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 12, "shape_type": "TEXT_BOX (17)", "position": {"left": 193595, "top": 330275, "width": 444500, "height": 431800}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 13, "shape_type": "FREEFORM (5)", "position": {"left": 313291, "top": 449516, "width": 205107, "height": 193318}, "is_image": false, "is_chart": false, "is_table": false}], "layout_info": {"layout_name": "布局5", "placeholders_count": 0}}, {"slide_index": 6, "slide_title": "幻灯片7", "text_elements": [{"placeholder_id": "slide_6_text_3", "original_text": "保留传统在线文本编辑器的字体、样式调整等常规功能，满足用户日常文书编辑需求，降低用户的学习成本和使用门槛。", "text_type": "content", "text_stats": {"char_count": 53, "word_count": 1, "line_count": 1, "max_line_length": 53}, "position": {"left": 4578533, "top": 2017491, "width": 5404724, "height": 1491843}, "font_info": {"font_name": "Source <PERSON>", "font_size": 14.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_6_text_4", "original_text": "常规编辑功能", "text_type": "title", "text_stats": {"char_count": 6, "word_count": 1, "line_count": 1, "max_line_length": 6}, "position": {"left": 4578533, "top": 1413552, "width": 5404724, "height": 603938}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 16.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_6_text_7", "original_text": "支持在文书正文中直接引用和编辑数据组件，用户可以像编辑普通文本一样操作数据组件，实现数据与文本的无缝融合，提升文书的专业性和准确性。", "text_type": "content", "text_stats": {"char_count": 66, "word_count": 1, "line_count": 1, "max_line_length": 66}, "position": {"left": 4578533, "top": 4673522, "width": 5404724, "height": 1491843}, "font_info": {"font_name": "Source <PERSON>", "font_size": 14.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_6_text_8", "original_text": "数据组件引用与编辑", "text_type": "title", "text_stats": {"char_count": 9, "word_count": 1, "line_count": 1, "max_line_length": 9}, "position": {"left": 4578533, "top": 4069583, "width": 5404724, "height": 603938}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 16.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_6_text_15", "original_text": "01", "text_type": "title", "text_stats": {"char_count": 2, "word_count": 1, "line_count": 1, "max_line_length": 2}, "position": {"left": 3831212, "top": 2148439, "width": 458935, "height": 453351}, "font_info": {"font_name": "思源黑体 CN Regular", "font_size": 14.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "CENTER (2)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_6_text_16", "original_text": "02", "text_type": "title", "text_stats": {"char_count": 2, "word_count": 1, "line_count": 1, "max_line_length": 2}, "position": {"left": 3867361, "top": 4790961, "width": 379669, "height": 453351}, "font_info": {"font_name": "思源黑体 CN Regular", "font_size": 14.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "CENTER (2)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_6_text_18", "original_text": "文书编辑功能", "text_type": "title", "text_stats": {"char_count": 6, "word_count": 1, "line_count": 1, "max_line_length": 6}, "position": {"left": 824877, "top": 330175, "width": 8405995, "height": 432000}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 32.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}], "non_text_elements": [{"shape_index": 0, "shape_type": "TEXT_BOX (17)", "position": {"left": -18288, "top": -32227, "width": 12210288, "height": 6890227}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 1, "shape_type": "TEXT_BOX (17)", "position": {"left": 2718931, "top": 1676472, "width": 1363556, "height": 1370266}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 2, "shape_type": "TEXT_BOX (17)", "position": {"left": 3863737, "top": 2178172, "width": 393884, "height": 393884}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 5, "shape_type": "TEXT_BOX (17)", "position": {"left": 2718931, "top": 4332503, "width": 1363556, "height": 1370266}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 6, "shape_type": "TEXT_BOX (17)", "position": {"left": 3860253, "top": 4820694, "width": 393884, "height": 393884}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 9, "shape_type": "TEXT_BOX (17)", "position": {"left": -52950, "top": 3831482, "width": 959939, "height": 964664}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 10, "shape_type": "TEXT_BOX (17)", "position": {"left": 751734, "top": 4124263, "width": 716748, "height": 720276}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 11, "shape_type": "TEXT_BOX (17)", "position": {"left": 10478928, "top": 4455524, "width": 507328, "height": 509825}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 12, "shape_type": "TEXT_BOX (17)", "position": {"left": 11294085, "top": 3509334, "width": 959939, "height": 964664}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 13, "shape_type": "FREEFORM (5)", "position": {"left": 3169477, "top": 2111125, "width": 462465, "height": 500960}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 14, "shape_type": "FREEFORM (5)", "position": {"left": 3109734, "top": 4715073, "width": 581950, "height": 605127}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 17, "shape_type": "TEXT_BOX (17)", "position": {"left": 361150, "top": 186175, "width": 9232259, "height": 720000}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 19, "shape_type": "TEXT_BOX (17)", "position": {"left": 193595, "top": 330275, "width": 444500, "height": 431800}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 20, "shape_type": "FREEFORM (5)", "position": {"left": 313291, "top": 449516, "width": 205107, "height": 193318}, "is_image": false, "is_chart": false, "is_table": false}], "layout_info": {"layout_name": "布局6", "placeholders_count": 0}}, {"slide_index": 7, "slide_title": "幻灯片8", "text_elements": [{"placeholder_id": "slide_7_text_8", "original_text": "实现文书数据与系统数据的双向转换，用户在编辑文书时，系统数据可以实时更新，反之亦然，确保数据的实时性和一致性，避免数据孤岛现象。", "text_type": "content", "text_stats": {"char_count": 64, "word_count": 1, "line_count": 1, "max_line_length": 64}, "position": {"left": 8815454, "top": 3642427, "width": 2703446, "height": 1390351}, "font_info": {"font_name": "Source <PERSON>", "font_size": 14.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_7_text_9", "original_text": "文书数据与系统数据同步", "text_type": "title", "text_stats": {"char_count": 11, "word_count": 1, "line_count": 1, "max_line_length": 11}, "position": {"left": 8815452, "top": 2816829, "width": 2703447, "height": 733169}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 16.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_7_text_10", "original_text": "在数据双向转换过程中，采用加密算法和权限管理机制，确保数据的安全性和保密性，防止数据泄露和未授权访问。", "text_type": "content", "text_stats": {"char_count": 51, "word_count": 1, "line_count": 1, "max_line_length": 51}, "position": {"left": 660400, "top": 3642426, "width": 2716147, "height": 1874453}, "font_info": {"font_name": "Source <PERSON>", "font_size": 14.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "RIGHT (3)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_7_text_11", "original_text": "转换安全保障", "text_type": "title", "text_stats": {"char_count": 6, "word_count": 1, "line_count": 1, "max_line_length": 6}, "position": {"left": 660400, "top": 2710181, "width": 2716147, "height": 839818}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 16.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "RIGHT (3)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_7_text_13", "original_text": "数据双向转换", "text_type": "title", "text_stats": {"char_count": 6, "word_count": 1, "line_count": 1, "max_line_length": 6}, "position": {"left": 824877, "top": 330175, "width": 8405995, "height": 432000}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 32.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}], "non_text_elements": [{"shape_index": 0, "shape_type": "TEXT_BOX (17)", "position": {"left": -18288, "top": -32227, "width": 12210288, "height": 6890227}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 1, "shape_type": "GROUP (6)", "position": {"left": 3601088, "top": 1544320, "width": 4989825, "height": 4419599}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 2, "shape_type": "TEXT_BOX (17)", "position": {"left": 5230495, "top": 2854324, "width": 1731010, "height": 1731010}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 3, "shape_type": "GROUP (6)", "position": {"left": 5509725, "top": 3108145, "width": 1172550, "height": 1172550}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 4, "shape_type": "TEXT_BOX (17)", "position": {"left": 7838542, "top": 3280619, "width": 653168, "height": 653168}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 5, "shape_type": "GROUP (6)", "position": {"left": 7927526, "top": 3370029, "width": 475200, "height": 474346}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 6, "shape_type": "TEXT_BOX (17)", "position": {"left": 3700291, "top": 3280619, "width": 653168, "height": 653168}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 7, "shape_type": "GROUP (6)", "position": {"left": 3789275, "top": 3370029, "width": 475200, "height": 474346}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 12, "shape_type": "TEXT_BOX (17)", "position": {"left": 361150, "top": 186175, "width": 9232259, "height": 720000}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 14, "shape_type": "TEXT_BOX (17)", "position": {"left": 193595, "top": 330275, "width": 444500, "height": 431800}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 15, "shape_type": "FREEFORM (5)", "position": {"left": 313291, "top": 449516, "width": 205107, "height": 193318}, "is_image": false, "is_chart": false, "is_table": false}], "layout_info": {"layout_name": "布局7", "placeholders_count": 0}}, {"slide_index": 8, "slide_title": "幻灯片9", "text_elements": [{"placeholder_id": "slide_8_text_11", "original_text": "应用场景", "text_type": "title", "text_stats": {"char_count": 4, "word_count": 1, "line_count": 1, "max_line_length": 4}, "position": {"left": 607251, "top": 3401402, "width": 5924390, "height": 2330658}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 36.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_8_text_12", "original_text": "Part", "text_type": "title", "text_stats": {"char_count": 4, "word_count": 1, "line_count": 1, "max_line_length": 4}, "position": {"left": 664546, "top": 2248451, "width": 2703140, "height": 1075448}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 72.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_8_text_13", "original_text": "03", "text_type": "title", "text_stats": {"char_count": 2, "word_count": 1, "line_count": 1, "max_line_length": 2}, "position": {"left": 2910082, "top": 945727, "width": 2003111, "height": 2378172}, "font_info": {"font_name": "OPPOSans H", "font_size": 72.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}], "non_text_elements": [{"shape_index": 0, "shape_type": "PICTURE (13)", "position": {"left": -1, "top": 0, "width": 12226974, "height": 6874042}, "is_image": true, "is_chart": false, "is_table": false}, {"shape_index": 1, "shape_type": "TEXT_BOX (17)", "position": {"left": -1, "top": 1, "width": 12226973, "height": 6874042}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 2, "shape_type": "FREEFORM (5)", "position": {"left": 744088, "top": 661141, "width": 2214690, "height": 275018}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 3, "shape_type": "FREEFORM (5)", "position": {"left": -1, "top": 6335594, "width": 934028, "height": 306789}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 4, "shape_type": "FREEFORM (5)", "position": {"left": 4323648, "top": 2634019, "width": 963038, "height": 325306}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 5, "shape_type": "FREEFORM (5)", "position": {"left": 0, "top": -2, "width": 653691, "height": 1144814}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 6, "shape_type": "TEXT_BOX (17)", "position": {"left": 6323369, "top": 3493049, "width": 3739281, "height": 3364950}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 7, "shape_type": "TEXT_BOX (17)", "position": {"left": 6792291, "top": 4365188, "width": 1272168, "height": 1144814}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 8, "shape_type": "PICTURE (13)", "position": {"left": 6582847, "top": -2, "width": 5627441, "height": 6523824}, "is_image": true, "is_chart": false, "is_table": false}, {"shape_index": 9, "shape_type": "TEXT_BOX (17)", "position": {"left": 5528074, "top": 0, "width": 2937827, "height": 2643359}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 10, "shape_type": "FREEFORM (5)", "position": {"left": 10392879, "top": 3645935, "width": 1834094, "height": 3212063}, "is_image": false, "is_chart": false, "is_table": false}], "layout_info": {"layout_name": "布局8", "placeholders_count": 0}}, {"slide_index": 9, "slide_title": "幻灯片10", "text_elements": [{"placeholder_id": "slide_9_text_2", "original_text": "金融行业", "text_type": "title", "text_stats": {"char_count": 4, "word_count": 1, "line_count": 1, "max_line_length": 4}, "position": {"left": 1645145, "top": 1796995, "width": 3888000, "height": 826456}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 16.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_9_text_4", "original_text": "在金融行业，该工具可以将客户信息、交易数据等转换为数据组件，方便金融文档的快速生成和编辑，提高金融业务处理效率，减少人工录入错误。", "text_type": "content", "text_stats": {"char_count": 65, "word_count": 1, "line_count": 1, "max_line_length": 65}, "position": {"left": 1645145, "top": 3240221, "width": 3888000, "height": 1705489}, "font_info": {"font_name": "Source <PERSON>", "font_size": 14.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_9_text_5", "original_text": "医疗行业", "text_type": "title", "text_stats": {"char_count": 4, "word_count": 1, "line_count": 1, "max_line_length": 4}, "position": {"left": 6649049, "top": 1796995, "width": 3888000, "height": 826456}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 16.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_9_text_7", "original_text": "在医疗行业，可将患者病历、检查结果等数据转换为数据组件，医护人员在书写病历、报告时可直接引用和编辑，提高医疗文书的准确性和规范性，助力医疗信息化建设。", "text_type": "content", "text_stats": {"char_count": 75, "word_count": 1, "line_count": 1, "max_line_length": 75}, "position": {"left": 6649049, "top": 3240221, "width": 3888000, "height": 1705489}, "font_info": {"font_name": "Source <PERSON>", "font_size": 14.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_9_text_10", "original_text": "行业领域应用", "text_type": "title", "text_stats": {"char_count": 6, "word_count": 1, "line_count": 1, "max_line_length": 6}, "position": {"left": 824877, "top": 330175, "width": 8405995, "height": 432000}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 32.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}], "non_text_elements": [{"shape_index": 0, "shape_type": "TEXT_BOX (17)", "position": {"left": -18288, "top": -32227, "width": 12210288, "height": 6890227}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 1, "shape_type": "LINE (9)", "position": {"left": -246743, "top": 2791737, "width": 6085888, "height": 0}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 3, "shape_type": "TEXT_BOX (17)", "position": {"left": 1339145, "top": 3007602, "width": 4500000, "height": 2160000}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 6, "shape_type": "TEXT_BOX (17)", "position": {"left": 6343049, "top": 3007602, "width": 4500000, "height": 2160000}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 8, "shape_type": "LINE (9)", "position": {"left": 6343049, "top": 2791737, "width": 6124722, "height": 0}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 9, "shape_type": "TEXT_BOX (17)", "position": {"left": 361150, "top": 186175, "width": 9232259, "height": 720000}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 11, "shape_type": "TEXT_BOX (17)", "position": {"left": 193595, "top": 330275, "width": 444500, "height": 431800}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 12, "shape_type": "FREEFORM (5)", "position": {"left": 313291, "top": 449516, "width": 205107, "height": 193318}, "is_image": false, "is_chart": false, "is_table": false}], "layout_info": {"layout_name": "布局9", "placeholders_count": 0}}, {"slide_index": 10, "slide_title": "幻灯片11", "text_elements": [{"placeholder_id": "slide_10_text_10", "original_text": "企业员工在撰写各类报告时，可利用该工具快速引用业务数据，生成专业报告，减少数据查找和整理时间，提升工作效率，使报告更具说服力和准确性。", "text_type": "content", "text_stats": {"char_count": 67, "word_count": 1, "line_count": 1, "max_line_length": 67}, "position": {"left": 1936995, "top": 3134656, "width": 3728080, "height": 1932644}, "font_info": {"font_name": "Source <PERSON>", "font_size": 14.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "CENTER (2)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_10_text_11", "original_text": "报告撰写", "text_type": "title", "text_stats": {"char_count": 4, "word_count": 1, "line_count": 1, "max_line_length": 4}, "position": {"left": 1936995, "top": 2163513, "width": 3728080, "height": 913566}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 16.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "CENTER (2)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_10_text_12", "original_text": "在合同编辑过程中，该工具可将合同条款、金额等数据转换为数据组件，方便合同条款的修改和更新，确保合同数据的准确性和一致性，降低合同风险。", "text_type": "content", "text_stats": {"char_count": 67, "word_count": 1, "line_count": 1, "max_line_length": 67}, "position": {"left": 6526924, "top": 3134656, "width": 3728080, "height": 1932644}, "font_info": {"font_name": "Source <PERSON>", "font_size": 14.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "CENTER (2)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_10_text_13", "original_text": "合同编辑", "text_type": "title", "text_stats": {"char_count": 4, "word_count": 1, "line_count": 1, "max_line_length": 4}, "position": {"left": 6526924, "top": 2163513, "width": 3728080, "height": 913566}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 16.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "CENTER (2)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_10_text_15", "original_text": "企业通用应用", "text_type": "title", "text_stats": {"char_count": 6, "word_count": 1, "line_count": 1, "max_line_length": 6}, "position": {"left": 824877, "top": 330175, "width": 8405995, "height": 432000}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 32.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}], "non_text_elements": [{"shape_index": 0, "shape_type": "TEXT_BOX (17)", "position": {"left": -18288, "top": -32227, "width": 12210288, "height": 6890227}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 1, "shape_type": "FREEFORM (5)", "position": {"left": -92440, "top": 6146988, "width": 1673663, "height": 869932}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 2, "shape_type": "TEXT_BOX (17)", "position": {"left": 203200, "top": 5511800, "width": 2527300, "height": 1346200}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 3, "shape_type": "FREEFORM (5)", "position": {"left": 11441612, "top": -354658, "width": 526038, "height": 1182117}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 4, "shape_type": "TEXT_BOX (17)", "position": {"left": 10300798, "top": 3556, "width": 1762975, "height": 939072}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 5, "shape_type": "TEXT_BOX (17)", "position": {"left": 1177049, "top": 1764295, "width": 9837902, "height": 482194}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 6, "shape_type": "FREEFORM (5)", "position": {"left": 10712500, "top": 5294564, "width": 607602, "height": 482193}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 7, "shape_type": "TEXT_BOX (17)", "position": {"left": 1722896, "top": 2057399, "width": 4156279, "height": 3282524}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 8, "shape_type": "TEXT_BOX (17)", "position": {"left": 6312825, "top": 2057399, "width": 4156279, "height": 3282524}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 9, "shape_type": "TEXT_BOX (17)", "position": {"left": 1416000, "top": 1936171, "width": 9360000, "height": 138441}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 14, "shape_type": "TEXT_BOX (17)", "position": {"left": 361150, "top": 186175, "width": 9232259, "height": 720000}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 16, "shape_type": "TEXT_BOX (17)", "position": {"left": 193595, "top": 330275, "width": 444500, "height": 431800}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 17, "shape_type": "FREEFORM (5)", "position": {"left": 313291, "top": 449516, "width": 205107, "height": 193318}, "is_image": false, "is_chart": false, "is_table": false}], "layout_info": {"layout_name": "布局10", "placeholders_count": 0}}, {"slide_index": 11, "slide_title": "幻灯片12", "text_elements": [{"placeholder_id": "slide_11_text_11", "original_text": "效率提升与优势", "text_type": "title", "text_stats": {"char_count": 7, "word_count": 1, "line_count": 1, "max_line_length": 7}, "position": {"left": 607251, "top": 3401402, "width": 5924390, "height": 2330658}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 36.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_11_text_12", "original_text": "Part", "text_type": "title", "text_stats": {"char_count": 4, "word_count": 1, "line_count": 1, "max_line_length": 4}, "position": {"left": 664546, "top": 2248451, "width": 2703140, "height": 1075448}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 72.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_11_text_13", "original_text": "04", "text_type": "title", "text_stats": {"char_count": 2, "word_count": 1, "line_count": 1, "max_line_length": 2}, "position": {"left": 2910082, "top": 945727, "width": 2003111, "height": 2378172}, "font_info": {"font_name": "OPPOSans H", "font_size": 72.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}], "non_text_elements": [{"shape_index": 0, "shape_type": "PICTURE (13)", "position": {"left": -1, "top": 0, "width": 12226974, "height": 6874042}, "is_image": true, "is_chart": false, "is_table": false}, {"shape_index": 1, "shape_type": "TEXT_BOX (17)", "position": {"left": -1, "top": 1, "width": 12226973, "height": 6874042}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 2, "shape_type": "FREEFORM (5)", "position": {"left": 744088, "top": 661141, "width": 2214690, "height": 275018}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 3, "shape_type": "FREEFORM (5)", "position": {"left": -1, "top": 6335594, "width": 934028, "height": 306789}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 4, "shape_type": "FREEFORM (5)", "position": {"left": 4323648, "top": 2634019, "width": 963038, "height": 325306}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 5, "shape_type": "FREEFORM (5)", "position": {"left": 0, "top": -2, "width": 653691, "height": 1144814}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 6, "shape_type": "TEXT_BOX (17)", "position": {"left": 6323369, "top": 3493049, "width": 3739281, "height": 3364950}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 7, "shape_type": "TEXT_BOX (17)", "position": {"left": 6792291, "top": 4365188, "width": 1272168, "height": 1144814}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 8, "shape_type": "PICTURE (13)", "position": {"left": 6582847, "top": -2, "width": 5627441, "height": 6523824}, "is_image": true, "is_chart": false, "is_table": false}, {"shape_index": 9, "shape_type": "TEXT_BOX (17)", "position": {"left": 5528074, "top": 0, "width": 2937827, "height": 2643359}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 10, "shape_type": "FREEFORM (5)", "position": {"left": 10392879, "top": 3645935, "width": 1834094, "height": 3212063}, "is_image": false, "is_chart": false, "is_table": false}], "layout_info": {"layout_name": "布局11", "placeholders_count": 0}}, {"slide_index": 12, "slide_title": "幻灯片13", "text_elements": [{"placeholder_id": "slide_12_text_2", "original_text": "通过数据组件的复用和双向转换，减少了数据录入和整理的重复工作量，使文书编制人员能够将更多精力投入到文书内容的创作和优化上，显著提高工作效率。", "text_type": "content", "text_stats": {"char_count": 70, "word_count": 1, "line_count": 1, "max_line_length": 70}, "position": {"left": 3945879, "top": 2410177, "width": 6840000, "height": 936000}, "font_info": {"font_name": "Source <PERSON>", "font_size": 14.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_12_text_3", "original_text": "减少重复工作", "text_type": "title", "text_stats": {"char_count": 6, "word_count": 1, "line_count": 1, "max_line_length": 6}, "position": {"left": 3945879, "top": 1881069, "width": 6847176, "height": 307777}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 16.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_12_text_5", "original_text": "借助系统内的专业领域数据，用户可以快速生成高质量的文书，缩短文书编制周期，满足企业快速响应市场和业务需求的要求。", "text_type": "content", "text_stats": {"char_count": 56, "word_count": 1, "line_count": 1, "max_line_length": 56}, "position": {"left": 3945879, "top": 4341996, "width": 6840000, "height": 936000}, "font_info": {"font_name": "Source <PERSON>", "font_size": 14.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_12_text_6", "original_text": "快速生成文书", "text_type": "title", "text_stats": {"char_count": 6, "word_count": 1, "line_count": 1, "max_line_length": 6}, "position": {"left": 3945879, "top": 3812887, "width": 6847176, "height": 307777}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 16.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_12_text_11", "original_text": "提升效率", "text_type": "title", "text_stats": {"char_count": 4, "word_count": 1, "line_count": 1, "max_line_length": 4}, "position": {"left": 824877, "top": 330175, "width": 8405995, "height": 432000}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 32.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}], "non_text_elements": [{"shape_index": 0, "shape_type": "TEXT_BOX (17)", "position": {"left": -18288, "top": -32227, "width": 12210288, "height": 6890227}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 1, "shape_type": "TEXT_BOX (17)", "position": {"left": 3769466, "top": 2299510, "width": 7200000, "height": 1152000}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 4, "shape_type": "TEXT_BOX (17)", "position": {"left": 3769468, "top": 4231330, "width": 7200000, "height": 1152000}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 7, "shape_type": "FREEFORM (5)", "position": {"left": 2004515, "top": 2330048, "width": 882805, "height": 2604304}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 8, "shape_type": "TEXT_BOX (17)", "position": {"left": 1209832, "top": 2569519, "width": 2125362, "height": 2125362}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 9, "shape_type": "FREEFORM (5)", "position": {"left": 1790278, "top": 3156420, "width": 964470, "height": 951561}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 10, "shape_type": "TEXT_BOX (17)", "position": {"left": 361150, "top": 186175, "width": 9232259, "height": 720000}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 12, "shape_type": "TEXT_BOX (17)", "position": {"left": 193595, "top": 330275, "width": 444500, "height": 431800}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 13, "shape_type": "FREEFORM (5)", "position": {"left": 313291, "top": 449516, "width": 205107, "height": 193318}, "is_image": false, "is_chart": false, "is_table": false}], "layout_info": {"layout_name": "布局12", "placeholders_count": 0}}, {"slide_index": 13, "slide_title": "幻灯片14", "text_elements": [{"placeholder_id": "slide_13_text_2", "original_text": "该工具在传统在线文本编辑器的基础上，创新性地实现了行业业务数据与文书数据的深度融合，填补了市场空白，为文书编辑领域带来了新的解决方案。", "text_type": "content", "text_stats": {"char_count": 67, "word_count": 1, "line_count": 1, "max_line_length": 67}, "position": {"left": 1500178, "top": 3119144, "width": 4114644, "height": 2087607}, "font_info": {"font_name": "Source <PERSON>", "font_size": 14.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "CENTER (2)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_13_text_3", "original_text": "创新性", "text_type": "title", "text_stats": {"char_count": 3, "word_count": 1, "line_count": 1, "max_line_length": 3}, "position": {"left": 1500178, "top": 2041477, "width": 4114644, "height": 766119}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 16.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "CENTER (2)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_13_text_6", "original_text": "界面友好，操作简单，保留了用户熟悉的文本编辑习惯，同时提供了强大的数据处理功能，易于上手和使用，具有广泛的市场推广潜力。", "text_type": "content", "text_stats": {"char_count": 60, "word_count": 1, "line_count": 1, "max_line_length": 60}, "position": {"left": 6542709, "top": 3114584, "width": 4114644, "height": 2087607}, "font_info": {"font_name": "Source <PERSON>", "font_size": 14.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "CENTER (2)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_13_text_7", "original_text": "易用性", "text_type": "title", "text_stats": {"char_count": 3, "word_count": 1, "line_count": 1, "max_line_length": 3}, "position": {"left": 6542709, "top": 2063813, "width": 4114644, "height": 721446}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 16.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "CENTER (2)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_13_text_10", "original_text": "竞争优势", "text_type": "title", "text_stats": {"char_count": 4, "word_count": 1, "line_count": 1, "max_line_length": 4}, "position": {"left": 824877, "top": 330175, "width": 8405995, "height": 432000}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 32.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}], "non_text_elements": [{"shape_index": 0, "shape_type": "TEXT_BOX (17)", "position": {"left": -18288, "top": -32227, "width": 12210288, "height": 6890227}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 1, "shape_type": "TEXT_BOX (17)", "position": {"left": 1239073, "top": 1989321, "width": 4636854, "height": 3525654}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 4, "shape_type": "TEXT_BOX (17)", "position": {"left": 3534641, "top": 2381011, "width": 45719, "height": 1057617}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 5, "shape_type": "TEXT_BOX (17)", "position": {"left": 6281604, "top": 1984761, "width": 4636854, "height": 3525654}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 8, "shape_type": "TEXT_BOX (17)", "position": {"left": 8577172, "top": 2381011, "width": 45719, "height": 1057617}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 9, "shape_type": "TEXT_BOX (17)", "position": {"left": 361150, "top": 186175, "width": 9232259, "height": 720000}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 11, "shape_type": "TEXT_BOX (17)", "position": {"left": 193595, "top": 330275, "width": 444500, "height": 431800}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 12, "shape_type": "FREEFORM (5)", "position": {"left": 313291, "top": 449516, "width": 205107, "height": 193318}, "is_image": false, "is_chart": false, "is_table": false}], "layout_info": {"layout_name": "布局13", "placeholders_count": 0}}, {"slide_index": 14, "slide_title": "幻灯片15", "text_elements": [{"placeholder_id": "slide_14_text_11", "original_text": "未来展望", "text_type": "title", "text_stats": {"char_count": 4, "word_count": 1, "line_count": 1, "max_line_length": 4}, "position": {"left": 607251, "top": 3401402, "width": 5924390, "height": 2330658}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 36.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_14_text_12", "original_text": "Part", "text_type": "title", "text_stats": {"char_count": 4, "word_count": 1, "line_count": 1, "max_line_length": 4}, "position": {"left": 664546, "top": 2248451, "width": 2703140, "height": 1075448}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 72.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_14_text_13", "original_text": "05", "text_type": "title", "text_stats": {"char_count": 2, "word_count": 1, "line_count": 1, "max_line_length": 2}, "position": {"left": 2910082, "top": 945727, "width": 2003111, "height": 2378172}, "font_info": {"font_name": "OPPOSans H", "font_size": 72.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}], "non_text_elements": [{"shape_index": 0, "shape_type": "PICTURE (13)", "position": {"left": -1, "top": 0, "width": 12226974, "height": 6874042}, "is_image": true, "is_chart": false, "is_table": false}, {"shape_index": 1, "shape_type": "TEXT_BOX (17)", "position": {"left": -1, "top": 1, "width": 12226973, "height": 6874042}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 2, "shape_type": "FREEFORM (5)", "position": {"left": 744088, "top": 661141, "width": 2214690, "height": 275018}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 3, "shape_type": "FREEFORM (5)", "position": {"left": -1, "top": 6335594, "width": 934028, "height": 306789}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 4, "shape_type": "FREEFORM (5)", "position": {"left": 4323648, "top": 2634019, "width": 963038, "height": 325306}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 5, "shape_type": "FREEFORM (5)", "position": {"left": 0, "top": -2, "width": 653691, "height": 1144814}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 6, "shape_type": "TEXT_BOX (17)", "position": {"left": 6323369, "top": 3493049, "width": 3739281, "height": 3364950}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 7, "shape_type": "TEXT_BOX (17)", "position": {"left": 6792291, "top": 4365188, "width": 1272168, "height": 1144814}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 8, "shape_type": "PICTURE (13)", "position": {"left": 6582847, "top": -2, "width": 5627441, "height": 6523824}, "is_image": true, "is_chart": false, "is_table": false}, {"shape_index": 9, "shape_type": "TEXT_BOX (17)", "position": {"left": 5528074, "top": 0, "width": 2937827, "height": 2643359}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 10, "shape_type": "FREEFORM (5)", "position": {"left": 10392879, "top": 3645935, "width": 1834094, "height": 3212063}, "is_image": false, "is_chart": false, "is_table": false}], "layout_info": {"layout_name": "布局14", "placeholders_count": 0}}, {"slide_index": 15, "slide_title": "幻灯片16", "text_elements": [{"placeholder_id": "slide_15_text_4", "original_text": "结合人工智能技术，为用户提供智能推荐功能，根据用户输入的内容和上下文，自动推荐相关的数据组件和文本模板，进一步提高文书编制效率。", "text_type": "content", "text_stats": {"char_count": 64, "word_count": 1, "line_count": 1, "max_line_length": 64}, "position": {"left": 769165, "top": 2134245, "width": 5198054, "height": 3521872}, "font_info": {"font_name": "Source <PERSON>", "font_size": 14.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_15_text_5", "original_text": "增加多语言支持功能，满足跨国企业和多语言环境下的文书编辑需求，拓展工具的市场应用范围，提升其国际竞争力。", "text_type": "content", "text_stats": {"char_count": 52, "word_count": 1, "line_count": 1, "max_line_length": 52}, "position": {"left": 6215709, "top": 2134245, "width": 5198054, "height": 3521872}, "font_info": {"font_name": "Source <PERSON>", "font_size": 14.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_15_text_6", "original_text": "智能推荐功能", "text_type": "title", "text_stats": {"char_count": 6, "word_count": 1, "line_count": 1, "max_line_length": 6}, "position": {"left": 756465, "top": 1564182, "width": 5198054, "height": 476525}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 16.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_15_text_7", "original_text": "多语言支持", "text_type": "title", "text_stats": {"char_count": 5, "word_count": 1, "line_count": 1, "max_line_length": 5}, "position": {"left": 6215361, "top": 1564182, "width": 5198054, "height": 463825}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 16.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_15_text_9", "original_text": "功能拓展", "text_type": "title", "text_stats": {"char_count": 4, "word_count": 1, "line_count": 1, "max_line_length": 4}, "position": {"left": 824877, "top": 330175, "width": 8405995, "height": 432000}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 32.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}], "non_text_elements": [{"shape_index": 0, "shape_type": "TEXT_BOX (17)", "position": {"left": -18288, "top": -32227, "width": 12210288, "height": 6890227}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 1, "shape_type": "TEXT_BOX (17)", "position": {"left": 0, "top": 6494578, "width": 12192000, "height": 363422}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 2, "shape_type": "TEXT_BOX (17)", "position": {"left": 706699, "top": 1564182, "width": 5354576, "height": 4447268}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 3, "shape_type": "TEXT_BOX (17)", "position": {"left": 6141174, "top": 1564182, "width": 5354576, "height": 4447268}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 8, "shape_type": "TEXT_BOX (17)", "position": {"left": 361150, "top": 186175, "width": 9232259, "height": 720000}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 10, "shape_type": "TEXT_BOX (17)", "position": {"left": 193595, "top": 330275, "width": 444500, "height": 431800}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 11, "shape_type": "FREEFORM (5)", "position": {"left": 313291, "top": 449516, "width": 205107, "height": 193318}, "is_image": false, "is_chart": false, "is_table": false}], "layout_info": {"layout_name": "布局15", "placeholders_count": 0}}, {"slide_index": 16, "slide_title": "幻灯片17", "text_elements": [{"placeholder_id": "slide_16_text_5", "original_text": "01", "text_type": "title", "text_stats": {"char_count": 2, "word_count": 1, "line_count": 1, "max_line_length": 2}, "position": {"left": 1215093, "top": 1971566, "width": 607859, "height": 523220}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 28.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_16_text_9", "original_text": "02", "text_type": "title", "text_stats": {"char_count": 2, "word_count": 1, "line_count": 1, "max_line_length": 2}, "position": {"left": 3182323, "top": 4082917, "width": 607859, "height": 523220}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 28.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_16_text_13", "original_text": "拓展更多行业", "text_type": "title", "text_stats": {"char_count": 6, "word_count": 1, "line_count": 1, "max_line_length": 6}, "position": {"left": 2108835, "top": 1910080, "width": 6802120, "height": 467123}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 16.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_16_text_14", "original_text": "持续优化和改进工具，使其能够适应更多行业领域的业务数据特点和文书编辑需求，如教育、法律等行业，推动文书编辑工具在更广泛的领域得到应用。", "text_type": "content", "text_stats": {"char_count": 67, "word_count": 1, "line_count": 1, "max_line_length": 67}, "position": {"left": 2108835, "top": 2371570, "width": 6802120, "height": 859310}, "font_info": {"font_name": "Source <PERSON>", "font_size": 14.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_16_text_15", "original_text": "行业定制化服务", "text_type": "title", "text_stats": {"char_count": 7, "word_count": 1, "line_count": 1, "max_line_length": 7}, "position": {"left": 4076065, "top": 3995240, "width": 6802120, "height": 493314}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 16.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_16_text_16", "original_text": "提供行业定制化服务，根据不同行业的特殊需求，定制专属的数据组件和文书模板，打造更具针对性和专业性的文书编辑解决方案，提升用户满意度和市场占有率。", "text_type": "content", "text_stats": {"char_count": 72, "word_count": 1, "line_count": 1, "max_line_length": 72}, "position": {"left": 4076065, "top": 4482921, "width": 6802120, "height": 859310}, "font_info": {"font_name": "Source <PERSON>", "font_size": 14.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_16_text_18", "original_text": "行业拓展", "text_type": "title", "text_stats": {"char_count": 4, "word_count": 1, "line_count": 1, "max_line_length": 4}, "position": {"left": 824877, "top": 330175, "width": 8405995, "height": 432000}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 32.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}], "non_text_elements": [{"shape_index": 0, "shape_type": "TEXT_BOX (17)", "position": {"left": -18288, "top": -32227, "width": 12210288, "height": 6890227}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 1, "shape_type": "TEXT_BOX (17)", "position": {"left": 2916555, "top": 3831651, "width": 822960, "height": 822960}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 2, "shape_type": "TEXT_BOX (17)", "position": {"left": 1006475, "top": 1667571, "width": 822960, "height": 822960}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 3, "shape_type": "TEXT_BOX (17)", "position": {"left": 1113155, "top": 1819970, "width": 8001000, "height": 1553149}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 4, "shape_type": "TEXT_BOX (17)", "position": {"left": 1097915, "top": 1819971, "width": 822960, "height": 822960}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 6, "shape_type": "FREEFORM (5)", "position": {"left": 1304424, "top": 2992262, "width": 220211, "height": 238542}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 7, "shape_type": "TEXT_BOX (17)", "position": {"left": 3080385, "top": 3931321, "width": 8001000, "height": 1553149}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 8, "shape_type": "TEXT_BOX (17)", "position": {"left": 3065145, "top": 3931322, "width": 822960, "height": 822960}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 10, "shape_type": "FREEFORM (5)", "position": {"left": 3271654, "top": 5103613, "width": 220211, "height": 238542}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 11, "shape_type": "FREEFORM (5)", "position": {"left": 8572500, "top": 2811781, "width": 590984, "height": 626769}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 12, "shape_type": "FREEFORM (5)", "position": {"left": 10553700, "top": 4922521, "width": 590984, "height": 626769}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 17, "shape_type": "TEXT_BOX (17)", "position": {"left": 361150, "top": 186175, "width": 9232259, "height": 720000}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 19, "shape_type": "TEXT_BOX (17)", "position": {"left": 193595, "top": 330275, "width": 444500, "height": 431800}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 20, "shape_type": "FREEFORM (5)", "position": {"left": 313291, "top": 449516, "width": 205107, "height": 193318}, "is_image": false, "is_chart": false, "is_table": false}], "layout_info": {"layout_name": "布局16", "placeholders_count": 0}}, {"slide_index": 17, "slide_title": "幻灯片18", "text_elements": [{"placeholder_id": "slide_17_text_2", "original_text": "主讲人：", "text_type": "title", "text_stats": {"char_count": 4, "word_count": 1, "line_count": 1, "max_line_length": 4}, "position": {"left": 1353391, "top": 5514590, "width": 920911, "height": 317779}, "font_info": {"font_name": "Source <PERSON>", "font_size": 18.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "CENTER (2)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_17_text_3", "original_text": "AiPPT", "text_type": "title", "text_stats": {"char_count": 5, "word_count": 1, "line_count": 1, "max_line_length": 5}, "position": {"left": 2246841, "top": 5488813, "width": 897908, "height": 369332}, "font_info": {"font_name": "OPPOSans H", "font_size": 18.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_17_text_6", "original_text": "时间：", "text_type": "title", "text_stats": {"char_count": 3, "word_count": 1, "line_count": 1, "max_line_length": 3}, "position": {"left": 4014385, "top": 5554537, "width": 920911, "height": 237884}, "font_info": {"font_name": "Source <PERSON>", "font_size": 18.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "CENTER (2)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_17_text_7", "original_text": "2025.7", "text_type": "title", "text_stats": {"char_count": 6, "word_count": 1, "line_count": 1, "max_line_length": 6}, "position": {"left": 4692336, "top": 5488813, "width": 985247, "height": 369332}, "font_info": {"font_name": "OPPOSans H", "font_size": 9.79, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_17_text_15", "original_text": "PowerPoint design", "text_type": "title", "text_stats": {"char_count": 17, "word_count": 2, "line_count": 1, "max_line_length": 17}, "position": {"left": 889117, "top": 4569554, "width": 3000495, "height": 461665}, "font_info": {"font_name": "Source <PERSON>", "font_size": 20.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_17_text_22", "original_text": "202X", "text_type": "title", "text_stats": {"char_count": 4, "word_count": 1, "line_count": 1, "max_line_length": 4}, "position": {"left": 601804, "top": 1378424, "width": 3741482, "height": 1428188}, "font_info": {"font_name": "Source Han Sans CN Bold", "font_size": 96.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}, {"placeholder_id": "slide_17_text_23", "original_text": "谢谢大家", "text_type": "title", "text_stats": {"char_count": 4, "word_count": 1, "line_count": 1, "max_line_length": 4}, "position": {"left": 607303, "top": 2611521, "width": 6153108, "height": 1791367}, "font_info": {"font_name": "OPPOSans H", "font_size": 36.0, "font_bold": null, "font_italic": null, "font_color": null, "alignment": "LEFT (1)"}, "shape_properties": {"shape_type": "TEXT_BOX (17)", "has_text_frame": true, "auto_size": null}}], "non_text_elements": [{"shape_index": 0, "shape_type": "PICTURE (13)", "position": {"left": -1, "top": 0, "width": 12226974, "height": 6874042}, "is_image": true, "is_chart": false, "is_table": false}, {"shape_index": 1, "shape_type": "TEXT_BOX (17)", "position": {"left": -1, "top": 1, "width": 12226973, "height": 6874042}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 4, "shape_type": "TEXT_BOX (17)", "position": {"left": 724530, "top": 5431959, "width": 483041, "height": 483041}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 5, "shape_type": "FREEFORM (5)", "position": {"left": 865787, "top": 5557699, "width": 213768, "height": 231561}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 8, "shape_type": "TEXT_BOX (17)", "position": {"left": 3443940, "top": 5431959, "width": 483041, "height": 483041}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 9, "shape_type": "FREEFORM (5)", "position": {"left": 3580836, "top": 5566311, "width": 227407, "height": 214336}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 10, "shape_type": "FREEFORM (5)", "position": {"left": 731388, "top": 661141, "width": 2214690, "height": 275018}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 11, "shape_type": "FREEFORM (5)", "position": {"left": -1, "top": 6335594, "width": 934028, "height": 306789}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 12, "shape_type": "FREEFORM (5)", "position": {"left": 3996102, "top": 1968227, "width": 930740, "height": 314396}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 13, "shape_type": "FREEFORM (5)", "position": {"left": 0, "top": -2, "width": 653691, "height": 1144814}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 14, "shape_type": "TEXT_BOX (17)", "position": {"left": 744088, "top": 4546391, "width": 5259703, "height": 507990}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 16, "shape_type": "TEXT_BOX (17)", "position": {"left": 6323369, "top": 3493049, "width": 3739281, "height": 3364950}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 17, "shape_type": "TEXT_BOX (17)", "position": {"left": 6792291, "top": 4365188, "width": 1272168, "height": 1144814}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 18, "shape_type": "PICTURE (13)", "position": {"left": 6582847, "top": -2, "width": 5627441, "height": 6523824}, "is_image": true, "is_chart": false, "is_table": false}, {"shape_index": 19, "shape_type": "TEXT_BOX (17)", "position": {"left": 5528074, "top": 0, "width": 2937827, "height": 2643359}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 20, "shape_type": "FREEFORM (5)", "position": {"left": 10392879, "top": 3645935, "width": 1834094, "height": 3212063}, "is_image": false, "is_chart": false, "is_table": false}, {"shape_index": 21, "shape_type": "LINE (9)", "position": {"left": 3411940, "top": 4800386, "width": 2088108, "height": 0}, "is_image": false, "is_chart": false, "is_table": false}], "layout_info": {"layout_name": "布局17", "placeholders_count": 0}}], "text_structure_summary": {"total_text_elements": 94, "text_types_distribution": {"title": 74, "content": 20}, "average_text_length": {"title": 4, "content": 63}, "slides_structure": [{"slide_index": 0, "text_elements_count": 7, "text_types": [{"placeholder_id": "slide_0_text_2", "text_type": "title", "char_count": 4}, {"placeholder_id": "slide_0_text_3", "text_type": "title", "char_count": 5}, {"placeholder_id": "slide_0_text_6", "text_type": "title", "char_count": 3}, {"placeholder_id": "slide_0_text_7", "text_type": "title", "char_count": 6}, {"placeholder_id": "slide_0_text_15", "text_type": "title", "char_count": 17}, {"placeholder_id": "slide_0_text_22", "text_type": "title", "char_count": 4}, {"placeholder_id": "slide_0_text_23", "text_type": "title", "char_count": 13}]}, {"slide_index": 1, "text_elements_count": 11, "text_types": [{"placeholder_id": "slide_1_text_6", "text_type": "title", "char_count": 2}, {"placeholder_id": "slide_1_text_7", "text_type": "title", "char_count": 4}, {"placeholder_id": "slide_1_text_8", "text_type": "title", "char_count": 2}, {"placeholder_id": "slide_1_text_17", "text_type": "title", "char_count": 4}, {"placeholder_id": "slide_1_text_18", "text_type": "title", "char_count": 2}, {"placeholder_id": "slide_1_text_19", "text_type": "title", "char_count": 4}, {"placeholder_id": "slide_1_text_20", "text_type": "title", "char_count": 2}, {"placeholder_id": "slide_1_text_21", "text_type": "title", "char_count": 7}, {"placeholder_id": "slide_1_text_22", "text_type": "title", "char_count": 2}, {"placeholder_id": "slide_1_text_23", "text_type": "title", "char_count": 4}, {"placeholder_id": "slide_1_text_24", "text_type": "title", "char_count": 2}]}, {"slide_index": 2, "text_elements_count": 3, "text_types": [{"placeholder_id": "slide_2_text_11", "text_type": "title", "char_count": 4}, {"placeholder_id": "slide_2_text_12", "text_type": "title", "char_count": 4}, {"placeholder_id": "slide_2_text_13", "text_type": "title", "char_count": 2}]}, {"slide_index": 3, "text_elements_count": 5, "text_types": [{"placeholder_id": "slide_3_text_4", "text_type": "title", "char_count": 4}, {"placeholder_id": "slide_3_text_6", "text_type": "content", "char_count": 76}, {"placeholder_id": "slide_3_text_7", "text_type": "title", "char_count": 4}, {"placeholder_id": "slide_3_text_8", "text_type": "content", "char_count": 61}, {"placeholder_id": "slide_3_text_18", "text_type": "title", "char_count": 7}]}, {"slide_index": 4, "text_elements_count": 3, "text_types": [{"placeholder_id": "slide_4_text_11", "text_type": "title", "char_count": 4}, {"placeholder_id": "slide_4_text_12", "text_type": "title", "char_count": 4}, {"placeholder_id": "slide_4_text_13", "text_type": "title", "char_count": 2}]}, {"slide_index": 5, "text_elements_count": 5, "text_types": [{"placeholder_id": "slide_5_text_3", "text_type": "content", "char_count": 59}, {"placeholder_id": "slide_5_text_4", "text_type": "title", "char_count": 4}, {"placeholder_id": "slide_5_text_6", "text_type": "content", "char_count": 55}, {"placeholder_id": "slide_5_text_7", "text_type": "title", "char_count": 4}, {"placeholder_id": "slide_5_text_11", "text_type": "title", "char_count": 6}]}, {"slide_index": 6, "text_elements_count": 7, "text_types": [{"placeholder_id": "slide_6_text_3", "text_type": "content", "char_count": 53}, {"placeholder_id": "slide_6_text_4", "text_type": "title", "char_count": 6}, {"placeholder_id": "slide_6_text_7", "text_type": "content", "char_count": 66}, {"placeholder_id": "slide_6_text_8", "text_type": "title", "char_count": 9}, {"placeholder_id": "slide_6_text_15", "text_type": "title", "char_count": 2}, {"placeholder_id": "slide_6_text_16", "text_type": "title", "char_count": 2}, {"placeholder_id": "slide_6_text_18", "text_type": "title", "char_count": 6}]}, {"slide_index": 7, "text_elements_count": 5, "text_types": [{"placeholder_id": "slide_7_text_8", "text_type": "content", "char_count": 64}, {"placeholder_id": "slide_7_text_9", "text_type": "title", "char_count": 11}, {"placeholder_id": "slide_7_text_10", "text_type": "content", "char_count": 51}, {"placeholder_id": "slide_7_text_11", "text_type": "title", "char_count": 6}, {"placeholder_id": "slide_7_text_13", "text_type": "title", "char_count": 6}]}, {"slide_index": 8, "text_elements_count": 3, "text_types": [{"placeholder_id": "slide_8_text_11", "text_type": "title", "char_count": 4}, {"placeholder_id": "slide_8_text_12", "text_type": "title", "char_count": 4}, {"placeholder_id": "slide_8_text_13", "text_type": "title", "char_count": 2}]}, {"slide_index": 9, "text_elements_count": 5, "text_types": [{"placeholder_id": "slide_9_text_2", "text_type": "title", "char_count": 4}, {"placeholder_id": "slide_9_text_4", "text_type": "content", "char_count": 65}, {"placeholder_id": "slide_9_text_5", "text_type": "title", "char_count": 4}, {"placeholder_id": "slide_9_text_7", "text_type": "content", "char_count": 75}, {"placeholder_id": "slide_9_text_10", "text_type": "title", "char_count": 6}]}, {"slide_index": 10, "text_elements_count": 5, "text_types": [{"placeholder_id": "slide_10_text_10", "text_type": "content", "char_count": 67}, {"placeholder_id": "slide_10_text_11", "text_type": "title", "char_count": 4}, {"placeholder_id": "slide_10_text_12", "text_type": "content", "char_count": 67}, {"placeholder_id": "slide_10_text_13", "text_type": "title", "char_count": 4}, {"placeholder_id": "slide_10_text_15", "text_type": "title", "char_count": 6}]}, {"slide_index": 11, "text_elements_count": 3, "text_types": [{"placeholder_id": "slide_11_text_11", "text_type": "title", "char_count": 7}, {"placeholder_id": "slide_11_text_12", "text_type": "title", "char_count": 4}, {"placeholder_id": "slide_11_text_13", "text_type": "title", "char_count": 2}]}, {"slide_index": 12, "text_elements_count": 5, "text_types": [{"placeholder_id": "slide_12_text_2", "text_type": "content", "char_count": 70}, {"placeholder_id": "slide_12_text_3", "text_type": "title", "char_count": 6}, {"placeholder_id": "slide_12_text_5", "text_type": "content", "char_count": 56}, {"placeholder_id": "slide_12_text_6", "text_type": "title", "char_count": 6}, {"placeholder_id": "slide_12_text_11", "text_type": "title", "char_count": 4}]}, {"slide_index": 13, "text_elements_count": 5, "text_types": [{"placeholder_id": "slide_13_text_2", "text_type": "content", "char_count": 67}, {"placeholder_id": "slide_13_text_3", "text_type": "title", "char_count": 3}, {"placeholder_id": "slide_13_text_6", "text_type": "content", "char_count": 60}, {"placeholder_id": "slide_13_text_7", "text_type": "title", "char_count": 3}, {"placeholder_id": "slide_13_text_10", "text_type": "title", "char_count": 4}]}, {"slide_index": 14, "text_elements_count": 3, "text_types": [{"placeholder_id": "slide_14_text_11", "text_type": "title", "char_count": 4}, {"placeholder_id": "slide_14_text_12", "text_type": "title", "char_count": 4}, {"placeholder_id": "slide_14_text_13", "text_type": "title", "char_count": 2}]}, {"slide_index": 15, "text_elements_count": 5, "text_types": [{"placeholder_id": "slide_15_text_4", "text_type": "content", "char_count": 64}, {"placeholder_id": "slide_15_text_5", "text_type": "content", "char_count": 52}, {"placeholder_id": "slide_15_text_6", "text_type": "title", "char_count": 6}, {"placeholder_id": "slide_15_text_7", "text_type": "title", "char_count": 5}, {"placeholder_id": "slide_15_text_9", "text_type": "title", "char_count": 4}]}, {"slide_index": 16, "text_elements_count": 7, "text_types": [{"placeholder_id": "slide_16_text_5", "text_type": "title", "char_count": 2}, {"placeholder_id": "slide_16_text_9", "text_type": "title", "char_count": 2}, {"placeholder_id": "slide_16_text_13", "text_type": "title", "char_count": 6}, {"placeholder_id": "slide_16_text_14", "text_type": "content", "char_count": 67}, {"placeholder_id": "slide_16_text_15", "text_type": "title", "char_count": 7}, {"placeholder_id": "slide_16_text_16", "text_type": "content", "char_count": 72}, {"placeholder_id": "slide_16_text_18", "text_type": "title", "char_count": 4}]}, {"slide_index": 17, "text_elements_count": 7, "text_types": [{"placeholder_id": "slide_17_text_2", "text_type": "title", "char_count": 4}, {"placeholder_id": "slide_17_text_3", "text_type": "title", "char_count": 5}, {"placeholder_id": "slide_17_text_6", "text_type": "title", "char_count": 3}, {"placeholder_id": "slide_17_text_7", "text_type": "title", "char_count": 6}, {"placeholder_id": "slide_17_text_15", "text_type": "title", "char_count": 17}, {"placeholder_id": "slide_17_text_22", "text_type": "title", "char_count": 4}, {"placeholder_id": "slide_17_text_23", "text_type": "title", "char_count": 4}]}]}, "generation_requirements": {"total_slides_needed": 18, "slides_requirements": [{"slide_index": 0, "required_text_elements": [{"placeholder_id": "slide_0_text_2", "text_type": "title", "recommended_char_count": 4, "char_range": {"min": 1, "max": 24}, "original_example": "主讲人："}, {"placeholder_id": "slide_0_text_3", "text_type": "title", "recommended_char_count": 5, "char_range": {"min": 1, "max": 25}, "original_example": "AiPPT"}, {"placeholder_id": "slide_0_text_6", "text_type": "title", "recommended_char_count": 3, "char_range": {"min": 1, "max": 23}, "original_example": "时间："}, {"placeholder_id": "slide_0_text_7", "text_type": "title", "recommended_char_count": 6, "char_range": {"min": 1, "max": 26}, "original_example": "2025.7"}, {"placeholder_id": "slide_0_text_15", "text_type": "title", "recommended_char_count": 17, "char_range": {"min": 1, "max": 37}, "original_example": "PowerPoint design"}, {"placeholder_id": "slide_0_text_22", "text_type": "title", "recommended_char_count": 4, "char_range": {"min": 1, "max": 24}, "original_example": "202X"}, {"placeholder_id": "slide_0_text_23", "text_type": "title", "recommended_char_count": 13, "char_range": {"min": 1, "max": 33}, "original_example": "基于数据组件的文书编辑工具"}]}, {"slide_index": 1, "required_text_elements": [{"placeholder_id": "slide_1_text_6", "text_type": "title", "recommended_char_count": 2, "char_range": {"min": 1, "max": 22}, "original_example": "目录"}, {"placeholder_id": "slide_1_text_7", "text_type": "title", "recommended_char_count": 4, "char_range": {"min": 1, "max": 24}, "original_example": "工具概述"}, {"placeholder_id": "slide_1_text_8", "text_type": "title", "recommended_char_count": 2, "char_range": {"min": 1, "max": 22}, "original_example": "01"}, {"placeholder_id": "slide_1_text_17", "text_type": "title", "recommended_char_count": 4, "char_range": {"min": 1, "max": 24}, "original_example": "功能特点"}, {"placeholder_id": "slide_1_text_18", "text_type": "title", "recommended_char_count": 2, "char_range": {"min": 1, "max": 22}, "original_example": "02"}, {"placeholder_id": "slide_1_text_19", "text_type": "title", "recommended_char_count": 4, "char_range": {"min": 1, "max": 24}, "original_example": "应用场景"}, {"placeholder_id": "slide_1_text_20", "text_type": "title", "recommended_char_count": 2, "char_range": {"min": 1, "max": 22}, "original_example": "03"}, {"placeholder_id": "slide_1_text_21", "text_type": "title", "recommended_char_count": 7, "char_range": {"min": 1, "max": 27}, "original_example": "效率提升与优势"}, {"placeholder_id": "slide_1_text_22", "text_type": "title", "recommended_char_count": 2, "char_range": {"min": 1, "max": 22}, "original_example": "04"}, {"placeholder_id": "slide_1_text_23", "text_type": "title", "recommended_char_count": 4, "char_range": {"min": 1, "max": 24}, "original_example": "未来展望"}, {"placeholder_id": "slide_1_text_24", "text_type": "title", "recommended_char_count": 2, "char_range": {"min": 1, "max": 22}, "original_example": "05"}]}, {"slide_index": 2, "required_text_elements": [{"placeholder_id": "slide_2_text_11", "text_type": "title", "recommended_char_count": 4, "char_range": {"min": 1, "max": 24}, "original_example": "工具概述"}, {"placeholder_id": "slide_2_text_12", "text_type": "title", "recommended_char_count": 4, "char_range": {"min": 1, "max": 24}, "original_example": "Part"}, {"placeholder_id": "slide_2_text_13", "text_type": "title", "recommended_char_count": 2, "char_range": {"min": 1, "max": 22}, "original_example": "01"}]}, {"slide_index": 3, "required_text_elements": [{"placeholder_id": "slide_3_text_4", "text_type": "title", "recommended_char_count": 4, "char_range": {"min": 1, "max": 24}, "original_example": "主要目标"}, {"placeholder_id": "slide_3_text_6", "text_type": "content", "recommended_char_count": 76, "char_range": {"min": 56, "max": 96}, "original_example": "本工具将行业业务数据转换为可编辑的数据组件，结合传统在线文本编辑器功能，形成在线文档编辑工具，旨在解..."}, {"placeholder_id": "slide_3_text_7", "text_type": "title", "recommended_char_count": 4, "char_range": {"min": 1, "max": 24}, "original_example": "核心定义"}, {"placeholder_id": "slide_3_text_8", "text_type": "content", "recommended_char_count": 61, "char_range": {"min": 41, "max": 81}, "original_example": "实现行业业务数据与文书数据的双向转换，减少对既有文书工作习惯的干扰，借助系统内专业领域数据辅助文书制..."}, {"placeholder_id": "slide_3_text_18", "text_type": "title", "recommended_char_count": 7, "char_range": {"min": 1, "max": 27}, "original_example": "工具定义与目标"}]}, {"slide_index": 4, "required_text_elements": [{"placeholder_id": "slide_4_text_11", "text_type": "title", "recommended_char_count": 4, "char_range": {"min": 1, "max": 24}, "original_example": "功能特点"}, {"placeholder_id": "slide_4_text_12", "text_type": "title", "recommended_char_count": 4, "char_range": {"min": 1, "max": 24}, "original_example": "Part"}, {"placeholder_id": "slide_4_text_13", "text_type": "title", "recommended_char_count": 2, "char_range": {"min": 1, "max": 22}, "original_example": "02"}]}, {"slide_index": 5, "required_text_elements": [{"placeholder_id": "slide_5_text_3", "text_type": "content", "recommended_char_count": 59, "char_range": {"min": 39, "max": 79}, "original_example": "通过特定算法和规则，将行业业务数据精准转换为可编辑的数据组件，确保数据的完整性和准确性，为文书编辑提..."}, {"placeholder_id": "slide_5_text_4", "text_type": "title", "recommended_char_count": 4, "char_range": {"min": 1, "max": 24}, "original_example": "转换机制"}, {"placeholder_id": "slide_5_text_6", "text_type": "content", "recommended_char_count": 55, "char_range": {"min": 35, "max": 75}, "original_example": "实现数据的灵活复用，避免重复录入，提高工作效率，同时保证数据的一致性和实时性，减少因数据更新不及时导..."}, {"placeholder_id": "slide_5_text_7", "text_type": "title", "recommended_char_count": 4, "char_range": {"min": 1, "max": 24}, "original_example": "转换优势"}, {"placeholder_id": "slide_5_text_11", "text_type": "title", "recommended_char_count": 6, "char_range": {"min": 1, "max": 26}, "original_example": "数据组件转换"}]}, {"slide_index": 6, "required_text_elements": [{"placeholder_id": "slide_6_text_3", "text_type": "content", "recommended_char_count": 53, "char_range": {"min": 33, "max": 73}, "original_example": "保留传统在线文本编辑器的字体、样式调整等常规功能，满足用户日常文书编辑需求，降低用户的学习成本和使用..."}, {"placeholder_id": "slide_6_text_4", "text_type": "title", "recommended_char_count": 6, "char_range": {"min": 1, "max": 26}, "original_example": "常规编辑功能"}, {"placeholder_id": "slide_6_text_7", "text_type": "content", "recommended_char_count": 66, "char_range": {"min": 46, "max": 86}, "original_example": "支持在文书正文中直接引用和编辑数据组件，用户可以像编辑普通文本一样操作数据组件，实现数据与文本的无缝..."}, {"placeholder_id": "slide_6_text_8", "text_type": "title", "recommended_char_count": 9, "char_range": {"min": 1, "max": 29}, "original_example": "数据组件引用与编辑"}, {"placeholder_id": "slide_6_text_15", "text_type": "title", "recommended_char_count": 2, "char_range": {"min": 1, "max": 22}, "original_example": "01"}, {"placeholder_id": "slide_6_text_16", "text_type": "title", "recommended_char_count": 2, "char_range": {"min": 1, "max": 22}, "original_example": "02"}, {"placeholder_id": "slide_6_text_18", "text_type": "title", "recommended_char_count": 6, "char_range": {"min": 1, "max": 26}, "original_example": "文书编辑功能"}]}, {"slide_index": 7, "required_text_elements": [{"placeholder_id": "slide_7_text_8", "text_type": "content", "recommended_char_count": 64, "char_range": {"min": 44, "max": 84}, "original_example": "实现文书数据与系统数据的双向转换，用户在编辑文书时，系统数据可以实时更新，反之亦然，确保数据的实时性..."}, {"placeholder_id": "slide_7_text_9", "text_type": "title", "recommended_char_count": 11, "char_range": {"min": 1, "max": 31}, "original_example": "文书数据与系统数据同步"}, {"placeholder_id": "slide_7_text_10", "text_type": "content", "recommended_char_count": 51, "char_range": {"min": 31, "max": 71}, "original_example": "在数据双向转换过程中，采用加密算法和权限管理机制，确保数据的安全性和保密性，防止数据泄露和未授权访问..."}, {"placeholder_id": "slide_7_text_11", "text_type": "title", "recommended_char_count": 6, "char_range": {"min": 1, "max": 26}, "original_example": "转换安全保障"}, {"placeholder_id": "slide_7_text_13", "text_type": "title", "recommended_char_count": 6, "char_range": {"min": 1, "max": 26}, "original_example": "数据双向转换"}]}, {"slide_index": 8, "required_text_elements": [{"placeholder_id": "slide_8_text_11", "text_type": "title", "recommended_char_count": 4, "char_range": {"min": 1, "max": 24}, "original_example": "应用场景"}, {"placeholder_id": "slide_8_text_12", "text_type": "title", "recommended_char_count": 4, "char_range": {"min": 1, "max": 24}, "original_example": "Part"}, {"placeholder_id": "slide_8_text_13", "text_type": "title", "recommended_char_count": 2, "char_range": {"min": 1, "max": 22}, "original_example": "03"}]}, {"slide_index": 9, "required_text_elements": [{"placeholder_id": "slide_9_text_2", "text_type": "title", "recommended_char_count": 4, "char_range": {"min": 1, "max": 24}, "original_example": "金融行业"}, {"placeholder_id": "slide_9_text_4", "text_type": "content", "recommended_char_count": 65, "char_range": {"min": 45, "max": 85}, "original_example": "在金融行业，该工具可以将客户信息、交易数据等转换为数据组件，方便金融文档的快速生成和编辑，提高金融业..."}, {"placeholder_id": "slide_9_text_5", "text_type": "title", "recommended_char_count": 4, "char_range": {"min": 1, "max": 24}, "original_example": "医疗行业"}, {"placeholder_id": "slide_9_text_7", "text_type": "content", "recommended_char_count": 75, "char_range": {"min": 55, "max": 95}, "original_example": "在医疗行业，可将患者病历、检查结果等数据转换为数据组件，医护人员在书写病历、报告时可直接引用和编辑，..."}, {"placeholder_id": "slide_9_text_10", "text_type": "title", "recommended_char_count": 6, "char_range": {"min": 1, "max": 26}, "original_example": "行业领域应用"}]}, {"slide_index": 10, "required_text_elements": [{"placeholder_id": "slide_10_text_10", "text_type": "content", "recommended_char_count": 67, "char_range": {"min": 47, "max": 87}, "original_example": "企业员工在撰写各类报告时，可利用该工具快速引用业务数据，生成专业报告，减少数据查找和整理时间，提升工..."}, {"placeholder_id": "slide_10_text_11", "text_type": "title", "recommended_char_count": 4, "char_range": {"min": 1, "max": 24}, "original_example": "报告撰写"}, {"placeholder_id": "slide_10_text_12", "text_type": "content", "recommended_char_count": 67, "char_range": {"min": 47, "max": 87}, "original_example": "在合同编辑过程中，该工具可将合同条款、金额等数据转换为数据组件，方便合同条款的修改和更新，确保合同数..."}, {"placeholder_id": "slide_10_text_13", "text_type": "title", "recommended_char_count": 4, "char_range": {"min": 1, "max": 24}, "original_example": "合同编辑"}, {"placeholder_id": "slide_10_text_15", "text_type": "title", "recommended_char_count": 6, "char_range": {"min": 1, "max": 26}, "original_example": "企业通用应用"}]}, {"slide_index": 11, "required_text_elements": [{"placeholder_id": "slide_11_text_11", "text_type": "title", "recommended_char_count": 7, "char_range": {"min": 1, "max": 27}, "original_example": "效率提升与优势"}, {"placeholder_id": "slide_11_text_12", "text_type": "title", "recommended_char_count": 4, "char_range": {"min": 1, "max": 24}, "original_example": "Part"}, {"placeholder_id": "slide_11_text_13", "text_type": "title", "recommended_char_count": 2, "char_range": {"min": 1, "max": 22}, "original_example": "04"}]}, {"slide_index": 12, "required_text_elements": [{"placeholder_id": "slide_12_text_2", "text_type": "content", "recommended_char_count": 70, "char_range": {"min": 50, "max": 90}, "original_example": "通过数据组件的复用和双向转换，减少了数据录入和整理的重复工作量，使文书编制人员能够将更多精力投入到文..."}, {"placeholder_id": "slide_12_text_3", "text_type": "title", "recommended_char_count": 6, "char_range": {"min": 1, "max": 26}, "original_example": "减少重复工作"}, {"placeholder_id": "slide_12_text_5", "text_type": "content", "recommended_char_count": 56, "char_range": {"min": 36, "max": 76}, "original_example": "借助系统内的专业领域数据，用户可以快速生成高质量的文书，缩短文书编制周期，满足企业快速响应市场和业务..."}, {"placeholder_id": "slide_12_text_6", "text_type": "title", "recommended_char_count": 6, "char_range": {"min": 1, "max": 26}, "original_example": "快速生成文书"}, {"placeholder_id": "slide_12_text_11", "text_type": "title", "recommended_char_count": 4, "char_range": {"min": 1, "max": 24}, "original_example": "提升效率"}]}, {"slide_index": 13, "required_text_elements": [{"placeholder_id": "slide_13_text_2", "text_type": "content", "recommended_char_count": 67, "char_range": {"min": 47, "max": 87}, "original_example": "该工具在传统在线文本编辑器的基础上，创新性地实现了行业业务数据与文书数据的深度融合，填补了市场空白，..."}, {"placeholder_id": "slide_13_text_3", "text_type": "title", "recommended_char_count": 3, "char_range": {"min": 1, "max": 23}, "original_example": "创新性"}, {"placeholder_id": "slide_13_text_6", "text_type": "content", "recommended_char_count": 60, "char_range": {"min": 40, "max": 80}, "original_example": "界面友好，操作简单，保留了用户熟悉的文本编辑习惯，同时提供了强大的数据处理功能，易于上手和使用，具有..."}, {"placeholder_id": "slide_13_text_7", "text_type": "title", "recommended_char_count": 3, "char_range": {"min": 1, "max": 23}, "original_example": "易用性"}, {"placeholder_id": "slide_13_text_10", "text_type": "title", "recommended_char_count": 4, "char_range": {"min": 1, "max": 24}, "original_example": "竞争优势"}]}, {"slide_index": 14, "required_text_elements": [{"placeholder_id": "slide_14_text_11", "text_type": "title", "recommended_char_count": 4, "char_range": {"min": 1, "max": 24}, "original_example": "未来展望"}, {"placeholder_id": "slide_14_text_12", "text_type": "title", "recommended_char_count": 4, "char_range": {"min": 1, "max": 24}, "original_example": "Part"}, {"placeholder_id": "slide_14_text_13", "text_type": "title", "recommended_char_count": 2, "char_range": {"min": 1, "max": 22}, "original_example": "05"}]}, {"slide_index": 15, "required_text_elements": [{"placeholder_id": "slide_15_text_4", "text_type": "content", "recommended_char_count": 64, "char_range": {"min": 44, "max": 84}, "original_example": "结合人工智能技术，为用户提供智能推荐功能，根据用户输入的内容和上下文，自动推荐相关的数据组件和文本模..."}, {"placeholder_id": "slide_15_text_5", "text_type": "content", "recommended_char_count": 52, "char_range": {"min": 32, "max": 72}, "original_example": "增加多语言支持功能，满足跨国企业和多语言环境下的文书编辑需求，拓展工具的市场应用范围，提升其国际竞争..."}, {"placeholder_id": "slide_15_text_6", "text_type": "title", "recommended_char_count": 6, "char_range": {"min": 1, "max": 26}, "original_example": "智能推荐功能"}, {"placeholder_id": "slide_15_text_7", "text_type": "title", "recommended_char_count": 5, "char_range": {"min": 1, "max": 25}, "original_example": "多语言支持"}, {"placeholder_id": "slide_15_text_9", "text_type": "title", "recommended_char_count": 4, "char_range": {"min": 1, "max": 24}, "original_example": "功能拓展"}]}, {"slide_index": 16, "required_text_elements": [{"placeholder_id": "slide_16_text_5", "text_type": "title", "recommended_char_count": 2, "char_range": {"min": 1, "max": 22}, "original_example": "01"}, {"placeholder_id": "slide_16_text_9", "text_type": "title", "recommended_char_count": 2, "char_range": {"min": 1, "max": 22}, "original_example": "02"}, {"placeholder_id": "slide_16_text_13", "text_type": "title", "recommended_char_count": 6, "char_range": {"min": 1, "max": 26}, "original_example": "拓展更多行业"}, {"placeholder_id": "slide_16_text_14", "text_type": "content", "recommended_char_count": 67, "char_range": {"min": 47, "max": 87}, "original_example": "持续优化和改进工具，使其能够适应更多行业领域的业务数据特点和文书编辑需求，如教育、法律等行业，推动文..."}, {"placeholder_id": "slide_16_text_15", "text_type": "title", "recommended_char_count": 7, "char_range": {"min": 1, "max": 27}, "original_example": "行业定制化服务"}, {"placeholder_id": "slide_16_text_16", "text_type": "content", "recommended_char_count": 72, "char_range": {"min": 52, "max": 92}, "original_example": "提供行业定制化服务，根据不同行业的特殊需求，定制专属的数据组件和文书模板，打造更具针对性和专业性的文..."}, {"placeholder_id": "slide_16_text_18", "text_type": "title", "recommended_char_count": 4, "char_range": {"min": 1, "max": 24}, "original_example": "行业拓展"}]}, {"slide_index": 17, "required_text_elements": [{"placeholder_id": "slide_17_text_2", "text_type": "title", "recommended_char_count": 4, "char_range": {"min": 1, "max": 24}, "original_example": "主讲人："}, {"placeholder_id": "slide_17_text_3", "text_type": "title", "recommended_char_count": 5, "char_range": {"min": 1, "max": 25}, "original_example": "AiPPT"}, {"placeholder_id": "slide_17_text_6", "text_type": "title", "recommended_char_count": 3, "char_range": {"min": 1, "max": 23}, "original_example": "时间："}, {"placeholder_id": "slide_17_text_7", "text_type": "title", "recommended_char_count": 6, "char_range": {"min": 1, "max": 26}, "original_example": "2025.7"}, {"placeholder_id": "slide_17_text_15", "text_type": "title", "recommended_char_count": 17, "char_range": {"min": 1, "max": 37}, "original_example": "PowerPoint design"}, {"placeholder_id": "slide_17_text_22", "text_type": "title", "recommended_char_count": 4, "char_range": {"min": 1, "max": 24}, "original_example": "202X"}, {"placeholder_id": "slide_17_text_23", "text_type": "title", "recommended_char_count": 4, "char_range": {"min": 1, "max": 24}, "original_example": "谢谢大家"}]}], "content_constraints": {"total_slides": 18, "text_length_constraints": {"title": {"min": 2, "max": 17, "examples": [4, 5, 3, 6, 17, 4, 13, 2, 4, 2, 4, 2, 4, 2, 7, 2, 4, 2, 4, 4, 2, 4, 4, 7, 4, 4, 2, 4, 4, 6, 6, 9, 2, 2, 6, 11, 6, 6, 4, 4, 2, 4, 4, 6, 4, 4, 6, 7, 4, 2, 6, 6, 4, 3, 3, 4, 4, 4, 2, 6, 5, 4, 2, 2, 6, 7, 4, 4, 5, 3, 6, 17, 4, 4]}, "content": {"min": 51, "max": 76, "examples": [76, 61, 59, 55, 53, 66, 64, 51, 65, 75, 67, 67, 70, 56, 67, 60, 64, 52, 67, 72]}}}, "generation_prompt": "请根据以下PPT模板结构要求生成内容：\n\n总页数：18页\n\n每页内容要求：\n第1页：\n  - title：1-24字\n  - title：1-25字\n  - title：1-23字\n  - title：1-26字\n  - title：1-37字\n  - title：1-24字\n  - title：1-33字\n\n第2页：\n  - title：1-22字\n  - title：1-24字\n  - title：1-22字\n  - title：1-24字\n  - title：1-22字\n  - title：1-24字\n  - title：1-22字\n  - title：1-27字\n  - title：1-22字\n  - title：1-24字\n  - title：1-22字\n\n第3页：\n  - title：1-24字\n  - title：1-24字\n  - title：1-22字\n\n第4页：\n  - title：1-24字\n  - content：56-96字\n  - title：1-24字\n  - content：41-81字\n  - title：1-27字\n\n第5页：\n  - title：1-24字\n  - title：1-24字\n  - title：1-22字\n\n第6页：\n  - content：39-79字\n  - title：1-24字\n  - content：35-75字\n  - title：1-24字\n  - title：1-26字\n\n第7页：\n  - content：33-73字\n  - title：1-26字\n  - content：46-86字\n  - title：1-29字\n  - title：1-22字\n  - title：1-22字\n  - title：1-26字\n\n第8页：\n  - content：44-84字\n  - title：1-31字\n  - content：31-71字\n  - title：1-26字\n  - title：1-26字\n\n第9页：\n  - title：1-24字\n  - title：1-24字\n  - title：1-22字\n\n第10页：\n  - title：1-24字\n  - content：45-85字\n  - title：1-24字\n  - content：55-95字\n  - title：1-26字\n\n第11页：\n  - content：47-87字\n  - title：1-24字\n  - content：47-87字\n  - title：1-24字\n  - title：1-26字\n\n第12页：\n  - title：1-27字\n  - title：1-24字\n  - title：1-22字\n\n第13页：\n  - content：50-90字\n  - title：1-26字\n  - content：36-76字\n  - title：1-26字\n  - title：1-24字\n\n第14页：\n  - content：47-87字\n  - title：1-23字\n  - content：40-80字\n  - title：1-23字\n  - title：1-24字\n\n第15页：\n  - title：1-24字\n  - title：1-24字\n  - title：1-22字\n\n第16页：\n  - content：44-84字\n  - content：32-72字\n  - title：1-26字\n  - title：1-25字\n  - title：1-24字\n\n第17页：\n  - title：1-22字\n  - title：1-22字\n  - title：1-26字\n  - content：47-87字\n  - title：1-27字\n  - content：52-92字\n  - title：1-24字\n\n第18页：\n  - title：1-24字\n  - title：1-25字\n  - title：1-23字\n  - title：1-26字\n  - title：1-37字\n  - title：1-24字\n  - title：1-24字\n"}}