"""
对话相关API路由
"""
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Dict, List, Optional
import logging

from ..services.chat_service import ChatService

logger = logging.getLogger(__name__)
router = APIRouter()

# 全局聊天服务实例
chat_service = ChatService()

class OutlineRequest(BaseModel):
    """大纲生成请求"""
    user_input: str
    context: Optional[str] = ""

class RefineRequest(BaseModel):
    """大纲优化请求"""
    current_outline: Dict
    user_feedback: str

class ContentRequest(BaseModel):
    """内容生成请求"""
    outline: Dict

class TemplateSelectRequest(BaseModel):
    """模板选择请求"""
    template_id: str

class TemplateAwareChatRequest(BaseModel):
    """模板感知对话请求"""
    user_input: str
    conversation_history: Optional[List[Dict]] = []
    template_id: Optional[str] = None

class ChatResponse(BaseModel):
    """聊天响应"""
    success: bool
    data: Optional[Dict] = None
    message: str = ""
    error: Optional[str] = None

@router.post("/outline", response_model=ChatResponse)
@router.post("/generate-outline", response_model=ChatResponse)
async def generate_outline(request: OutlineRequest):
    """生成PPT大纲"""
    try:
        logger.info(f"收到大纲生成请求: {request.user_input[:100]}...")
        
        outline = chat_service.generate_outline(
            user_input=request.user_input,
            context=request.context
        )
        
        return ChatResponse(
            success=True,
            data={"outline": outline},
            message="大纲生成成功"
        )
        
    except Exception as e:
        logger.error(f"生成大纲失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"生成大纲失败: {str(e)}"
        )

@router.post("/refine-outline", response_model=ChatResponse)
async def refine_outline(request: RefineRequest):
    """优化PPT大纲"""
    try:
        logger.info(f"收到大纲优化请求: {request.user_feedback[:100]}...")
        
        refined_outline = chat_service.refine_outline(
            current_outline=request.current_outline,
            user_feedback=request.user_feedback
        )
        
        return ChatResponse(
            success=True,
            data=refined_outline,
            message="大纲优化成功"
        )
        
    except Exception as e:
        logger.error(f"优化大纲失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"优化大纲失败: {str(e)}"
        )

@router.post("/generate-content", response_model=ChatResponse)
async def generate_content(request: ContentRequest):
    """生成详细内容"""
    try:
        logger.info("收到内容生成请求")
        
        detailed_outline = chat_service.generate_content(
            outline=request.outline
        )
        
        return ChatResponse(
            success=True,
            data=detailed_outline,
            message="内容生成成功"
        )
        
    except Exception as e:
        logger.error(f"生成内容失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"生成内容失败: {str(e)}"
        )

@router.get("/history", response_model=ChatResponse)
async def get_conversation_history():
    """获取对话历史"""
    try:
        history = chat_service.get_conversation_history()
        
        return ChatResponse(
            success=True,
            data={"history": history},
            message="获取对话历史成功"
        )
        
    except Exception as e:
        logger.error(f"获取对话历史失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取对话历史失败: {str(e)}"
        )

@router.delete("/history", response_model=ChatResponse)
async def clear_conversation_history():
    """清空对话历史"""
    try:
        chat_service.clear_conversation_history()
        
        return ChatResponse(
            success=True,
            message="对话历史已清空"
        )
        
    except Exception as e:
        logger.error(f"清空对话历史失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"清空对话历史失败: {str(e)}"
        )

@router.get("/test-connection", response_model=ChatResponse)
async def test_llm_connection():
    """测试LLM连接"""
    try:
        success, message = chat_service.test_connection()
        
        if success:
            return ChatResponse(
                success=True,
                message=message
            )
        else:
            return ChatResponse(
                success=False,
                error=message
            )
        
    except Exception as e:
        logger.error(f"测试连接失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"测试连接失败: {str(e)}"
        )

@router.get("/templates", response_model=ChatResponse)
async def get_available_templates():
    """获取可用的模板列表"""
    try:
        logger.info("获取可用模板列表")

        templates = chat_service.get_available_templates()

        return ChatResponse(
            success=True,
            data={"templates": templates},
            message="获取模板列表成功"
        )

    except Exception as e:
        logger.error(f"获取模板列表失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取模板列表失败: {str(e)}"
        )

@router.post("/select-template", response_model=ChatResponse)
async def select_template(request: TemplateSelectRequest):
    """选择PPT模板"""
    try:
        logger.info(f"选择模板: {request.template_id}")

        template_info = chat_service.set_template(request.template_id)

        return ChatResponse(
            success=True,
            data=template_info,
            message="模板选择成功"
        )

    except Exception as e:
        logger.error(f"选择模板失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"选择模板失败: {str(e)}"
        )

@router.post("/template-chat", response_model=ChatResponse)
async def template_aware_chat(request: TemplateAwareChatRequest):
    """模板感知的智能对话"""
    try:
        logger.info(f"模板感知对话: {request.user_input[:100]}...")

        # 如果提供了template_id，先设置模板（传递用户输入以便生成正确的提示词）
        if request.template_id:
            logger.info(f"设置模板: {request.template_id}")
            try:
                template_info = chat_service.set_template(request.template_id, request.user_input)
                logger.info(f"模板设置成功: {template_info}")
            except Exception as e:
                logger.error(f"模板设置失败: {e}")
                # 继续执行，但会显示"未选择模板"

        response = chat_service.template_aware_chat(
            user_input=request.user_input,
            conversation_history=request.conversation_history
        )

        return ChatResponse(
            success=True,
            data=response,
            message="对话成功"
        )

    except Exception as e:
        logger.error(f"模板感知对话失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"模板感知对话失败: {str(e)}"
        )
