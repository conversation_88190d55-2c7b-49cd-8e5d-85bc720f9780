#!/usr/bin/env python3
"""
测试PPT预览提取功能
"""

import sys
import os
from pathlib import Path

# 添加当前目录到路径
sys.path.insert(0, '.')

def test_pptx_capabilities():
    """测试python-pptx的功能"""
    print("=== 测试python-pptx功能 ===")
    
    try:
        from pptx import Presentation
        print("✅ python-pptx导入成功")
        
        # 查找模板文件
        template_files = list(Path("templates").glob("*.pptx"))
        if not template_files:
            print("❌ 没有找到PPT模板文件")
            return
        
        template_file = template_files[0]
        print(f"测试文件: {template_file}")
        
        # 加载PPT
        prs = Presentation(str(template_file))
        print(f"✅ PPT加载成功，{len(prs.slides)} 张幻灯片")
        
        if len(prs.slides) > 0:
            slide = prs.slides[0]
            print(f"第一张幻灯片有 {len(slide.shapes)} 个形状")
            
            # 检查每个形状
            for i, shape in enumerate(slide.shapes):
                print(f"\n--- 形状 {i} ---")
                print(f"  类型: {shape.shape_type}")
                print(f"  名称: {getattr(shape, 'name', '无名称')}")
                
                # 检查是否有文本
                if hasattr(shape, 'text_frame'):
                    text = shape.text_frame.text.strip()
                    if text:
                        print(f"  文本: {text[:50]}...")
                
                # 检查是否是图片
                if hasattr(shape, 'image'):
                    print(f"  ✅ 这是一个图片形状")
                    try:
                        image = shape.image
                        print(f"    图片格式: {image.ext}")
                        print(f"    图片大小: {len(image.blob)} bytes")
                    except Exception as e:
                        print(f"    图片信息获取失败: {e}")
                
                # 检查填充
                if hasattr(shape, 'fill'):
                    try:
                        fill = shape.fill
                        print(f"  填充类型: {fill.type}")
                        if hasattr(fill, 'fore_color'):
                            print(f"  前景色: {fill.fore_color}")
                    except Exception as e:
                        print(f"  填充信息获取失败: {e}")
        
        # 检查母版
        print(f"\n=== 母版信息 ===")
        slide_master = prs.slide_master
        print(f"母版布局数量: {len(slide_master.slide_layouts)}")
        
        # 检查是否有背景
        try:
            background = slide_master.background
            print(f"母版背景: {background}")
        except Exception as e:
            print(f"获取母版背景失败: {e}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_alternative_methods():
    """测试其他可能的预览生成方法"""
    print("\n=== 测试其他预览生成方法 ===")
    
    # 方法1: 检查是否可以使用win32com (Windows COM)
    try:
        import win32com.client
        print("✅ win32com可用 - 可以尝试使用PowerPoint COM接口")
        
        # 注意：这需要安装了PowerPoint
        try:
            ppt_app = win32com.client.Dispatch("PowerPoint.Application")
            print("✅ PowerPoint应用程序可用")
            ppt_app.Quit()
        except Exception as e:
            print(f"❌ PowerPoint应用程序不可用: {e}")
            
    except ImportError:
        print("❌ win32com不可用")
    
    # 方法2: 检查是否可以使用aspose
    try:
        # 这个库通常是付费的，但有试用版
        print("检查Aspose.Slides是否可用...")
        # import aspose.slides as slides
        # print("✅ Aspose.Slides可用")
    except ImportError:
        print("❌ Aspose.Slides不可用")
    
    # 方法3: 检查是否可以使用LibreOffice
    print("检查LibreOffice是否可用...")
    libreoffice_paths = [
        "C:/Program Files/LibreOffice/program/soffice.exe",
        "C:/Program Files (x86)/LibreOffice/program/soffice.exe",
    ]
    
    for path in libreoffice_paths:
        if os.path.exists(path):
            print(f"✅ 找到LibreOffice: {path}")
            break
    else:
        print("❌ LibreOffice不可用")

def test_image_extraction():
    """测试从PPT中提取图片"""
    print("\n=== 测试图片提取 ===")
    
    try:
        from pptx import Presentation
        from pptx.enum.shapes import MSO_SHAPE_TYPE
        
        template_files = list(Path("templates").glob("*.pptx"))
        if not template_files:
            return
        
        template_file = template_files[0]
        prs = Presentation(str(template_file))
        
        if len(prs.slides) > 0:
            slide = prs.slides[0]
            
            # 查找图片
            images_found = 0
            for shape in slide.shapes:
                if shape.shape_type == MSO_SHAPE_TYPE.PICTURE:
                    images_found += 1
                    print(f"✅ 找到图片 {images_found}")
                    
                    try:
                        image = shape.image
                        print(f"  格式: {image.ext}")
                        print(f"  大小: {len(image.blob)} bytes")
                        
                        # 尝试保存图片
                        image_path = f"extracted_image_{images_found}.{image.ext}"
                        with open(image_path, 'wb') as f:
                            f.write(image.blob)
                        print(f"  ✅ 图片已保存: {image_path}")
                        
                    except Exception as e:
                        print(f"  ❌ 图片提取失败: {e}")
            
            if images_found == 0:
                print("❌ 没有找到图片")
                
    except Exception as e:
        print(f"❌ 图片提取测试失败: {e}")

if __name__ == '__main__':
    test_pptx_capabilities()
    test_alternative_methods()
    test_image_extraction()
    
    print("\n=== 结论 ===")
    print("python-pptx主要用于创建和修改PPT，不是为预览生成设计的")
    print("要生成真实的预览图片，可能需要：")
    print("1. PowerPoint COM接口 (需要安装PowerPoint)")
    print("2. LibreOffice命令行转换")
    print("3. Aspose.Slides (商业库)")
    print("4. 或者接受简化的预览图片")
