#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试重新解析模板API（集成新方案）
"""

import requests
import json
from pathlib import Path

def test_reanalyze_api():
    """测试重新解析模板API"""
    try:
        print('🔧 测试重新解析模板API（集成新方案）')
        print('=' * 60)
        
        # API端点
        template_id = 'template_20250708_221451_adc64662'
        url = f'http://localhost:9527/api/templates/{template_id}/analyze'
        
        print(f'📡 调用API: {url}')
        print(f'📋 模板ID: {template_id}')
        print()
        
        # 发送POST请求
        print('🚀 发送重新解析请求...')
        response = requests.post(url, timeout=60)
        
        print(f'📊 响应状态: {response.status_code}')
        
        if response.status_code == 200:
            result = response.json()
            
            print('✅ API调用成功!')
            print(f'成功状态: {result.get("success")}')
            print(f'消息: {result.get("message")}')
            print()
            
            # 检查是否包含新方案的结构化数据
            if "new_structure" in result:
                print('🎉 包含新方案的结构化数据!')
                
                new_structure = result["new_structure"]
                total_pages = result.get("total_pages", 0)
                total_placeholders = result.get("total_placeholders", 0)
                structure_file = result.get("structure_file", "")
                
                print(f'📄 总页数: {total_pages}')
                print(f'🏷️  总占位符: {total_placeholders}')
                print(f'📁 结构文件: {structure_file}')
                print()
                
                # 显示前2页的结构
                print('📖 前2页结构预览:')
                print('-' * 40)
                
                for i, (page_key, page_data) in enumerate(list(new_structure.items())[:2]):
                    print(f'{page_key}:')
                    for element_key, element_data in page_data.items():
                        print(f'  {element_key}: {element_data["type"]}, {element_data["word_limit"]}字, {element_data["PlaceholderId"]}')
                    print()
                
                # 检查结构文件是否存在
                if structure_file and Path(structure_file).exists():
                    print(f'✅ 结构化JSON文件已保存: {structure_file}')
                    
                    # 读取并验证文件内容
                    with open(structure_file, 'r', encoding='utf-8') as f:
                        saved_structure = json.load(f)
                    
                    if saved_structure == new_structure:
                        print('✅ 文件内容与API返回一致')
                    else:
                        print('⚠️  文件内容与API返回不一致')
                else:
                    print(f'❌ 结构化JSON文件不存在: {structure_file}')
                
                return True
                
            else:
                print('⚠️  响应中没有新方案的结构化数据')
                print('可能只执行了旧方案的解析')
                
                # 显示旧方案结果
                if "analysis" in result:
                    analysis = result["analysis"]
                    print(f'旧方案分析结果: {len(analysis)} 个字段')
                
                return False
        else:
            print(f'❌ API调用失败: {response.status_code}')
            print(f'响应内容: {response.text}')
            return False
            
    except Exception as e:
        print(f'❌ 测试异常: {e}')
        import traceback
        traceback.print_exc()
        return False

def check_analysis_files():
    """检查生成的分析文件"""
    print('\n📁 检查分析文件:')
    print('-' * 40)
    
    analysis_dir = Path("template_analysis")
    
    if analysis_dir.exists():
        print(f'✅ 分析目录存在: {analysis_dir}')
        
        # 列出所有JSON文件
        json_files = list(analysis_dir.glob("*.json"))
        print(f'📄 JSON文件数量: {len(json_files)}')
        
        for json_file in json_files:
            print(f'  - {json_file.name} ({json_file.stat().st_size} bytes)')
            
            # 验证JSON格式
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                print(f'    ✅ JSON格式正确，包含 {len(data)} 页')
            except Exception as e:
                print(f'    ❌ JSON格式错误: {e}')
    else:
        print(f'❌ 分析目录不存在: {analysis_dir}')

if __name__ == "__main__":
    print('🚀 开始测试重新解析模板API')
    
    success = test_reanalyze_api()
    check_analysis_files()
    
    print(f'\n🎯 测试结果: {"✅ 成功" if success else "❌ 失败"}')
    
    if success:
        print('\n🎉 重新解析模板功能已集成新方案!')
        print('现在点击"重新解析模板"按钮会：')
        print('1. 执行旧的md2pptx分析（保持兼容性）')
        print('2. 执行新的基于成品PPT的解析')
        print('3. 生成结构化JSON文件')
        print('4. 返回合并的结果')
    else:
        print('\n❌ 重新解析模板功能需要进一步调试')
