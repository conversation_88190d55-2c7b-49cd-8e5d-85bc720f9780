# -*- coding: utf-8 -*-
"""
PPT生成系统 - 大模型连接管理模块
负责连接管理、重试机制等
专为PPT生成优化，移除了SSH隧道等复杂功能
"""

import logging
import time
from openai import OpenAI

logger = logging.getLogger(__name__)

class ConnectionManager:
    """大模型连接管理器 - 专为PPT生成优化"""

    def __init__(self, config):
        """
        初始化连接管理器

        Args:
            config: 大模型配置
        """
        self.config = config
        self.api_key = config.get('api_key', '')
        self.model = config.get('model', 'deepseek-v3-0324')
        self.api_base = config.get('api_base', 'http://192.168.78.35/gateway/ai-service/v1')
        self.timeout = config.get('timeout', 180)

        # 验证配置
        self._validate_config()

        # 初始化OpenAI客户端
        self.client = self._create_openai_client()

        logger.info(f"大模型连接管理器初始化成功，API基础URL: {self.api_base}")

    def _validate_config(self):
        """验证配置参数"""
        if not self.api_key:
            raise ValueError("API密钥未设置")

        if not self.model:
            raise ValueError("模型名称未设置")

        if not self.api_base:
            raise ValueError("API基础URL未设置")

        # 验证超时时间
        try:
            self.timeout = int(self.timeout)
            if self.timeout <= 0:
                raise ValueError("超时时间必须大于0")
        except (ValueError, TypeError):
            raise ValueError("超时时间必须是有效的正整数")

        logger.debug("配置验证通过")

    def _create_openai_client(self):
        """创建OpenAI客户端"""
        try:
            client = OpenAI(
                api_key=self.api_key,
                base_url=self.api_base,
                timeout=self.timeout
            )
            logger.debug("OpenAI客户端创建成功")
            return client
        except Exception as e:
            logger.error(f"创建OpenAI客户端失败: {str(e)}")
            raise

    def send_request(self, messages, max_tokens=None, temperature=None, stream=False):
        """
        发送请求到大模型

        Args:
            messages: 消息列表
            max_tokens: 最大token数
            temperature: 温度参数
            stream: 是否流式输出

        Returns:
            响应结果
        """
        if not messages:
            raise ValueError("消息列表不能为空")

        # 设置默认参数
        if max_tokens is None:
            max_tokens = self.config.get('max_tokens', 4000)
        if temperature is None:
            temperature = self.config.get('temperature', 0.1)

        try:
            logger.debug(f"发送请求到模型: {self.model}")

            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                stream=stream
            )

            if stream:
                return response
            else:
                return response.choices[0].message.content

        except Exception as e:
            logger.error(f"发送请求失败: {str(e)}")
            raise

    def send_request_with_retry(self, messages, max_retries=3, retry_delay=1, **kwargs):
        """
        带重试机制的请求发送

        Args:
            messages: 消息列表
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）
            **kwargs: 其他参数

        Returns:
            响应结果
        """
        last_error = None

        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    logger.info(f"第 {attempt} 次重试...")
                    time.sleep(retry_delay * attempt)  # 递增延迟

                return self.send_request(messages, **kwargs)

            except Exception as e:
                last_error = e
                logger.warning(f"请求失败 (尝试 {attempt + 1}/{max_retries + 1}): {str(e)}")

                if attempt == max_retries:
                    logger.error(f"所有重试都失败了，最后错误: {str(last_error)}")
                    break

        raise last_error

    def test_connection(self):
        """
        测试连接是否正常

        Returns:
            tuple: (是否成功, 错误信息)
        """
        try:
            test_messages = [
                {"role": "user", "content": "你好，请回复'连接测试成功'"}
            ]

            response = self.send_request(test_messages, max_tokens=50)

            if response and "连接测试成功" in response:
                logger.info("连接测试成功")
                return True, "连接测试成功"
            else:
                logger.warning(f"连接测试响应异常: {response}")
                return True, "连接正常但响应异常"

        except Exception as e:
            logger.error(f"连接测试失败: {str(e)}")
            return False, f"连接测试失败: {str(e)}"

    def get_model_info(self):
        """
        获取模型信息

        Returns:
            dict: 模型信息
        """
        return {
            "model": self.model,
            "api_base": self.api_base,
            "timeout": self.timeout,
            "max_tokens": self.config.get('max_tokens', 4000),
            "temperature": self.config.get('temperature', 0.1)
        }

    def close(self):
        """关闭连接"""
        # 清理客户端
        self.client = None
        logger.info("大模型连接已关闭")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()

    def __del__(self):
        """析构函数"""
        try:
            self.close()
        except:
            pass  # 忽略析构时的错误